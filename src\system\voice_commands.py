"""
Voice Commands Processor for Gideon AI Assistant
"""

import re
import time
from datetime import datetime
from typing import Dict, List, Callable, Any, Optional

from src.utils.logger import <PERSON><PERSON><PERSON><PERSON>
from src.utils.config import Config
from src.utils.i18n import get_i18n


class VoiceCommandProcessor:
    """Voice command processor for <PERSON>"""
    
    def __init__(self, gideon_core=None):
        self.logger = GideonLogger("VoiceCommands")
        self.config = Config()
        self.i18n = get_i18n()
        self.gideon_core = gideon_core
        
        # Command registry
        self.commands = {}
        self.command_patterns = {}
        
        # Initialize built-in commands
        self._register_builtin_commands()
    
    def _register_builtin_commands(self):
        """Register built-in voice commands"""
        
        # System commands
        self.register_command(
            ["screenshot", "take screenshot", "capture screen"],
            self._cmd_screenshot,
            "Take a screenshot"
        )
        
        self.register_command(
            ["time", "what time", "current time"],
            self._cmd_time,
            "Get current time"
        )
        
        self.register_command(
            ["date", "what date", "current date", "today"],
            self._cmd_date,
            "Get current date"
        )
        
        # Gideon control commands
        self.register_command(
            ["stop listening", "stop", "quiet"],
            self._cmd_stop_listening,
            "Stop listening for voice input"
        )
        
        self.register_command(
            ["start listening", "listen"],
            self._cmd_start_listening,
            "Start listening for voice input"
        )
        
        self.register_command(
            ["mute", "be quiet", "silence"],
            self._cmd_mute,
            "Mute Gideon's voice output"
        )
        
        self.register_command(
            ["unmute", "speak", "talk"],
            self._cmd_unmute,
            "Unmute Gideon's voice output"
        )
        
        # Information commands
        self.register_command(
            ["help", "what can you do", "commands"],
            self._cmd_help,
            "Show available commands"
        )
        
        self.register_command(
            ["status", "how are you", "system status"],
            self._cmd_status,
            "Get Gideon's status"
        )
        
        # Memory commands
        self.register_command(
            ["remember", "save this", "memorize"],
            self._cmd_remember,
            "Remember information"
        )
        
        self.register_command(
            ["forget", "clear memory"],
            self._cmd_forget,
            "Clear conversation memory"
        )
        
        # Settings commands
        self.register_command(
            ["settings", "preferences", "options"],
            self._cmd_settings,
            "Open settings"
        )
        
        self.register_command(
            ["change language", "switch language"],
            self._cmd_change_language,
            "Change language"
        )
        
        # Arabic commands - always register for bilingual support
        self.register_command(
            ["لقطة شاشة", "التقط الشاشة", "صورة الشاشة"],
            self._cmd_screenshot,
            "التقاط لقطة شاشة"
        )

        self.register_command(
            ["الوقت", "كم الساعة", "الساعة الآن", "الوقت الحالي"],
            self._cmd_time,
            "معرفة الوقت الحالي"
        )

        self.register_command(
            ["التاريخ", "ما التاريخ", "التاريخ اليوم", "اليوم"],
            self._cmd_date,
            "معرفة التاريخ الحالي"
        )

        self.register_command(
            ["توقف عن الاستماع", "توقف", "هدوء", "اسكتي"],
            self._cmd_stop_listening,
            "إيقاف الاستماع للأوامر الصوتية"
        )

        self.register_command(
            ["ابدأ الاستماع", "استمع", "اسمعي"],
            self._cmd_start_listening,
            "بدء الاستماع للأوامر الصوتية"
        )

        self.register_command(
            ["مساعدة", "ماذا تستطيع أن تفعل", "الأوامر", "ساعديني"],
            self._cmd_help,
            "إظهار الأوامر المتاحة"
        )

        self.register_command(
            ["شكرا", "شكرا لك", "شكرا جزيلا"],
            self._cmd_thanks_arabic,
            "شكر جيديون"
        )

        self.register_command(
            ["مرحبا", "السلام عليكم", "أهلا"],
            self._cmd_hello_arabic,
            "تحية جيديون"
        )

        self.register_command(
            ["كيف حالك", "كيفك", "شلونك"],
            self._cmd_how_are_you_arabic,
            "السؤال عن حال جيديون"
        )

        self.register_command(
            ["غير اللغة", "تغيير اللغة", "بدل اللغة"],
            self._cmd_change_language,
            "تغيير اللغة"
        )

        # Terminal commands - English
        self.register_command(
            ["open terminal", "show terminal", "terminal access"],
            self._cmd_open_terminal,
            "Open terminal interface"
        )

        self.register_command(
            ["close terminal", "hide terminal"],
            self._cmd_close_terminal,
            "Close terminal interface"
        )

        self.register_command(
            ["execute command", "run command", "terminal command"],
            self._cmd_execute_terminal_command,
            "Execute terminal command"
        )

        self.register_command(
            ["clear terminal", "clear console"],
            self._cmd_clear_terminal,
            "Clear terminal output"
        )

        self.register_command(
            ["terminal help", "terminal commands"],
            self._cmd_terminal_help,
            "Show terminal help"
        )

        self.register_command(
            ["list files", "show files", "directory listing"],
            self._cmd_list_files,
            "List files in current directory"
        )

        self.register_command(
            ["current directory", "where am i", "show path"],
            self._cmd_current_directory,
            "Show current directory"
        )

        self.register_command(
            ["change directory", "go to folder", "navigate to"],
            self._cmd_change_directory,
            "Change directory"
        )

        # Terminal commands - Arabic
        self.register_command(
            ["افتح الطرفية", "اظهر الطرفية", "الوصول للطرفية"],
            self._cmd_open_terminal,
            "فتح واجهة الطرفية"
        )

        self.register_command(
            ["اغلق الطرفية", "اخف الطرفية"],
            self._cmd_close_terminal,
            "إغلاق واجهة الطرفية"
        )

        self.register_command(
            ["نفذ الأمر", "شغل الأمر", "أمر الطرفية"],
            self._cmd_execute_terminal_command,
            "تنفيذ أمر الطرفية"
        )

        self.register_command(
            ["امسح الطرفية", "نظف الطرفية"],
            self._cmd_clear_terminal,
            "مسح مخرجات الطرفية"
        )

        self.register_command(
            ["مساعدة الطرفية", "أوامر الطرفية"],
            self._cmd_terminal_help,
            "إظهار مساعدة الطرفية"
        )

        self.register_command(
            ["اعرض الملفات", "قائمة الملفات", "محتويات المجلد"],
            self._cmd_list_files,
            "عرض الملفات في المجلد الحالي"
        )

        self.register_command(
            ["المجلد الحالي", "أين أنا", "اظهر المسار"],
            self._cmd_current_directory,
            "إظهار المجلد الحالي"
        )

        self.register_command(
            ["غير المجلد", "اذهب للمجلد", "انتقل إلى"],
            self._cmd_change_directory,
            "تغيير المجلد"
        )

        # AI Model switching commands - English
        self.register_command(
            ["switch to qwen", "use qwen model", "load qwen", "switch to qwen3"],
            self._cmd_switch_to_qwen,
            "Switch to Qwen3:235b model"
        )

        self.register_command(
            ["switch to gemma", "use gemma model", "load gemma", "switch to gemma2"],
            self._cmd_switch_to_gemma,
            "Switch to Gemma2:27b model"
        )

        self.register_command(
            ["switch to gemma3", "use gemma3 model", "load gemma3", "switch to gemma 3"],
            self._cmd_switch_to_gemma3,
            "Switch to Gemma3:27b model"
        )

        self.register_command(
            ["switch to dolphin", "use dolphin model", "load dolphin"],
            self._cmd_switch_to_dolphin,
            "Switch to Dolphin-Llama3:70b model"
        )

        # Arabic model switching commands
        self.register_command(
            ["استخدم نموذج كوين", "غير إلى كوين", "حمل كوين"],
            self._cmd_switch_to_qwen,
            "التبديل إلى نموذج Qwen3:235b"
        )

        self.register_command(
            ["استخدم نموذج جيما", "غير إلى جيما", "حمل جيما"],
            self._cmd_switch_to_gemma3,
            "التبديل إلى نموذج Gemma3:27b"
        )

        self.register_command(
            ["استخدم نموذج دولفين", "غير إلى دولفين", "حمل دولفين"],
            self._cmd_switch_to_dolphin,
            "التبديل إلى نموذج Dolphin-Llama3:70b"
        )

        # AI Model switching commands - English
        self.register_command(
            ["switch to qwen", "use qwen model", "load qwen", "switch to qwen3"],
            self._cmd_switch_to_qwen,
            "Switch to Qwen3:235b model"
        )

        self.register_command(
            ["switch to gemma", "use gemma model", "load gemma", "switch to gemma2"],
            self._cmd_switch_to_gemma,
            "Switch to Gemma2:27b model"
        )

        self.register_command(
            ["switch to dolphin", "use dolphin model", "load dolphin"],
            self._cmd_switch_to_dolphin,
            "Switch to Dolphin-Llama3:70b model"
        )

        # AI Model switching commands - Arabic
        self.register_command(
            ["استخدم نموذج كوين", "غير إلى كوين", "حمل كوين"],
            self._cmd_switch_to_qwen,
            "التبديل إلى نموذج Qwen3:235b"
        )

        self.register_command(
            ["استخدم نموذج جيما", "غير إلى جيما", "حمل جيما"],
            self._cmd_switch_to_gemma,
            "التبديل إلى نموذج Gemma2:27b"
        )

        self.register_command(
            ["استخدم نموذج دولفين", "غير إلى دولفين", "حمل دولفين"],
            self._cmd_switch_to_dolphin,
            "التبديل إلى نموذج Dolphin-Llama3:70b"
        )
    
    def register_command(self, triggers: List[str], handler: Callable, description: str = ""):
        """Register a voice command"""
        for trigger in triggers:
            trigger_lower = trigger.lower().strip()
            self.commands[trigger_lower] = {
                'handler': handler,
                'description': description,
                'triggers': triggers
            }
        
        self.logger.debug(f"Registered command: {triggers[0]} - {description}")
    
    def register_pattern_command(self, pattern: str, handler: Callable, description: str = ""):
        """Register a pattern-based voice command"""
        compiled_pattern = re.compile(pattern, re.IGNORECASE)
        self.command_patterns[pattern] = {
            'pattern': compiled_pattern,
            'handler': handler,
            'description': description
        }
        
        self.logger.debug(f"Registered pattern command: {pattern} - {description}")
    
    def is_command(self, text: str) -> bool:
        """Check if text is a voice command"""
        text_lower = text.lower().strip()

        # Check exact matches first
        if text_lower in self.commands:
            return True

        # Check pattern matches
        for pattern_info in self.command_patterns.values():
            if pattern_info['pattern'].search(text):
                return True

        # Check if text starts with a command trigger (more restrictive)
        for trigger in self.commands.keys():
            if text_lower.startswith(trigger) or trigger.startswith(text_lower):
                # Only match if the similarity is high enough
                if len(text_lower) >= 3 and len(trigger) >= 3:
                    similarity = min(len(text_lower), len(trigger)) / max(len(text_lower), len(trigger))
                    if similarity > 0.7:  # 70% similarity threshold
                        return True

        # Check for specific command phrases in longer text
        for trigger in self.commands.keys():
            if len(trigger.split()) > 1:  # Multi-word commands
                if trigger in text_lower:
                    return True

        return False
    
    def execute_command(self, text: str) -> str:
        """Execute voice command and return response"""
        try:
            text_lower = text.lower().strip()
            
            # Try exact match first
            if text_lower in self.commands:
                command_info = self.commands[text_lower]
                return command_info['handler'](text)
            
            # Try pattern matches
            for pattern_info in self.command_patterns.values():
                match = pattern_info['pattern'].search(text)
                if match:
                    return pattern_info['handler'](text, match)
            
            # Try partial matches
            best_match = None
            best_score = 0
            
            for trigger, command_info in self.commands.items():
                if trigger in text_lower:
                    score = len(trigger) / len(text_lower)
                    if score > best_score:
                        best_score = score
                        best_match = command_info
                elif text_lower in trigger:
                    score = len(text_lower) / len(trigger)
                    if score > best_score:
                        best_score = score
                        best_match = command_info
            
            if best_match and best_score > 0.6:  # Higher similarity threshold
                return best_match['handler'](text)

            # If we reach here, it's likely not a command at all
            # Return None to indicate this should go to AI instead
            return None
            
        except Exception as e:
            self.logger.error(f"Error executing command '{text}': {e}")
            return f"Error executing command: {e}"
    
    def get_available_commands(self) -> List[Dict[str, Any]]:
        """Get list of available commands"""
        commands_list = []
        seen_handlers = set()
        
        for trigger, command_info in self.commands.items():
            handler = command_info['handler']
            if handler not in seen_handlers:
                commands_list.append({
                    'triggers': command_info['triggers'],
                    'description': command_info['description'],
                    'primary_trigger': command_info['triggers'][0]
                })
                seen_handlers.add(handler)
        
        return commands_list
    
    # Built-in command handlers
    def _cmd_screenshot(self, text: str) -> str:
        """Take screenshot command"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'screen_capture'):
                filepath = self.gideon_core.take_screenshot()
                if filepath:
                    return f"Screenshot saved to {filepath}"
                else:
                    return "Failed to take screenshot"
            else:
                return "Screenshot functionality not available"
        except Exception as e:
            return f"Error taking screenshot: {e}"
    
    def _cmd_time(self, text: str) -> str:
        """Get current time command"""
        current_time = datetime.now().strftime("%I:%M %p")
        return f"The current time is {current_time}"
    
    def _cmd_date(self, text: str) -> str:
        """Get current date command"""
        current_date = datetime.now().strftime("%A, %B %d, %Y")
        return f"Today is {current_date}"
    
    def _cmd_stop_listening(self, text: str) -> str:
        """Stop listening command"""
        if self.gideon_core:
            self.gideon_core.is_listening = False
        return "I've stopped listening for voice commands"
    
    def _cmd_start_listening(self, text: str) -> str:
        """Start listening command"""
        if self.gideon_core:
            self.gideon_core.is_listening = True
        return "I'm now listening for voice commands"
    
    def _cmd_mute(self, text: str) -> str:
        """Mute command"""
        # This would typically be handled by the TTS engine
        return "Voice output muted"
    
    def _cmd_unmute(self, text: str) -> str:
        """Unmute command"""
        # This would typically be handled by the TTS engine
        return "Voice output unmuted"
    
    def _cmd_help(self, text: str) -> str:
        """Help command"""
        commands = self.get_available_commands()
        
        if self.i18n.current_language == "ar":
            help_text = "الأوامر المتاحة:\n"
        else:
            help_text = "Available commands:\n"
        
        for cmd in commands[:10]:  # Limit to first 10 commands
            help_text += f"• {cmd['primary_trigger']}: {cmd['description']}\n"
        
        return help_text
    
    def _cmd_status(self, text: str) -> str:
        """Status command"""
        if self.gideon_core:
            status = self.gideon_core.get_status()
            return f"System status: {'Ready' if status.get('initialized') else 'Not ready'}, " \
                   f"Listening: {'Yes' if status.get('listening') else 'No'}, " \
                   f"Speaking: {'Yes' if status.get('speaking') else 'No'}"
        else:
            return "Gideon core not available"
    
    def _cmd_remember(self, text: str) -> str:
        """Remember command"""
        # Extract what to remember from the text
        remember_text = text.lower().replace("remember", "").replace("save this", "").replace("memorize", "").strip()
        
        if remember_text:
            if self.gideon_core and hasattr(self.gideon_core, 'memory_system'):
                self.gideon_core.memory_system.set_user_preference("remembered_info", remember_text)
                return f"I'll remember: {remember_text}"
            else:
                return "Memory system not available"
        else:
            return "What would you like me to remember?"
    
    def _cmd_forget(self, text: str) -> str:
        """Forget command"""
        if self.gideon_core and hasattr(self.gideon_core, 'memory_system'):
            # Clear recent conversation history
            self.gideon_core.memory_system.recent_conversations.clear()
            return "I've cleared my recent conversation memory"
        else:
            return "Memory system not available"
    
    def _cmd_settings(self, text: str) -> str:
        """Settings command"""
        return "Settings functionality would open here"
    
    def _cmd_change_language(self, text: str) -> str:
        """Change language command"""
        current_lang = self.i18n.current_language
        if current_lang == "en":
            self.i18n.set_language("ar")
            self.config.set("app.language", "ar")
            return "تم تغيير اللغة إلى العربية. مرحباً بك!"
        else:
            self.i18n.set_language("en")
            self.config.set("app.language", "en")
            return "Language changed to English. Welcome!"

    def _cmd_thanks_arabic(self, text: str) -> str:
        """Arabic thanks command"""
        responses = [
            "عفواً! أهلاً وسهلاً",
            "لا شكر على واجب",
            "تسلم! دائماً في الخدمة",
            "العفو! كيف يمكنني مساعدتك أكثر؟"
        ]
        import random
        return random.choice(responses)

    def _cmd_hello_arabic(self, text: str) -> str:
        """Arabic hello command"""
        if "السلام عليكم" in text:
            return "وعليكم السلام ورحمة الله وبركاته! كيف يمكنني مساعدتك؟"
        else:
            responses = [
                "مرحباً! أهلاً وسهلاً بك",
                "أهلاً! كيف حالك اليوم؟",
                "مرحباً بك! كيف يمكنني مساعدتك؟",
                "أهلاً وسهلاً! ماذا تريد أن نفعل اليوم؟"
            ]
            import random
            return random.choice(responses)

    def _cmd_how_are_you_arabic(self, text: str) -> str:
        """Arabic how are you command"""
        responses = [
            "الحمد لله، أنا بخير! وأنت كيف حالك؟",
            "بخير والحمد لله! كيف يمكنني مساعدتك؟",
            "تمام! شكراً لسؤالك. كيف حالك أنت؟",
            "الحمد لله على كل حال! ماذا تحتاج مني؟"
        ]
        import random
        return random.choice(responses)

    # Terminal command handlers
    def _cmd_open_terminal(self, text: str) -> str:
        """Open terminal interface command"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'terminal_interface'):
                self.gideon_core.terminal_interface.show()
                return "Terminal interface opened"
            else:
                return "Terminal interface not available"
        except Exception as e:
            return f"Error opening terminal: {e}"

    def _cmd_close_terminal(self, text: str) -> str:
        """Close terminal interface command"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'terminal_interface'):
                self.gideon_core.terminal_interface.hide()
                return "Terminal interface closed"
            else:
                return "Terminal interface not available"
        except Exception as e:
            return f"Error closing terminal: {e}"

    def _cmd_execute_terminal_command(self, text: str) -> str:
        """Execute terminal command"""
        try:
            # Extract command from voice input
            command_text = text.lower()

            # Remove trigger phrases
            triggers = ["execute command", "run command", "terminal command",
                       "نفذ الأمر", "شغل الأمر", "أمر الطرفية"]

            for trigger in triggers:
                if trigger in command_text:
                    command_text = command_text.replace(trigger, "").strip()
                    break

            if not command_text:
                return "Please specify a command to execute"

            if self.gideon_core and hasattr(self.gideon_core, 'terminal_interface'):
                self.gideon_core.terminal_interface.execute_command_from_voice(command_text)
                return f"Executing command: {command_text}"
            else:
                return "Terminal interface not available"

        except Exception as e:
            return f"Error executing terminal command: {e}"

    def _cmd_clear_terminal(self, text: str) -> str:
        """Clear terminal output command"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'terminal_interface'):
                terminal = self.gideon_core.terminal_interface
                terminal._clear_output()
                return "Terminal output cleared"
            else:
                return "Terminal interface not available"
        except Exception as e:
            return f"Error clearing terminal: {e}"

    def _cmd_terminal_help(self, text: str) -> str:
        """Show terminal help command"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'terminal_interface'):
                terminal = self.gideon_core.terminal_interface
                terminal._show_help()
                return "Terminal help displayed"
            else:
                return "Terminal interface not available"
        except Exception as e:
            return f"Error showing terminal help: {e}"

    def _cmd_list_files(self, text: str) -> str:
        """List files in current directory command"""
        try:
            import platform

            if platform.system().lower() == 'windows':
                command = "dir"
            else:
                command = "ls -la"

            if self.gideon_core and hasattr(self.gideon_core, 'terminal_interface'):
                self.gideon_core.terminal_interface.execute_command_from_voice(command)
                return f"Listing files with: {command}"
            else:
                return "Terminal interface not available"

        except Exception as e:
            return f"Error listing files: {e}"

    def _cmd_current_directory(self, text: str) -> str:
        """Show current directory command"""
        try:
            import platform

            if platform.system().lower() == 'windows':
                command = "cd"
            else:
                command = "pwd"

            if self.gideon_core and hasattr(self.gideon_core, 'terminal_interface'):
                self.gideon_core.terminal_interface.execute_command_from_voice(command)
                return f"Showing current directory with: {command}"
            else:
                return "Terminal interface not available"

        except Exception as e:
            return f"Error showing current directory: {e}"

    def _cmd_change_directory(self, text: str) -> str:
        """Change directory command"""
        try:
            # Extract path from voice input
            command_text = text.lower()

            # Remove trigger phrases
            triggers = ["change directory", "go to folder", "navigate to",
                       "غير المجلد", "اذهب للمجلد", "انتقل إلى"]

            path = ""
            for trigger in triggers:
                if trigger in command_text:
                    path = command_text.replace(trigger, "").strip()
                    break

            if not path:
                return "Please specify a directory path"

            # Handle common directory names
            if path in ["home", "البيت"]:
                path = "~"
            elif path in ["desktop", "المكتب"]:
                path = "~/Desktop"
            elif path in ["documents", "المستندات"]:
                path = "~/Documents"
            elif path in ["downloads", "التحميلات"]:
                path = "~/Downloads"

            command = f"cd {path}"

            if self.gideon_core and hasattr(self.gideon_core, 'terminal_interface'):
                self.gideon_core.terminal_interface.execute_command_from_voice(command)
                return f"Changing directory to: {path}"
            else:
                return "Terminal interface not available"

        except Exception as e:
            return f"Error changing directory: {e}"

    # AI Model switching command handlers
    def _cmd_switch_to_qwen(self, text: str) -> str:
        """Switch to Qwen3:235b model command"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'ai_engine'):
                success = self.gideon_core.ai_engine.load_model("ollama", "qwen3:235b")
                if success:
                    return "Switched to Qwen3:235b model - Ultra-large MoE model with 22B active parameters ready"
                else:
                    return "Failed to switch to Qwen3:235b model. Please ensure the model is downloaded."
            else:
                return "AI engine not available"
        except Exception as e:
            return f"Error switching to Qwen model: {e}"

    def _cmd_switch_to_gemma(self, text: str) -> str:
        """Switch to Gemma2:27b model command"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'ai_engine'):
                success = self.gideon_core.ai_engine.load_model("ollama", "gemma2:27b")
                if success:
                    return "Switched to Gemma2:27b model - Google's advanced reasoning model ready"
                else:
                    return "Failed to switch to Gemma2:27b model. Please ensure the model is downloaded."
            else:
                return "AI engine not available"
        except Exception as e:
            return f"Error switching to Gemma model: {e}"

    def _cmd_switch_to_gemma3(self, text: str) -> str:
        """Switch to Gemma3:27b model command"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'ai_engine'):
                success = self.gideon_core.ai_engine.load_model("ollama", "gemma3:27b")
                if success:
                    return "Switched to Gemma3:27b model - Google's latest advanced reasoning model ready"
                else:
                    return "Failed to switch to Gemma3:27b model. Please ensure the model is downloaded."
            else:
                return "AI engine not available"
        except Exception as e:
            return f"Error switching to Gemma3 model: {e}"

    def _cmd_switch_to_gemma3(self, text: str) -> str:
        """Switch to Gemma3:27b model command"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'ai_engine'):
                success = self.gideon_core.ai_engine.load_model("ollama", "gemma3:27b")
                if success:
                    return "Switched to Gemma3:27b model - Google's latest advanced reasoning model ready"
                else:
                    return "Failed to switch to Gemma3:27b model. Please ensure the model is downloaded."
            else:
                return "AI engine not available"
        except Exception as e:
            return f"Error switching to Gemma3 model: {e}"

    def _cmd_switch_to_dolphin(self, text: str) -> str:
        """Switch to Dolphin-Llama3:70b model command"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'ai_engine'):
                success = self.gideon_core.ai_engine.load_model("ollama", "dolphin-llama3:70b")
                if success:
                    return "Switched to Dolphin-Llama3:70b model - High-performance instruction-following model ready"
                else:
                    return "Failed to switch to Dolphin-Llama3:70b model. Please ensure the model is downloaded."
            else:
                return "AI engine not available"
        except Exception as e:
            return f"Error switching to Dolphin model: {e}"
