2025-06-15 00:40:09,141 - SimpleInterface - ERROR - AI response error: 'NoneType' object has no attribute 'generate_response'
2025-06-15 00:41:07,433 - <PERSON><PERSON><PERSON> - INFO - Initializing Gideon Core System...
2025-06-15 00:41:07,437 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-15 00:41:07,438 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-15 00:41:07,438 - MemorySystem - INFO - Memory system initialized successfully
2025-06-15 00:41:07,439 - ModelManager - INFO - No existing models config found
2025-06-15 00:41:07,440 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-15 00:41:07,440 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-15 00:41:07,442 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-15 00:41:07,442 - AIEngine - INFO - Initializing LLM backends...
2025-06-15 00:41:07,442 - AIEngine - INFO - ✅ Ollama backend available
2025-06-15 00:41:07,443 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-15 00:41:07,443 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-15 00:41:07,443 - AIEngine - INFO - ✅ Transformers backend available
2025-06-15 00:41:07,443 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-15 00:41:07,444 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-15 00:41:07,704 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-15 00:41:07,705 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-15 00:41:07,705 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-15 00:41:07,706 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-15 00:41:07,707 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-15 00:41:07,707 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-15 00:41:07,707 - AIEngine - INFO - AI Engine initialized successfully
2025-06-15 00:41:07,842 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Speakers (DualSense Wireless Co', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-15 00:41:07,907 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-15 00:41:08,047 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-15 00:41:08,047 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-15 00:41:11,066 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 9749.700694329327
2025-06-15 00:41:11,084 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-15 00:41:11,084 - STTEngine - INFO -    Energy threshold: 9749.700694329327
2025-06-15 00:41:11,085 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-15 00:41:11,085 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-15 00:41:11,085 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-15 00:41:11,085 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-15 00:41:11,085 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-15 00:41:11,085 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-15 00:41:11,086 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-15 00:41:11,348 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-15 00:41:11,348 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-15 00:41:11,348 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-15 00:41:14,627 - STTEngine - INFO - Testing microphone... Say something!
2025-06-15 00:41:20,649 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-15 00:41:24,853 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 00:41:24,854 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-15 00:41:24,855 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-15 00:41:24,855 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-15 00:41:42,320 - STTEngine - ERROR - Unexpected recognition error for ar-SA: [WinError 2] The system cannot find the file specified
2025-06-15 00:42:23,255 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'ما حدا فيكم حكى لي مرحبا' | Confidence: 0.87
2025-06-15 00:42:23,260 - STTEngine - INFO - 🎤 Heard: 'ما حدا فيكم حكى لي مرحبا'
2025-06-15 00:42:40,986 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'احنا في زمن مسخره' | Confidence: 0.79
2025-06-15 00:42:40,987 - STTEngine - INFO - 🎤 Heard: 'احنا في زمن مسخره'
2025-06-15 00:42:45,608 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'صبركم علي في المسخره' | Confidence: 0.81
2025-06-15 00:42:45,608 - STTEngine - INFO - 🎤 Heard: 'صبركم علي في المسخره'
2025-06-15 00:42:55,467 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-15 00:42:55,470 - AIEngine - INFO - Attempting LLM response generation...
2025-06-15 00:42:55,470 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-15 00:42:55,470 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-15 00:42:55,471 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-15 00:42:55,471 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-15 00:43:32,435 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'شكو' | Confidence: 0.90
2025-06-15 00:43:32,435 - STTEngine - INFO - 🎤 Heard: 'شكو'
2025-06-15 00:44:14,113 - AIEngine - INFO - Generating response for: 'سلام'
2025-06-15 00:44:14,113 - AIEngine - INFO - Attempting LLM response generation...
2025-06-15 00:44:14,114 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-15 00:44:14,114 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-15 00:44:14,114 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-15 00:44:14,115 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-15 00:44:39,144 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:39,145 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:39,171 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:55,208 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:55,208 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:55,212 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:51:57,926 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-15 00:51:57,931 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-15 00:51:57,931 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-15 00:51:57,931 - MemorySystem - INFO - Memory system initialized successfully
2025-06-15 00:51:57,932 - ModelManager - INFO - No existing models config found
2025-06-15 00:51:57,933 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-15 00:51:57,933 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-15 00:51:57,934 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-15 00:51:57,935 - AIEngine - INFO - Initializing LLM backends...
2025-06-15 00:51:57,935 - AIEngine - INFO - ✅ Ollama backend available
2025-06-15 00:51:57,935 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-15 00:51:57,936 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-15 00:51:57,936 - AIEngine - INFO - ✅ Transformers backend available
2025-06-15 00:51:57,936 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-15 00:51:57,937 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-15 00:51:58,187 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-15 00:51:58,188 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-15 00:51:58,189 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-15 00:51:58,189 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-15 00:51:58,190 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-15 00:51:58,191 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-15 00:51:58,191 - AIEngine - INFO - AI Engine initialized successfully
2025-06-15 00:51:58,304 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-15 00:51:58,360 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-15 00:51:58,480 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-15 00:51:58,480 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-15 00:52:01,490 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-15 00:52:01,509 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-15 00:52:01,509 - STTEngine - INFO -    Energy threshold: 150
2025-06-15 00:52:01,509 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-15 00:52:01,509 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-15 00:52:01,509 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-15 00:52:01,510 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-15 00:52:01,510 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-15 00:52:01,510 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-15 00:52:01,510 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-15 00:52:01,754 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-15 00:52:01,754 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-15 00:52:01,754 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-15 00:52:05,129 - STTEngine - INFO - Testing microphone... Say something!
2025-06-15 00:52:08,156 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-15 00:52:12,359 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 00:52:12,360 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-15 00:52:12,360 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-15 00:52:12,360 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-15 00:53:09,072 - AIEngine - INFO - Found Ollama models: ['qwen3:235b', 'gemma3:27b', 'deepseek-coder-v2:236b', 'dolphin-llama3:70b']
2025-06-15 00:53:19,514 - CleanProfessionalInterface - INFO - Model configuration updated: {'backend': 'ollama', 'model': 'qwen3:235b', 'always_llm': True, 'fallback_to_rules': True}
2025-06-15 00:53:37,541 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-15 00:53:37,544 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-15 00:53:37,551 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-15 00:53:37,551 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-15 00:53:37,551 - MemorySystem - INFO - Memory system initialized successfully
2025-06-15 00:53:37,554 - ModelManager - INFO - No existing models config found
2025-06-15 00:53:37,554 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-15 00:53:37,555 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-15 00:53:37,555 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-15 00:53:37,556 - AIEngine - INFO - Initializing LLM backends...
2025-06-15 00:53:37,556 - AIEngine - INFO - ✅ Ollama backend available
2025-06-15 00:53:37,556 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-15 00:53:37,556 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-15 00:53:37,557 - AIEngine - INFO - ✅ Transformers backend available
2025-06-15 00:53:37,557 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-15 00:53:37,557 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-15 00:53:38,011 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-15 00:53:38,013 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-15 00:53:38,013 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-15 00:53:38,013 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-15 00:53:38,016 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-15 00:53:38,016 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-15 00:53:38,017 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-15 00:53:38,017 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-15 00:53:38,017 - AIEngine - INFO - Attempting LLM response generation...
2025-06-15 00:53:38,017 - AIEngine - INFO - AI Engine initialized successfully
2025-06-15 00:53:38,017 - AIEngine - INFO - Attempting LLM response generation...
2025-06-15 00:53:38,018 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-15 00:53:38,018 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-15 00:53:38,018 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-15 00:53:38,018 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-15 00:53:38,019 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-15 00:53:38,019 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-15 00:53:38,019 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-15 00:53:38,019 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-15 00:53:39,027 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-15 00:53:39,084 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-15 00:53:39,213 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-15 00:53:39,213 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-15 00:53:42,205 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-15 00:53:42,372 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-15 00:53:42,373 - STTEngine - INFO -    Energy threshold: 150
2025-06-15 00:53:42,373 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-15 00:53:42,373 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-15 00:53:42,373 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-15 00:53:42,373 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-15 00:53:42,373 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-15 00:53:42,373 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-15 00:53:42,373 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-15 00:53:44,577 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-15 00:53:44,577 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-15 00:53:44,577 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-15 00:53:48,945 - STTEngine - INFO - Testing microphone... Say something!
2025-06-15 00:53:51,986 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-15 00:53:56,378 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 00:53:56,379 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-15 00:53:56,379 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-15 00:53:56,379 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-15 00:53:58,000 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 00:53:59,086 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-15 00:53:59,258 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-15 00:53:59,258 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-15 00:53:59,298 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-15 00:53:59,302 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-15 00:53:59,302 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: Gideon A...
2025-06-15 00:53:59,479 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,479 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,479 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-15 00:53:59,479 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: Ultra-pr...
2025-06-15 00:53:59,480 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,480 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,480 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-15 00:53:59,480 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: Bilingua...
2025-06-15 00:53:59,480 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,481 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,481 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-15 00:53:59,481 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: Voice in...
2025-06-15 00:53:59,481 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,482 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,482 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-15 00:53:59,482 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: Ultra-lo...
2025-06-15 00:53:59,482 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,482 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,483 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-15 00:53:59,483 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: AI Model...
2025-06-15 00:53:59,483 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,483 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,483 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-15 00:53:59,484 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: Advanced...
2025-06-15 00:53:59,484 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,484 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,484 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-15 00:53:59,484 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: Drag & d...
2025-06-15 00:53:59,485 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,485 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,485 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-15 00:53:59,485 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: 🚀 Gideon...
2025-06-15 00:53:59,493 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,493 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,493 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-15 00:53:59,493 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: 💼 Ultra-...
2025-06-15 00:53:59,498 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,498 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,498 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-15 00:53:59,498 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: 🎯 Real-t...
2025-06-15 00:53:59,501 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,502 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,502 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-15 00:53:59,502 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: 🧠 Enterp...
2025-06-15 00:53:59,503 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,503 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,503 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-15 00:53:59,503 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: 💡 Advanc...
2025-06-15 00:53:59,507 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,507 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,507 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-15 00:53:59,508 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: 💬 Say 'G...
2025-06-15 00:53:59,508 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,508 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:54:01,521 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-15 00:54:01,521 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:54:01] 🤖 Gideon: Hello! I'...
2025-06-15 00:54:01,523 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:54:01,523 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:54:18,612 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> هلا...
2025-06-15 00:54:18,613 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫ :You 👤 [00:54:18]
...
2025-06-15 00:54:18,615 - UltraProfessionalInterface - INFO - 🔤 Arabic text processed: ﻼﻫ...
2025-06-15 00:54:18,630 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:54:18,630 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:54:18,638 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-15 00:54:18,638 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-15 00:54:18,638 - AIEngine - INFO - Attempting LLM response generation...
2025-06-15 00:54:18,638 - AIEngine - INFO - Attempting LLM response generation...
2025-06-15 00:54:18,638 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-15 00:54:18,638 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-15 00:54:18,639 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-15 00:54:18,639 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-15 00:54:18,639 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-15 00:54:18,639 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-15 00:54:18,639 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-15 00:54:18,639 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-15 00:54:18,755 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'أعمل على ذلك......'
2025-06-15 00:54:18,755 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'أعمل على ذلك......'
2025-06-15 00:54:18,775 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'أعمل على ذلك......'
2025-06-15 00:54:18,775 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'أعمل على ذلك......'
2025-06-15 00:54:18,775 - UltraProfessionalInterface - INFO - 📝 Direct display for short response
2025-06-15 00:54:18,775 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> أعمل على ذلك......
2025-06-15 00:54:18,776 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ...ﻚﻟﺫ ﻰﻠﻋ ﻞﻤﻋﺃ :Gideon 🤖 [00:...
2025-06-15 00:54:18,777 - UltraProfessionalInterface - INFO - 🔤 Arabic text processed: ...ﻚﻟﺫ ﻰﻠﻋ ﻞﻤﻋﺃ...
2025-06-15 00:54:18,801 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:54:18,801 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:04,785 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-15 01:04:04,787 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-15 01:04:04,793 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-15 01:04:04,794 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-15 01:04:04,794 - MemorySystem - INFO - Memory system initialized successfully
2025-06-15 01:04:04,795 - ModelManager - INFO - No existing models config found
2025-06-15 01:04:04,796 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-15 01:04:04,796 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-15 01:04:04,797 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-15 01:04:04,797 - AIEngine - INFO - Initializing LLM backends...
2025-06-15 01:04:04,798 - AIEngine - INFO - ✅ Ollama backend available
2025-06-15 01:04:04,798 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-15 01:04:04,798 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-15 01:04:04,798 - AIEngine - INFO - ✅ Transformers backend available
2025-06-15 01:04:04,798 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-15 01:04:04,798 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-15 01:04:05,066 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-15 01:04:05,068 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-15 01:04:05,068 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-15 01:04:05,068 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-15 01:04:05,070 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-15 01:04:05,070 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-15 01:04:05,070 - AIEngine - INFO - AI Engine initialized successfully
2025-06-15 01:04:05,190 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-15 01:04:05,247 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-15 01:04:05,374 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-15 01:04:05,375 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-15 01:04:08,385 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-15 01:04:08,403 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-15 01:04:08,404 - STTEngine - INFO -    Energy threshold: 150
2025-06-15 01:04:08,404 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-15 01:04:08,404 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-15 01:04:08,404 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-15 01:04:08,404 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-15 01:04:08,404 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-15 01:04:08,404 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-15 01:04:08,404 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-15 01:04:08,632 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-15 01:04:08,632 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-15 01:04:08,632 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-15 01:04:11,958 - STTEngine - INFO - Testing microphone... Say something!
2025-06-15 01:04:14,986 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-15 01:04:19,241 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 01:04:19,242 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-15 01:04:19,242 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-15 01:04:19,242 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-15 01:04:19,965 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 01:04:20,091 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-15 01:04:20,272 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-15 01:04:20,273 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-15 01:04:20,314 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-15 01:04:20,318 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-15 01:04:20,318 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:20] ⚙️ System: Gideon A...
2025-06-15 01:04:20,537 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:20,537 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:20,537 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-15 01:04:20,537 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:20] ⚙️ System: Ultra-pr...
2025-06-15 01:04:20,539 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:20,539 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:20,540 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-15 01:04:20,540 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:20] ⚙️ System: Bilingua...
2025-06-15 01:04:20,540 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:20,540 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:20,540 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-15 01:04:20,540 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:20] ⚙️ System: Voice in...
2025-06-15 01:04:20,541 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:20,541 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:20,541 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-15 01:04:20,541 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:20] ⚙️ System: Ultra-lo...
2025-06-15 01:04:20,542 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:20,542 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:20,542 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-15 01:04:20,542 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:20] ⚙️ System: AI Model...
2025-06-15 01:04:20,543 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:20,543 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:20,543 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-15 01:04:20,543 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:20] ⚙️ System: Advanced...
2025-06-15 01:04:20,543 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:20,544 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:20,544 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-15 01:04:20,544 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:20] ⚙️ System: Drag & d...
2025-06-15 01:04:20,544 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:20,544 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:20,544 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-15 01:04:20,545 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:20] ⚙️ System: 🚀 Gideon...
2025-06-15 01:04:20,552 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:20,553 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:20,553 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-15 01:04:20,553 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:20] ⚙️ System: 💼 Ultra-...
2025-06-15 01:04:20,557 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:20,557 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:20,557 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-15 01:04:20,557 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:20] ⚙️ System: 🎯 Real-t...
2025-06-15 01:04:20,560 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:20,560 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:20,561 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-15 01:04:20,561 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:20] ⚙️ System: 🧠 Enterp...
2025-06-15 01:04:20,561 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:20,561 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:20,562 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-15 01:04:20,562 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:20] ⚙️ System: 💡 Advanc...
2025-06-15 01:04:20,565 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:20,565 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:20,565 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-15 01:04:20,566 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:20] ⚙️ System: 💬 Say 'G...
2025-06-15 01:04:20,566 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:20,566 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:04:22,843 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-15 01:04:22,844 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:04:22] 🤖 Gideon: Hello! I'...
2025-06-15 01:04:22,846 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:04:22,846 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:05:44,851 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-15 01:05:44,854 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-15 01:05:44,860 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-15 01:05:44,860 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-15 01:05:44,860 - MemorySystem - INFO - Memory system initialized successfully
2025-06-15 01:05:44,862 - ModelManager - INFO - No existing models config found
2025-06-15 01:05:44,863 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-15 01:05:44,863 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-15 01:05:44,864 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-15 01:05:44,864 - AIEngine - INFO - Initializing LLM backends...
2025-06-15 01:05:44,864 - AIEngine - INFO - ✅ Ollama backend available
2025-06-15 01:05:44,865 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-15 01:05:44,865 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-15 01:05:44,865 - AIEngine - INFO - ✅ Transformers backend available
2025-06-15 01:05:44,865 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-15 01:05:44,865 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-15 01:05:45,142 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-15 01:05:45,144 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-15 01:05:45,144 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-15 01:05:45,144 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-15 01:05:45,147 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-15 01:05:45,147 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-15 01:05:45,147 - AIEngine - INFO - AI Engine initialized successfully
2025-06-15 01:05:45,312 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-15 01:05:45,370 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-15 01:05:45,497 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-15 01:05:45,497 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-15 01:05:48,508 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-15 01:05:48,524 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-15 01:05:48,524 - STTEngine - INFO -    Energy threshold: 150
2025-06-15 01:05:48,524 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-15 01:05:48,525 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-15 01:05:48,525 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-15 01:05:48,525 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-15 01:05:48,525 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-15 01:05:48,525 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-15 01:05:48,525 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-15 01:05:48,745 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-15 01:05:48,745 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-15 01:05:48,745 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-15 01:05:52,050 - STTEngine - INFO - Testing microphone... Say something!
2025-06-15 01:05:55,077 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-15 01:05:59,314 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 01:05:59,315 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-15 01:05:59,315 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-15 01:05:59,315 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-15 01:06:00,976 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 01:06:01,113 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-15 01:06:01,294 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-15 01:06:01,295 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-15 01:06:01,335 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-15 01:06:01,338 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-15 01:06:01,338 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:01] ⚙️ System: Gideon A...
2025-06-15 01:06:01,536 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:01,536 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:06:01,536 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-15 01:06:01,536 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:01] ⚙️ System: Ultra-pr...
2025-06-15 01:06:01,537 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:01,537 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:06:01,537 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-15 01:06:01,537 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:01] ⚙️ System: Bilingua...
2025-06-15 01:06:01,537 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:01,538 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:06:01,538 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-15 01:06:01,538 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:01] ⚙️ System: Voice in...
2025-06-15 01:06:01,538 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:01,538 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:06:01,538 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-15 01:06:01,538 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:01] ⚙️ System: Ultra-lo...
2025-06-15 01:06:01,539 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:01,539 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:06:01,539 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-15 01:06:01,539 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:01] ⚙️ System: AI Model...
2025-06-15 01:06:01,540 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:01,540 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:06:01,540 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-15 01:06:01,540 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:01] ⚙️ System: Advanced...
2025-06-15 01:06:01,541 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:01,541 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:06:01,541 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-15 01:06:01,541 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:01] ⚙️ System: Drag & d...
2025-06-15 01:06:01,541 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:01,541 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:06:01,542 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-15 01:06:01,542 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:01] ⚙️ System: 🚀 Gideon...
2025-06-15 01:06:01,551 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:01,551 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:06:01,551 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-15 01:06:01,551 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:01] ⚙️ System: 💼 Ultra-...
2025-06-15 01:06:01,555 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:01,555 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:06:01,555 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-15 01:06:01,556 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:01] ⚙️ System: 🎯 Real-t...
2025-06-15 01:06:01,559 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:01,559 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:06:01,560 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-15 01:06:01,560 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:01] ⚙️ System: 🧠 Enterp...
2025-06-15 01:06:01,560 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:01,560 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:06:01,561 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-15 01:06:01,561 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:01] ⚙️ System: 💡 Advanc...
2025-06-15 01:06:01,564 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:01,564 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:06:01,564 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-15 01:06:01,565 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:01] ⚙️ System: 💬 Say 'G...
2025-06-15 01:06:01,565 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:01,565 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:06:03,573 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-15 01:06:03,574 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:06:03] 🤖 Gideon: Hello! I'...
2025-06-15 01:06:03,574 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:06:03,575 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:25,658 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-15 01:12:25,660 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-15 01:12:25,665 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-15 01:12:25,665 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-15 01:12:25,665 - MemorySystem - INFO - Memory system initialized successfully
2025-06-15 01:12:25,666 - ModelManager - INFO - No existing models config found
2025-06-15 01:12:25,667 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-15 01:12:25,667 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-15 01:12:25,668 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-15 01:12:25,668 - AIEngine - INFO - Initializing LLM backends...
2025-06-15 01:12:25,669 - AIEngine - INFO - ✅ Ollama backend available
2025-06-15 01:12:25,669 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-15 01:12:25,669 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-15 01:12:25,669 - AIEngine - INFO - ✅ Transformers backend available
2025-06-15 01:12:25,670 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-15 01:12:25,670 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-15 01:12:25,937 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-15 01:12:25,939 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-15 01:12:25,939 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-15 01:12:25,939 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-15 01:12:25,941 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-15 01:12:25,942 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-15 01:12:25,942 - AIEngine - INFO - AI Engine initialized successfully
2025-06-15 01:12:26,043 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-15 01:12:26,100 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-15 01:12:26,227 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-15 01:12:26,227 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-15 01:12:29,222 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-15 01:12:29,235 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-15 01:12:29,235 - STTEngine - INFO -    Energy threshold: 150
2025-06-15 01:12:29,235 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-15 01:12:29,235 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-15 01:12:29,235 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-15 01:12:29,236 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-15 01:12:29,236 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-15 01:12:29,236 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-15 01:12:29,236 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-15 01:12:29,454 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-15 01:12:29,454 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-15 01:12:29,454 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-15 01:12:32,707 - STTEngine - INFO - Testing microphone... Say something!
2025-06-15 01:12:35,712 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-15 01:12:40,297 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 01:12:40,298 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-15 01:12:40,298 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-15 01:12:40,298 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-15 01:12:40,978 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 01:12:41,105 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-15 01:12:41,289 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-15 01:12:41,289 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-15 01:12:41,328 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-15 01:12:41,337 - SystemTrayManager - INFO - System tray manager initialized
2025-06-15 01:12:41,337 - UltraProfessionalInterface - WARNING - ⚠️ System tray not available - install pystray for minimize-to-tray functionality
2025-06-15 01:12:41,340 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-15 01:12:41,340 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:41] ⚙️ System: Gideon A...
2025-06-15 01:12:41,542 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:41,543 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:41,543 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-15 01:12:41,543 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:41] ⚙️ System: Ultra-pr...
2025-06-15 01:12:41,543 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:41,544 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:41,544 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-15 01:12:41,544 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:41] ⚙️ System: Bilingua...
2025-06-15 01:12:41,544 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:41,545 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:41,545 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-15 01:12:41,545 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:41] ⚙️ System: Voice in...
2025-06-15 01:12:41,546 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:41,546 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:41,546 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-15 01:12:41,546 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:41] ⚙️ System: Ultra-lo...
2025-06-15 01:12:41,547 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:41,547 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:41,547 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-15 01:12:41,547 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:41] ⚙️ System: AI Model...
2025-06-15 01:12:41,547 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:41,547 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:41,548 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-15 01:12:41,548 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:41] ⚙️ System: Advanced...
2025-06-15 01:12:41,548 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:41,548 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:41,548 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-15 01:12:41,548 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:41] ⚙️ System: Drag & d...
2025-06-15 01:12:41,549 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:41,549 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:41,549 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-15 01:12:41,550 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:41] ⚙️ System: 🚀 Gideon...
2025-06-15 01:12:41,560 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:41,560 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:41,560 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-15 01:12:41,561 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:41] ⚙️ System: 💼 Ultra-...
2025-06-15 01:12:41,565 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:41,565 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:41,565 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-15 01:12:41,565 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:41] ⚙️ System: 🎯 Real-t...
2025-06-15 01:12:41,568 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:41,569 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:41,569 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-15 01:12:41,569 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:41] ⚙️ System: 🧠 Enterp...
2025-06-15 01:12:41,570 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:41,570 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:41,570 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-15 01:12:41,570 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:41] ⚙️ System: 💡 Advanc...
2025-06-15 01:12:41,574 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:41,574 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:41,574 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-15 01:12:41,574 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:41] ⚙️ System: 💬 Say 'G...
2025-06-15 01:12:41,575 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:41,575 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:12:43,579 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-15 01:12:43,665 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:12:43] 🤖 Gideon: Hello! I'...
2025-06-15 01:12:43,668 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:12:43,668 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:26,074 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-15 01:14:26,077 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-15 01:14:26,082 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-15 01:14:26,082 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-15 01:14:26,083 - MemorySystem - INFO - Memory system initialized successfully
2025-06-15 01:14:26,084 - ModelManager - INFO - No existing models config found
2025-06-15 01:14:26,085 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-15 01:14:26,085 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-15 01:14:26,086 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-15 01:14:26,086 - AIEngine - INFO - Initializing LLM backends...
2025-06-15 01:14:26,087 - AIEngine - INFO - ✅ Ollama backend available
2025-06-15 01:14:26,087 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-15 01:14:26,087 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-15 01:14:26,087 - AIEngine - INFO - ✅ Transformers backend available
2025-06-15 01:14:26,087 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-15 01:14:26,088 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-15 01:14:26,375 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-15 01:14:26,377 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-15 01:14:26,377 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-15 01:14:26,377 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-15 01:14:26,380 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-15 01:14:26,380 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-15 01:14:26,380 - AIEngine - INFO - AI Engine initialized successfully
2025-06-15 01:14:26,499 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-15 01:14:26,558 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-15 01:14:26,685 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-15 01:14:26,685 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-15 01:14:29,679 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-15 01:14:29,691 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-15 01:14:29,691 - STTEngine - INFO -    Energy threshold: 150
2025-06-15 01:14:29,691 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-15 01:14:29,691 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-15 01:14:29,691 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-15 01:14:29,691 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-15 01:14:29,692 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-15 01:14:29,692 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-15 01:14:29,692 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-15 01:14:29,890 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-15 01:14:29,890 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-15 01:14:29,890 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-15 01:14:33,142 - STTEngine - INFO - Testing microphone... Say something!
2025-06-15 01:14:36,151 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-15 01:14:40,711 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 01:14:40,712 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-15 01:14:40,712 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-15 01:14:40,712 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-15 01:14:41,383 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 01:14:41,516 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-15 01:14:41,699 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-15 01:14:41,699 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-15 01:14:41,739 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-15 01:14:41,746 - SystemTrayManager - INFO - System tray manager initialized
2025-06-15 01:14:41,747 - SystemTrayManager - INFO - System tray icon created successfully
2025-06-15 01:14:41,748 - SystemTrayManager - INFO - System tray started successfully
2025-06-15 01:14:41,748 - UltraProfessionalInterface - INFO - ✅ System tray initialized - chat accessible when minimized
2025-06-15 01:14:41,753 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-15 01:14:41,754 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:41] ⚙️ System: Gideon A...
2025-06-15 01:14:41,937 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:41,937 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:41,937 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-15 01:14:41,938 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:41] ⚙️ System: Ultra-pr...
2025-06-15 01:14:41,938 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:41,938 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:41,938 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-15 01:14:41,939 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:41] ⚙️ System: Bilingua...
2025-06-15 01:14:41,939 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:41,939 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:41,939 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-15 01:14:41,939 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:41] ⚙️ System: Voice in...
2025-06-15 01:14:41,940 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:41,940 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:41,940 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-15 01:14:41,940 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:41] ⚙️ System: Ultra-lo...
2025-06-15 01:14:41,940 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:41,940 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:41,941 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-15 01:14:41,941 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:41] ⚙️ System: AI Model...
2025-06-15 01:14:41,942 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:41,942 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:41,942 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-15 01:14:41,942 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:41] ⚙️ System: Advanced...
2025-06-15 01:14:41,943 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:41,943 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:41,943 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-15 01:14:41,943 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:41] ⚙️ System: Drag & d...
2025-06-15 01:14:41,944 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:41,944 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:41,944 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-15 01:14:41,944 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:41] ⚙️ System: 🚀 Gideon...
2025-06-15 01:14:41,951 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:41,952 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:41,952 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-15 01:14:41,952 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:41] ⚙️ System: 💼 Ultra-...
2025-06-15 01:14:41,956 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:41,956 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:41,956 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-15 01:14:41,956 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:41] ⚙️ System: 🎯 Real-t...
2025-06-15 01:14:41,959 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:41,959 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:41,959 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-15 01:14:41,960 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:41] ⚙️ System: 🧠 Enterp...
2025-06-15 01:14:41,961 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:41,961 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:41,961 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-15 01:14:41,961 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:41] ⚙️ System: 💡 Advanc...
2025-06-15 01:14:41,964 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:41,965 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:41,965 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-15 01:14:41,965 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:41] ⚙️ System: 💬 Say 'G...
2025-06-15 01:14:41,966 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:41,966 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:14:43,966 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-15 01:14:43,966 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:43] 🤖 Gideon: Hello! I'...
2025-06-15 01:14:43,967 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:14:43,967 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:37,488 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-15 01:20:37,489 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-15 01:20:37,494 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-15 01:20:37,494 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-15 01:20:37,494 - MemorySystem - INFO - Memory system initialized successfully
2025-06-15 01:20:37,497 - ModelManager - INFO - No existing models config found
2025-06-15 01:20:37,497 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-15 01:20:37,497 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-15 01:20:37,499 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-15 01:20:37,499 - AIEngine - INFO - Initializing LLM backends...
2025-06-15 01:20:37,500 - AIEngine - INFO - ✅ Ollama backend available
2025-06-15 01:20:37,500 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-15 01:20:37,500 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-15 01:20:37,500 - AIEngine - INFO - ✅ Transformers backend available
2025-06-15 01:20:37,500 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-15 01:20:37,501 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-15 01:20:37,779 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-15 01:20:37,780 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-15 01:20:37,780 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-15 01:20:37,780 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-15 01:20:37,782 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-15 01:20:37,783 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-15 01:20:37,783 - AIEngine - INFO - AI Engine initialized successfully
2025-06-15 01:20:37,883 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-15 01:20:37,939 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-15 01:20:38,052 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-15 01:20:38,052 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-15 01:20:41,051 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-15 01:20:41,063 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-15 01:20:41,063 - STTEngine - INFO -    Energy threshold: 150
2025-06-15 01:20:41,063 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-15 01:20:41,064 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-15 01:20:41,064 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-15 01:20:41,064 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-15 01:20:41,064 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-15 01:20:41,064 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-15 01:20:41,064 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-15 01:20:41,280 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-15 01:20:41,280 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-15 01:20:41,280 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-15 01:20:42,361 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-15 01:20:42,385 - TerminalManager - INFO - Shutting down terminal manager...
2025-06-15 01:20:42,405 - TerminalManager - INFO - Created default terminal session: session_1
2025-06-15 01:20:42,405 - TerminalManager - INFO - Closed session: session_0
2025-06-15 01:20:42,411 - TerminalManager - INFO - Terminal manager shutdown complete
2025-06-15 01:20:43,010 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-15 01:20:43,112 - STTEngine - INFO - Stopped continuous listening
2025-06-15 01:20:43,127 - UltraProfessionalInterface - INFO - ✅ Static interface cleanup completed - chat box was permanently visible
2025-06-15 01:20:43,128 - GideonMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-15 01:20:44,549 - STTEngine - INFO - Testing microphone... Say something!
2025-06-15 01:20:47,575 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-15 01:20:51,780 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 01:20:51,780 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-15 01:20:51,781 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-15 01:20:51,781 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-15 01:20:52,442 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 01:20:52,578 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-15 01:20:52,768 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-15 01:20:52,768 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-15 01:20:52,811 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-15 01:20:52,817 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-15 01:20:52,817 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-15 01:20:52,820 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-15 01:20:52,821 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:52] ⚙️ System: Gideon A...
2025-06-15 01:20:53,028 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:53,028 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:53,028 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-15 01:20:53,028 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:53] ⚙️ System: Ultra-pr...
2025-06-15 01:20:53,029 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:53,029 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:53,029 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-15 01:20:53,029 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:53] ⚙️ System: Bilingua...
2025-06-15 01:20:53,030 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:53,030 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:53,030 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-15 01:20:53,031 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:53] ⚙️ System: Voice in...
2025-06-15 01:20:53,031 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:53,031 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:53,031 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-15 01:20:53,031 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:53] ⚙️ System: Ultra-lo...
2025-06-15 01:20:53,032 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:53,032 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:53,032 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-15 01:20:53,032 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:53] ⚙️ System: AI Model...
2025-06-15 01:20:53,032 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:53,032 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:53,032 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-15 01:20:53,032 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:53] ⚙️ System: Advanced...
2025-06-15 01:20:53,033 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:53,033 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:53,033 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-15 01:20:53,033 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:53] ⚙️ System: Drag & d...
2025-06-15 01:20:53,034 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:53,034 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:53,034 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-15 01:20:53,035 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:53] ⚙️ System: 🚀 Gideon...
2025-06-15 01:20:53,042 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:53,043 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:53,043 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-15 01:20:53,043 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:53] ⚙️ System: 💼 Ultra-...
2025-06-15 01:20:53,046 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:53,046 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:53,047 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-15 01:20:53,047 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:53] ⚙️ System: 🎯 Real-t...
2025-06-15 01:20:53,050 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:53,050 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:53,050 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-15 01:20:53,051 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:53] ⚙️ System: 🧠 Enterp...
2025-06-15 01:20:53,051 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:53,051 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:53,051 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-15 01:20:53,051 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:53] ⚙️ System: 💡 Advanc...
2025-06-15 01:20:53,054 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:53,055 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:53,055 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-15 01:20:53,055 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:53] ⚙️ System: 💬 Say 'G...
2025-06-15 01:20:53,055 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:53,056 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:20:55,064 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-15 01:20:55,169 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:20:55] 🤖 Gideon: Hello! I'...
2025-06-15 01:20:55,173 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:20:55,173 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 01:52:17,342 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-15 01:52:17,438 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
2025-06-15 01:52:25,639 - CompactChatManager - INFO - Compact chat window hidden
2025-06-15 01:52:25,641 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-15 01:52:25,647 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:52:25] ⚙️ System: 💬 Compac...
2025-06-15 01:52:25,684 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 01:52:25,685 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
