#!/usr/bin/env python3
"""
Simple and Clean Gideon AI Assistant
Focused on usability and clarity
"""

import sys
import os
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging to be less verbose
logging.basicConfig(
    level=logging.WARNING,  # Only show warnings and errors
    format='%(levelname)s: %(message)s'
)

def main():
    """Main entry point for simple Gideon AI Assistant"""
    
    print("🚀 Starting Gideon AI Assistant (Simple Mode)...")
    print("=" * 50)
    
    try:
        # Import core components
        from src.core.gideon_core import Gideon<PERSON>ore
        from src.ui.simple_interface import SimpleInterface
        
        print("✅ Initializing AI core...")

        # Initialize core with minimal logging
        gideon_core = GideonCore()
        gideon_core.initialize()  # Properly initialize the core

        print("✅ Creating simple interface...")

        # Create simple interface
        interface = SimpleInterface(gideon_core)
        
        print("✅ Gideon AI Assistant Ready!")
        print("\nFEATURES:")
        print("• Clean and simple interface")
        print("• Arabic and English support")
        print("• Voice input (click 🎤)")
        print("• Keyboard shortcuts (F1 for help)")
        print("• Real-time AI responses")
        print("\nStarting interface...")
        print("=" * 50)
        
        # Start the interface
        interface.run()
        
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("\nPlease install required packages:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting Gideon: {e}")
        print("\nFor detailed logs, check the logs directory")
        sys.exit(1)

if __name__ == "__main__":
    main()
