2025-06-14 01:16:49,577 - <PERSON><PERSON><PERSON> - INFO - 🤖 Starting Gideon AI Assistant
2025-06-14 01:16:49,579 - <PERSON><PERSON><PERSON> - INFO - Initializing Gideon Core System...
2025-06-14 01:16:49,583 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-14 01:16:49,583 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-14 01:16:49,584 - MemorySystem - INFO - Memory system initialized successfully
2025-06-14 01:16:49,585 - ModelManager - INFO - No existing models config found
2025-06-14 01:16:49,587 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-14 01:16:49,587 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-14 01:16:49,588 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-14 01:16:49,588 - AIEngine - INFO - Initializing LLM backends...
2025-06-14 01:16:49,589 - <PERSON><PERSON><PERSON>ine - INFO - ✅ Ollama backend available
2025-06-14 01:16:49,589 - <PERSON><PERSON><PERSON>ine - INFO - ℹ️ llama.cpp backend not available
2025-06-14 01:16:49,589 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-14 01:16:49,590 - AIEngine - INFO - ✅ Transformers backend available
2025-06-14 01:16:49,590 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-14 01:16:49,590 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-14 01:16:49,842 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-14 01:16:49,844 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-14 01:16:49,844 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-14 01:16:49,844 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-14 01:18:50,659 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-14 01:18:50,674 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-14 01:18:50,675 - AIEngine - INFO - AI Engine initialized successfully
2025-06-14 01:18:50,993 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Speakers (DualSense Wireless Co', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-14 01:18:51,062 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-14 01:18:51,243 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-14 01:18:51,244 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-14 01:18:54,252 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 3423.7782592055037
2025-06-14 01:18:54,271 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-14 01:18:54,271 - STTEngine - INFO -    Energy threshold: 3423.7782592055037
2025-06-14 01:18:54,272 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-14 01:18:54,272 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-14 01:18:54,272 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-14 01:18:54,273 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-14 01:18:54,273 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-14 01:18:54,273 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-14 01:18:54,273 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-14 01:18:55,227 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-14 01:18:55,228 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-14 01:18:55,228 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-14 01:18:58,507 - STTEngine - INFO - Testing microphone... Say something!
2025-06-14 01:19:04,635 - STTEngine - INFO - 🌍 Detected language: en | Text: 'hello' | Confidence: 1.00
2025-06-14 01:19:04,636 - STTEngine - INFO - Microphone test successful. Heard: hello
2025-06-14 01:19:06,781 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-14 01:19:06,782 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-14 01:19:06,783 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-14 01:19:06,783 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-14 01:19:07,920 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-14 01:19:08,089 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-14 01:19:08,285 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-14 01:19:08,285 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-14 01:19:08,331 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-14 01:19:08,338 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-14 01:19:08,338 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:08] ⚙️ System: Gideon A...
2025-06-14 01:19:08,617 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:08,618 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:08,618 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-14 01:19:08,618 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:08] ⚙️ System: Ultra-pr...
2025-06-14 01:19:08,619 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:08,619 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:08,619 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-14 01:19:08,619 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:08] ⚙️ System: Bilingua...
2025-06-14 01:19:08,620 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:08,620 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:08,620 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-14 01:19:08,620 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:08] ⚙️ System: Voice in...
2025-06-14 01:19:08,621 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:08,621 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:08,621 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-14 01:19:08,621 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:08] ⚙️ System: Ultra-lo...
2025-06-14 01:19:08,623 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:08,623 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:08,623 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-14 01:19:08,623 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:08] ⚙️ System: AI Model...
2025-06-14 01:19:08,624 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:08,624 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:08,624 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-14 01:19:08,624 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:08] ⚙️ System: Advanced...
2025-06-14 01:19:08,625 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:08,625 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:08,625 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-14 01:19:08,625 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:08] ⚙️ System: Drag & d...
2025-06-14 01:19:08,626 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:08,626 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:08,627 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-14 01:19:08,627 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:08] ⚙️ System: 🚀 Gideon...
2025-06-14 01:19:08,634 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:08,634 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:08,634 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-14 01:19:08,635 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:08] ⚙️ System: 💼 Ultra-...
2025-06-14 01:19:08,642 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:08,642 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:08,642 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-14 01:19:08,642 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:08] ⚙️ System: 🎯 Real-t...
2025-06-14 01:19:08,646 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:08,646 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:08,646 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-14 01:19:08,647 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:08] ⚙️ System: 🧠 Enterp...
2025-06-14 01:19:08,647 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:08,648 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:08,648 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-14 01:19:08,648 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:08] ⚙️ System: 💡 Advanc...
2025-06-14 01:19:08,652 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:08,652 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:08,652 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-14 01:19:08,653 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:08] ⚙️ System: 💬 Say 'G...
2025-06-14 01:19:08,653 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:08,654 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:10,654 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-14 01:19:10,655 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:10] 🤖 Gideon: Hello! I'...
2025-06-14 01:19:10,659 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:10,659 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:20,304 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> hi...
2025-06-14 01:19:20,304 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:20] 👤 You: hi
...
2025-06-14 01:19:20,318 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:20,319 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:20,335 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-14 01:19:20,335 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'hi' -> 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-14 01:19:20,368 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-14 01:19:20,369 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'Hi there! I'm Gideon, your AI ...'
2025-06-14 01:19:20,369 - UltraProfessionalInterface - INFO - 📝 Using streaming effect for long response
2025-06-14 01:19:20,369 - UltraProfessionalInterface - INFO - 🎬 Starting streaming effect for response: 'Hi there! I'm Gideon, your AI ...'
2025-06-14 01:19:20,572 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hi there! I'm Gideon, your AI assistant. What can ...
2025-06-14 01:19:20,572 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:20] 🤖 Gideon: Hi there!...
2025-06-14 01:19:20,576 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:20,576 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:20,577 - UltraProfessionalInterface - INFO - ✅ Streaming effect completed
2025-06-14 01:19:24,812 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> هلا...
2025-06-14 01:19:24,819 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫ :You 👤 [01:19:24]
...
2025-06-14 01:19:24,826 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:24,826 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:24,828 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-14 01:19:24,832 - AIEngine - INFO - Attempting LLM response generation...
2025-06-14 01:19:24,832 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-14 01:19:24,832 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-14 01:19:24,833 - AIEngine - INFO - Attempting LLM response generation...
2025-06-14 01:19:24,833 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-14 01:19:24,833 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-14 01:19:24,834 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-14 01:19:24,834 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-14 01:19:24,834 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-14 01:19:24,834 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-14 01:19:24,847 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-14 01:19:24,938 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-14 01:19:24,938 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'I'm processing that......'
2025-06-14 01:19:24,952 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-14 01:19:24,952 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'I'm processing that......'
2025-06-14 01:19:24,952 - UltraProfessionalInterface - INFO - 📝 Direct display for short response
2025-06-14 01:19:24,952 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-14 01:19:24,952 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:24] 🤖 Gideon: I'm proce...
2025-06-14 01:19:24,977 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:19:24,977 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:19:24,978 - TTSEngine - ERROR - Error during speech: run loop already started
2025-06-14 01:19:46,442 - STTEngine - INFO - 🌍 Detected language: en | Text: 'han han' | Confidence: 0.81
2025-06-14 01:19:46,442 - STTEngine - INFO - 🎤 Heard: 'han han'
2025-06-14 01:19:50,619 - STTEngine - INFO - 🌍 Detected language: en | Text: 'picture' | Confidence: 0.90
2025-06-14 01:19:50,619 - STTEngine - INFO - 🎤 Heard: 'picture'
2025-06-14 01:19:59,116 - STTEngine - INFO - 🌍 Detected language: en | Text: 'I love you' | Confidence: 0.88
2025-06-14 01:19:59,116 - STTEngine - INFO - 🎤 Heard: 'I love you'
2025-06-14 01:20:16,614 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'انا ما حد كلمني بس' | Confidence: 0.77
2025-06-14 01:20:16,615 - STTEngine - INFO - 🎤 Heard: 'انا ما حد كلمني بس'
2025-06-14 01:20:16,629 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-14 01:20:16,629 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-14 01:20:16,645 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-14 01:20:24,536 - STTEngine - INFO - 🌍 Detected language: en | Text: 'Mushkil movie Mushkil Ahmed' | Confidence: 0.83
2025-06-14 01:20:24,536 - STTEngine - INFO - 🎤 Heard: 'Mushkil movie Mushkil Ahmed'
2025-06-14 01:20:26,473 - AIEngine - INFO - LLM response generated successfully: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-14 01:20:26,474 - AIEngine - INFO - LLM generated successful response: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-14 01:20:26,482 - AIEngine - INFO - Final response generated: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-14 01:20:32,139 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'شو اسمه حتى لما قلت لنا في واحد' | Confidence: 0.86
2025-06-14 01:20:32,140 - STTEngine - INFO - 🎤 Heard: 'شو اسمه حتى لما قلت لنا في واحد'
2025-06-14 01:20:37,587 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'السعدون يبي دقيقه' | Confidence: 0.83
2025-06-14 01:20:37,587 - STTEngine - INFO - 🎤 Heard: 'السعدون يبي دقيقه'
2025-06-14 01:20:49,212 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'لا تقول' | Confidence: 0.91
2025-06-14 01:20:49,212 - STTEngine - INFO - 🎤 Heard: 'لا تقول'
2025-06-14 01:21:03,077 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'اذكرك مره لما قلت لك الحين موجود' | Confidence: 0.79
2025-06-14 01:21:03,077 - STTEngine - INFO - 🎤 Heard: 'اذكرك مره لما قلت لك الحين موجود'
2025-06-14 01:21:42,422 - STTEngine - INFO - 🌍 Detected language: en | Text: 'you know what does it cost wale' | Confidence: 1.00
2025-06-14 01:21:42,423 - STTEngine - INFO - 🎤 Heard: 'you know what does it cost wale'
2025-06-14 01:21:47,094 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'خرا' | Confidence: 0.90
2025-06-14 01:21:47,094 - STTEngine - INFO - 🎤 Heard: 'خرا'
2025-06-14 01:21:58,172 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'خلي' | Confidence: 0.90
2025-06-14 01:21:58,172 - STTEngine - INFO - 🎤 Heard: 'خلي'
2025-06-14 01:22:04,347 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'احمر قديم' | Confidence: 0.83
2025-06-14 01:22:04,348 - STTEngine - INFO - 🎤 Heard: 'احمر قديم'
2025-06-14 01:22:06,275 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-14 01:22:06,279 - TerminalManager - INFO - Shutting down terminal manager...
2025-06-14 01:22:06,317 - TerminalManager - INFO - Created default terminal session: session_1
2025-06-14 01:22:06,317 - TerminalManager - INFO - Closed session: session_0
2025-06-14 01:22:06,321 - TerminalManager - INFO - Terminal manager shutdown complete
2025-06-14 01:22:06,532 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-14 01:22:06,670 - GideonMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-14 01:40:36,024 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-14 01:40:36,026 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-14 01:40:36,031 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-14 01:40:36,031 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-14 01:40:36,032 - MemorySystem - INFO - Memory system initialized successfully
2025-06-14 01:40:36,033 - ModelManager - INFO - No existing models config found
2025-06-14 01:40:36,034 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-14 01:40:36,034 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-14 01:40:36,035 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-14 01:40:36,036 - AIEngine - INFO - Initializing LLM backends...
2025-06-14 01:40:36,036 - AIEngine - INFO - ✅ Ollama backend available
2025-06-14 01:40:36,036 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-14 01:40:36,037 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-14 01:40:36,037 - AIEngine - INFO - ✅ Transformers backend available
2025-06-14 01:40:36,038 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-14 01:40:36,038 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-14 01:40:36,299 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-14 01:40:36,300 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-14 01:40:36,301 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-14 01:40:36,301 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-14 01:42:34,602 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-14 01:42:34,617 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-14 01:42:34,617 - AIEngine - INFO - AI Engine initialized successfully
2025-06-14 01:42:34,889 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Speakers (DualSense Wireless Co', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-14 01:42:34,957 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-14 01:42:35,133 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-14 01:42:35,134 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-14 01:42:38,143 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 3592.6243918889727
2025-06-14 01:42:38,165 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-14 01:42:38,166 - STTEngine - INFO -    Energy threshold: 3592.6243918889727
2025-06-14 01:42:38,167 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-14 01:42:38,167 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-14 01:42:38,168 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-14 01:42:38,169 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-14 01:42:38,169 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-14 01:42:38,169 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-14 01:42:38,170 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-14 01:42:39,314 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-14 01:42:39,316 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-14 01:42:39,317 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-14 01:42:42,691 - STTEngine - INFO - Testing microphone... Say something!
2025-06-14 01:42:45,717 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-14 01:42:49,921 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-14 01:42:49,935 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-14 01:42:49,936 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-14 01:42:49,936 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-14 01:42:50,831 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-14 01:42:50,953 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-14 01:42:51,126 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-14 01:42:51,126 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-14 01:42:51,166 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-14 01:42:51,171 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-14 01:42:51,172 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:51] ⚙️ System: Gideon A...
2025-06-14 01:42:51,417 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:51,417 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:42:51,418 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-14 01:42:51,418 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:51] ⚙️ System: Ultra-pr...
2025-06-14 01:42:51,418 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:51,419 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:42:51,419 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-14 01:42:51,419 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:51] ⚙️ System: Bilingua...
2025-06-14 01:42:51,420 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:51,420 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:42:51,420 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-14 01:42:51,420 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:51] ⚙️ System: Voice in...
2025-06-14 01:42:51,421 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:51,422 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:42:51,422 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-14 01:42:51,422 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:51] ⚙️ System: Ultra-lo...
2025-06-14 01:42:51,423 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:51,423 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:42:51,423 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-14 01:42:51,424 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:51] ⚙️ System: AI Model...
2025-06-14 01:42:51,424 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:51,424 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:42:51,425 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-14 01:42:51,425 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:51] ⚙️ System: Advanced...
2025-06-14 01:42:51,425 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:51,426 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:42:51,426 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-14 01:42:51,426 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:51] ⚙️ System: Drag & d...
2025-06-14 01:42:51,426 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:51,427 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:42:51,427 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-14 01:42:51,428 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:51] ⚙️ System: 🚀 Gideon...
2025-06-14 01:42:51,435 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:51,435 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:42:51,435 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-14 01:42:51,435 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:51] ⚙️ System: 💼 Ultra-...
2025-06-14 01:42:51,439 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:51,439 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:42:51,439 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-14 01:42:51,439 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:51] ⚙️ System: 🎯 Real-t...
2025-06-14 01:42:51,443 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:51,443 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:42:51,443 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-14 01:42:51,443 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:51] ⚙️ System: 🧠 Enterp...
2025-06-14 01:42:51,448 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:51,448 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:42:51,448 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-14 01:42:51,448 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:51] ⚙️ System: 💡 Advanc...
2025-06-14 01:42:51,452 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:51,452 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:42:51,452 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-14 01:42:51,453 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:51] ⚙️ System: 💬 Say 'G...
2025-06-14 01:42:51,453 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:51,453 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:42:53,458 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-14 01:42:53,458 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:42:53] 🤖 Gideon: Hello! I'...
2025-06-14 01:42:53,461 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:42:53,461 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:43:27,348 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> هلا...
2025-06-14 01:43:27,354 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫ :You 👤 [01:43:27]
...
2025-06-14 01:43:27,372 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:43:27,372 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:43:27,380 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-14 01:43:27,382 - AIEngine - INFO - Attempting LLM response generation...
2025-06-14 01:43:27,383 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-14 01:43:27,384 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-14 01:43:27,384 - AIEngine - INFO - Attempting LLM response generation...
2025-06-14 01:43:27,384 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-14 01:43:27,385 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-14 01:43:27,385 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-14 01:43:27,385 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-14 01:43:27,386 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-14 01:43:27,386 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-14 01:43:27,398 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-14 01:43:27,499 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-14 01:43:27,500 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'I'm processing that......'
2025-06-14 01:43:27,521 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-14 01:43:27,522 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'I'm processing that......'
2025-06-14 01:43:27,522 - UltraProfessionalInterface - INFO - 📝 Direct display for short response
2025-06-14 01:43:27,522 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-14 01:43:27,522 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:43:27] 🤖 Gideon: I'm proce...
2025-06-14 01:43:27,547 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:43:27,548 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:43:51,999 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Terminal Status:
• Visible: No
• Available: Yes
• ...
2025-06-14 01:43:52,000 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:43:52] ⚙️ System: Terminal...
2025-06-14 01:43:52,005 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:43:52,005 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:43:55,193 - UltraProfessionalInterface - ERROR - Error toggling terminal: cannot use geometry manager grid inside . which already has slaves managed by pack
2025-06-14 01:43:55,193 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Terminal error: cannot use geometry manager grid i...
2025-06-14 01:43:55,193 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:43:55] ⚙️ System: Terminal...
2025-06-14 01:43:55,198 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:43:55,198 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:44:01,813 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-14 01:44:01,814 - TerminalManager - INFO - Shutting down terminal manager...
2025-06-14 01:44:01,818 - STTEngine - INFO - Stopped continuous listening
2025-06-14 01:44:01,825 - TerminalManager - INFO - Created default terminal session: session_1
2025-06-14 01:44:01,826 - TerminalManager - INFO - Closed session: session_0
2025-06-14 01:44:01,826 - TerminalManager - INFO - Terminal manager shutdown complete
2025-06-14 01:44:02,609 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-14 01:44:02,732 - GideonMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-14 01:44:18,515 - AIEngine - INFO - LLM response generated successfully: مرحباً، سعدت لرؤيتك. فيم يمكنني مساعدتك اليوم؟...
2025-06-14 01:44:18,517 - AIEngine - INFO - LLM generated successful response: مرحباً، سعدت لرؤيتك. فيم يمكنني مساعدتك اليوم؟...
2025-06-14 01:44:18,531 - AIEngine - INFO - Final response generated: مرحباً، سعدت لرؤيتك. فيم يمكنني مساعدتك اليوم؟ Fee...
2025-06-14 01:44:41,682 - AIEngine - INFO - LLM response generated successfully: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-14 01:44:41,684 - AIEngine - INFO - LLM generated successful response: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-14 01:44:41,688 - AIEngine - INFO - Final response generated: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-14 01:55:19,736 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-14 01:55:19,738 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-14 01:55:19,743 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-14 01:55:19,743 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-14 01:55:19,743 - MemorySystem - INFO - Memory system initialized successfully
2025-06-14 01:55:19,744 - ModelManager - INFO - No existing models config found
2025-06-14 01:55:19,745 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-14 01:55:19,745 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-14 01:55:19,747 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-14 01:55:19,747 - AIEngine - INFO - Initializing LLM backends...
2025-06-14 01:55:19,747 - AIEngine - INFO - ✅ Ollama backend available
2025-06-14 01:55:19,748 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-14 01:55:19,748 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-14 01:55:19,748 - AIEngine - INFO - ✅ Transformers backend available
2025-06-14 01:55:19,749 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-14 01:55:19,749 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-14 01:55:20,008 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-14 01:55:20,010 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-14 01:55:20,010 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-14 01:55:20,010 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-14 01:57:38,029 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-14 01:57:38,043 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-14 01:57:38,043 - AIEngine - INFO - AI Engine initialized successfully
2025-06-14 01:57:38,632 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Speakers (DualSense Wireless Co', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-14 01:57:38,807 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-14 01:57:39,162 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-14 01:57:39,163 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-14 01:57:42,174 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 3970.918939285944
2025-06-14 01:57:42,193 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-14 01:57:42,193 - STTEngine - INFO -    Energy threshold: 3970.918939285944
2025-06-14 01:57:42,193 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-14 01:57:42,194 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-14 01:57:42,194 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-14 01:57:42,194 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-14 01:57:42,194 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-14 01:57:42,194 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-14 01:57:42,195 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-14 01:57:42,611 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-14 01:57:42,612 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-14 01:57:42,612 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-14 01:57:45,877 - STTEngine - INFO - Testing microphone... Say something!
2025-06-14 01:57:50,121 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-14 01:57:54,313 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-14 01:57:54,314 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-14 01:57:54,314 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-14 01:57:54,314 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-14 01:57:55,019 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-14 01:57:55,141 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-14 01:57:55,320 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-14 01:57:55,321 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-14 01:57:55,361 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-14 01:57:55,367 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-14 01:57:55,367 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:55] ⚙️ System: Gideon A...
2025-06-14 01:57:55,614 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:55,615 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:57:55,615 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-14 01:57:55,615 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:55] ⚙️ System: Ultra-pr...
2025-06-14 01:57:55,615 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:55,616 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:57:55,616 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-14 01:57:55,616 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:55] ⚙️ System: Bilingua...
2025-06-14 01:57:55,616 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:55,616 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:57:55,616 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-14 01:57:55,617 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:55] ⚙️ System: Voice in...
2025-06-14 01:57:55,617 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:55,617 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:57:55,617 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-14 01:57:55,617 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:55] ⚙️ System: Ultra-lo...
2025-06-14 01:57:55,618 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:55,618 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:57:55,618 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-14 01:57:55,618 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:55] ⚙️ System: AI Model...
2025-06-14 01:57:55,618 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:55,619 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:57:55,619 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-14 01:57:55,619 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:55] ⚙️ System: Advanced...
2025-06-14 01:57:55,619 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:55,619 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:57:55,619 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-14 01:57:55,620 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:55] ⚙️ System: Drag & d...
2025-06-14 01:57:55,620 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:55,621 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:57:55,621 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-14 01:57:55,621 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:55] ⚙️ System: 🚀 Gideon...
2025-06-14 01:57:55,628 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:55,629 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:57:55,629 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-14 01:57:55,629 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:55] ⚙️ System: 💼 Ultra-...
2025-06-14 01:57:55,633 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:55,634 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:57:55,634 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-14 01:57:55,634 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:55] ⚙️ System: 🎯 Real-t...
2025-06-14 01:57:55,637 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:55,637 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:57:55,637 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-14 01:57:55,637 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:55] ⚙️ System: 🧠 Enterp...
2025-06-14 01:57:55,638 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:55,638 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:57:55,638 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-14 01:57:55,639 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:55] ⚙️ System: 💡 Advanc...
2025-06-14 01:57:55,642 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:55,642 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:57:55,642 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-14 01:57:55,642 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:55] ⚙️ System: 💬 Say 'G...
2025-06-14 01:57:55,643 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:55,643 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:57:57,646 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-14 01:57:57,646 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:57:57] 🤖 Gideon: Hello! I'...
2025-06-14 01:57:57,647 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:57:57,647 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:17,936 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-14 01:58:17,938 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-14 01:58:17,946 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-14 01:58:17,946 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-14 01:58:17,946 - MemorySystem - INFO - Memory system initialized successfully
2025-06-14 01:58:17,949 - ModelManager - INFO - No existing models config found
2025-06-14 01:58:17,949 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-14 01:58:17,949 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-14 01:58:17,950 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-14 01:58:17,951 - AIEngine - INFO - Initializing LLM backends...
2025-06-14 01:58:17,951 - AIEngine - INFO - ✅ Ollama backend available
2025-06-14 01:58:17,951 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-14 01:58:17,951 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-14 01:58:17,952 - AIEngine - INFO - ✅ Transformers backend available
2025-06-14 01:58:17,952 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-14 01:58:17,952 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-14 01:58:18,240 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-14 01:58:18,242 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-14 01:58:18,242 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-14 01:58:18,242 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-14 01:58:18,246 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-14 01:58:18,246 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-14 01:58:18,246 - AIEngine - INFO - AI Engine initialized successfully
2025-06-14 01:58:18,400 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Speakers (DualSense Wireless Co', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-14 01:58:18,477 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-14 01:58:18,641 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-14 01:58:18,642 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-14 01:58:21,649 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 4855.039033890862
2025-06-14 01:58:21,669 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-14 01:58:21,670 - STTEngine - INFO -    Energy threshold: 4855.039033890862
2025-06-14 01:58:21,670 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-14 01:58:21,670 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-14 01:58:21,670 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-14 01:58:21,670 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-14 01:58:21,671 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-14 01:58:21,671 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-14 01:58:21,671 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-14 01:58:21,883 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-14 01:58:21,883 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-14 01:58:21,884 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-14 01:58:25,187 - STTEngine - INFO - Testing microphone... Say something!
2025-06-14 01:58:30,271 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-14 01:58:34,473 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-14 01:58:34,474 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-14 01:58:34,474 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-14 01:58:34,474 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-14 01:58:35,161 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-14 01:58:35,319 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-14 01:58:35,519 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-14 01:58:35,520 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-14 01:58:35,562 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-14 01:58:35,569 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-14 01:58:35,570 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:35] ⚙️ System: Gideon A...
2025-06-14 01:58:35,796 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:35,796 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:35,796 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-14 01:58:35,796 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:35] ⚙️ System: Ultra-pr...
2025-06-14 01:58:35,797 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:35,797 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:35,798 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-14 01:58:35,798 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:35] ⚙️ System: Bilingua...
2025-06-14 01:58:35,798 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:35,798 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:35,799 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-14 01:58:35,799 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:35] ⚙️ System: Voice in...
2025-06-14 01:58:35,799 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:35,799 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:35,799 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-14 01:58:35,800 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:35] ⚙️ System: Ultra-lo...
2025-06-14 01:58:35,801 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:35,801 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:35,801 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-14 01:58:35,801 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:35] ⚙️ System: AI Model...
2025-06-14 01:58:35,802 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:35,802 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:35,802 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-14 01:58:35,802 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:35] ⚙️ System: Advanced...
2025-06-14 01:58:35,803 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:35,803 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:35,803 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-14 01:58:35,803 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:35] ⚙️ System: Drag & d...
2025-06-14 01:58:35,804 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:35,804 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:35,804 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-14 01:58:35,804 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:35] ⚙️ System: 🚀 Gideon...
2025-06-14 01:58:35,813 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:35,813 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:35,814 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-14 01:58:35,814 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:35] ⚙️ System: 💼 Ultra-...
2025-06-14 01:58:35,818 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:35,818 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:35,819 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-14 01:58:35,819 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:35] ⚙️ System: 🎯 Real-t...
2025-06-14 01:58:35,823 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:35,823 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:35,823 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-14 01:58:35,823 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:35] ⚙️ System: 🧠 Enterp...
2025-06-14 01:58:35,824 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:35,824 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:35,824 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-14 01:58:35,824 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:35] ⚙️ System: 💡 Advanc...
2025-06-14 01:58:35,828 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:35,828 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:35,829 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-14 01:58:35,829 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:35] ⚙️ System: 💬 Say 'G...
2025-06-14 01:58:35,829 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:35,829 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:58:37,833 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-14 01:58:37,833 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:58:37] 🤖 Gideon: Hello! I'...
2025-06-14 01:58:37,834 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:58:37,834 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:59:10,870 - AIEngine - INFO - Found Ollama models: ['qwen3:235b', 'gemma3:27b', 'deepseek-coder-v2:236b', 'dolphin-llama3:70b']
2025-06-14 01:59:20,911 - AIEngine - INFO - Found Ollama models: ['qwen3:235b', 'gemma3:27b', 'deepseek-coder-v2:236b', 'dolphin-llama3:70b']
2025-06-14 01:59:30,946 - UltraProfessionalInterface - INFO - Model configuration updated: {'backend': 'ollama', 'model': 'qwen3:235b', 'always_llm': True, 'fallback_to_rules': True}
2025-06-14 01:59:30,946 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ✅ AI model configuration updated...
2025-06-14 01:59:30,946 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:59:30] ⚙️ System: ✅ AI mod...
2025-06-14 01:59:30,957 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:59:30,957 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:59:35,374 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> hi...
2025-06-14 01:59:35,374 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:59:35] 👤 You: hi
...
2025-06-14 01:59:35,390 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:59:35,390 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:59:35,401 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-14 01:59:35,402 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'hi' -> 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-14 01:59:35,433 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-14 01:59:35,433 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'Hi there! I'm Gideon, your AI ...'
2025-06-14 01:59:35,433 - UltraProfessionalInterface - INFO - 📝 Using streaming effect for long response
2025-06-14 01:59:35,433 - UltraProfessionalInterface - INFO - 🎬 Starting streaming effect for response: 'Hi there! I'm Gideon, your AI ...'
2025-06-14 01:59:35,634 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hi there! I'm Gideon, your AI assistant. What can ...
2025-06-14 01:59:35,634 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:59:35] 🤖 Gideon: Hi there!...
2025-06-14 01:59:35,638 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:59:35,638 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:59:35,639 - UltraProfessionalInterface - INFO - ✅ Streaming effect completed
2025-06-14 01:59:41,089 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> هلا...
2025-06-14 01:59:41,095 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫ :You 👤 [01:59:41]
...
2025-06-14 01:59:41,104 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:59:41,105 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:59:41,106 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-14 01:59:41,108 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-14 01:59:41,108 - AIEngine - INFO - Attempting LLM response generation...
2025-06-14 01:59:41,108 - AIEngine - INFO - Attempting LLM response generation...
2025-06-14 01:59:41,109 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-14 01:59:41,109 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-14 01:59:41,109 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-14 01:59:41,109 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-14 01:59:41,109 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-14 01:59:41,110 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-14 01:59:41,110 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-14 01:59:41,110 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-14 01:59:41,219 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-14 01:59:41,219 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'I'm processing that......'
2025-06-14 01:59:41,233 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-14 01:59:41,233 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'I'm processing that......'
2025-06-14 01:59:41,233 - UltraProfessionalInterface - INFO - 📝 Direct display for short response
2025-06-14 01:59:41,234 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-14 01:59:41,234 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:59:41] 🤖 Gideon: I'm proce...
2025-06-14 01:59:41,261 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 01:59:41,261 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 01:59:41,689 - STTEngine - INFO - 🌍 Detected language: en | Text: 'Gionee company' | Confidence: 0.86
2025-06-14 01:59:41,689 - STTEngine - INFO - 🎤 Heard: 'Gionee company'
2025-06-14 02:00:14,608 - AIEngine - INFO - LLM response generated successfully: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-14 02:00:14,608 - AIEngine - INFO - LLM generated successful response: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-14 02:00:14,623 - AIEngine - INFO - Final response generated: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-14 02:00:26,575 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-14 02:00:26,575 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-14 02:00:26,579 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-14 02:01:40,018 - STTEngine - INFO - 🌍 Detected language: en | Text: 'lotus' | Confidence: 0.90
2025-06-14 02:01:40,020 - STTEngine - INFO - 🎤 Heard: 'lotus'
2025-06-14 02:02:41,174 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-14 02:02:41,175 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-14 02:02:41,182 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-14 02:02:41,182 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-14 02:02:41,182 - MemorySystem - INFO - Memory system initialized successfully
2025-06-14 02:02:41,184 - ModelManager - INFO - No existing models config found
2025-06-14 02:02:41,184 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-14 02:02:41,185 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-14 02:02:41,186 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-14 02:02:41,186 - AIEngine - INFO - Initializing LLM backends...
2025-06-14 02:02:41,186 - AIEngine - INFO - ✅ Ollama backend available
2025-06-14 02:02:41,186 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-14 02:02:41,186 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-14 02:02:41,187 - AIEngine - INFO - ✅ Transformers backend available
2025-06-14 02:02:41,187 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-14 02:02:41,187 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-14 02:02:41,451 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-14 02:02:41,453 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-14 02:02:41,453 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-14 02:02:41,453 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-14 02:02:41,456 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-14 02:02:41,456 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-14 02:02:41,456 - AIEngine - INFO - AI Engine initialized successfully
2025-06-14 02:02:41,584 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Speakers (DualSense Wireless Co', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-14 02:02:41,668 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-14 02:02:41,832 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-14 02:02:41,833 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-14 02:02:44,841 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 4423.028719482754
2025-06-14 02:02:44,861 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-14 02:02:44,861 - STTEngine - INFO -    Energy threshold: 4423.028719482754
2025-06-14 02:02:44,861 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-14 02:02:44,861 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-14 02:02:44,861 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-14 02:02:44,862 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-14 02:02:44,862 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-14 02:02:44,862 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-14 02:02:44,862 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-14 02:02:45,103 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-14 02:02:45,103 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-14 02:02:45,104 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-14 02:02:48,494 - STTEngine - INFO - Testing microphone... Say something!
2025-06-14 02:02:53,746 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-14 02:02:58,026 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-14 02:02:58,027 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-14 02:02:58,030 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-14 02:02:58,030 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-14 02:02:58,979 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-14 02:02:59,161 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-14 02:02:59,391 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-14 02:02:59,393 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-14 02:02:59,442 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-14 02:02:59,449 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-14 02:02:59,450 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:02:59,458 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-14 02:02:59,458 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:02:59,459 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-14 02:02:59,459 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:02:59,460 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-14 02:02:59,460 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:02:59,461 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-14 02:02:59,461 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:02:59,462 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-14 02:02:59,462 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:02:59,463 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-14 02:02:59,463 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:02:59,464 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-14 02:02:59,464 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:02:59,465 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-14 02:02:59,465 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:02:59,465 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-14 02:02:59,466 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:02:59,466 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-14 02:02:59,466 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:02:59,467 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-14 02:02:59,467 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:02:59,468 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-14 02:02:59,469 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:02:59,470 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-14 02:02:59,470 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:03:01,473 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-14 02:03:01,473 - UltraProfessionalInterface - ERROR - ❌ Critical error in _add_message: cannot access local variable 'text_direction_manager' where it is not associated with a value
2025-06-14 02:04:21,866 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-14 02:04:21,869 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-14 02:04:21,876 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-14 02:04:21,876 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-14 02:04:21,876 - MemorySystem - INFO - Memory system initialized successfully
2025-06-14 02:04:21,878 - ModelManager - INFO - No existing models config found
2025-06-14 02:04:21,878 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-14 02:04:21,878 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-14 02:04:21,881 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-14 02:04:21,881 - AIEngine - INFO - Initializing LLM backends...
2025-06-14 02:04:21,882 - AIEngine - INFO - ✅ Ollama backend available
2025-06-14 02:04:21,882 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-14 02:04:21,882 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-14 02:04:21,883 - AIEngine - INFO - ✅ Transformers backend available
2025-06-14 02:04:21,883 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-14 02:04:21,883 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-14 02:04:22,153 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-14 02:04:22,155 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-14 02:04:22,155 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-14 02:04:22,155 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-14 02:04:22,159 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-14 02:04:22,159 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-14 02:04:22,159 - AIEngine - INFO - AI Engine initialized successfully
2025-06-14 02:04:22,308 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Speakers (DualSense Wireless Co', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-14 02:04:22,379 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-14 02:04:22,538 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-14 02:04:22,538 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-14 02:04:25,546 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 3908.220369820802
2025-06-14 02:04:25,565 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-14 02:04:25,565 - STTEngine - INFO -    Energy threshold: 3908.220369820802
2025-06-14 02:04:25,565 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-14 02:04:25,566 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-14 02:04:25,566 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-14 02:04:25,566 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-14 02:04:25,566 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-14 02:04:25,566 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-14 02:04:25,566 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-14 02:04:25,817 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-14 02:04:25,817 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-14 02:04:25,818 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-14 02:04:29,150 - STTEngine - INFO - Testing microphone... Say something!
2025-06-14 02:04:33,057 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-14 02:04:37,264 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-14 02:04:37,264 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-14 02:04:37,265 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-14 02:04:37,265 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-14 02:04:38,031 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-14 02:04:38,196 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-14 02:04:38,414 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-14 02:04:38,414 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-14 02:04:38,459 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-14 02:04:38,464 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-14 02:04:38,464 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:38] ⚙️ System: Gideon A...
2025-06-14 02:04:38,690 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:38,691 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:04:38,691 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-14 02:04:38,691 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:38] ⚙️ System: Ultra-pr...
2025-06-14 02:04:38,692 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:38,692 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:04:38,693 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-14 02:04:38,693 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:38] ⚙️ System: Bilingua...
2025-06-14 02:04:38,693 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:38,693 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:04:38,694 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-14 02:04:38,694 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:38] ⚙️ System: Voice in...
2025-06-14 02:04:38,694 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:38,694 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:04:38,694 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-14 02:04:38,695 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:38] ⚙️ System: Ultra-lo...
2025-06-14 02:04:38,695 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:38,695 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:04:38,695 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-14 02:04:38,695 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:38] ⚙️ System: AI Model...
2025-06-14 02:04:38,696 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:38,696 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:04:38,696 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-14 02:04:38,696 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:38] ⚙️ System: Advanced...
2025-06-14 02:04:38,697 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:38,697 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:04:38,697 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-14 02:04:38,697 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:38] ⚙️ System: Drag & d...
2025-06-14 02:04:38,698 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:38,698 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:04:38,698 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-14 02:04:38,699 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:38] ⚙️ System: 🚀 Gideon...
2025-06-14 02:04:38,707 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:38,707 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:04:38,708 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-14 02:04:38,708 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:38] ⚙️ System: 💼 Ultra-...
2025-06-14 02:04:38,712 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:38,712 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:04:38,712 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-14 02:04:38,713 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:38] ⚙️ System: 🎯 Real-t...
2025-06-14 02:04:38,716 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:38,716 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:04:38,717 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-14 02:04:38,717 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:38] ⚙️ System: 🧠 Enterp...
2025-06-14 02:04:38,717 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:38,717 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:04:38,718 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-14 02:04:38,718 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:38] ⚙️ System: 💡 Advanc...
2025-06-14 02:04:38,722 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:38,722 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:04:38,722 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-14 02:04:38,723 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:38] ⚙️ System: 💬 Say 'G...
2025-06-14 02:04:38,723 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:38,723 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:04:40,725 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-14 02:04:40,726 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:04:40] 🤖 Gideon: Hello! I'...
2025-06-14 02:04:40,726 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:04:40,727 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:05:16,566 - AIEngine - INFO - Found Ollama models: ['qwen3:235b', 'gemma3:27b', 'deepseek-coder-v2:236b', 'dolphin-llama3:70b']
2025-06-14 02:05:27,769 - UltraProfessionalInterface - INFO - Model configuration updated: {'backend': 'ollama', 'model': 'qwen3:235b', 'always_llm': True, 'fallback_to_rules': True}
2025-06-14 02:05:27,770 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ✅ AI model configuration updated...
2025-06-14 02:05:27,771 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:05:27] ⚙️ System: ✅ AI mod...
2025-06-14 02:05:27,780 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:05:27,780 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:05:42,404 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> تكلم العربيه...
2025-06-14 02:05:42,410 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻪﻴﺑﺮﻌﻟﺍ ﻢﻠﻜﺗ :You 👤 [02:05:42]...
2025-06-14 02:05:42,411 - UltraProfessionalInterface - INFO - 🔤 Arabic text processed: ﻪﻴﺑﺮﻌﻟﺍ ﻢﻠﻜﺗ...
2025-06-14 02:05:42,428 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:05:42,428 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:05:42,436 - AIEngine - INFO - Generating response for: 'تكلم العربيه'
2025-06-14 02:05:42,439 - AIEngine - INFO - Attempting LLM response generation...
2025-06-14 02:05:42,439 - AIEngine - INFO - Generating response for: 'تكلم العربيه'
2025-06-14 02:05:42,440 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-14 02:05:42,440 - AIEngine - INFO - Attempting LLM response generation...
2025-06-14 02:05:42,440 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-14 02:05:42,440 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-14 02:05:42,441 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-14 02:05:42,441 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-14 02:05:42,441 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-14 02:05:42,441 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-14 02:05:42,448 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-14 02:05:42,549 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-14 02:05:42,550 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'تكلم العربيه' -> 'I'm processing that......'
2025-06-14 02:05:42,572 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-14 02:05:42,573 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'I'm processing that......'
2025-06-14 02:05:42,573 - UltraProfessionalInterface - INFO - 📝 Direct display for short response
2025-06-14 02:05:42,573 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-14 02:05:42,573 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:05:42] 🤖 Gideon: I'm proce...
2025-06-14 02:05:42,598 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-14 02:05:42,599 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-14 02:06:11,108 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-14 02:06:11,110 - TerminalManager - INFO - Shutting down terminal manager...
2025-06-14 02:06:11,179 - TerminalManager - INFO - Created default terminal session: session_1
2025-06-14 02:06:11,179 - TerminalManager - INFO - Closed session: session_0
2025-06-14 02:06:11,181 - TerminalManager - INFO - Terminal manager shutdown complete
2025-06-14 02:06:11,181 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-14 02:06:11,351 - GideonMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-14 02:06:12,220 - STTEngine - INFO - Stopped continuous listening
2025-06-14 02:07:19,203 - AIEngine - INFO - LLM response generated successfully: مرحباً، أنا جديون، مساعد ذكيفه ووديع. سأقدم لك إجا...
2025-06-14 02:07:19,209 - AIEngine - INFO - LLM generated successful response: مرحباً، أنا جديون، مساعد ذكيفه ووديع. سأقدم لك إجا...
2025-06-14 02:07:19,225 - AIEngine - INFO - Final response generated: مرحباً، أنا جديون، مساعد ذكيفه ووديع. سأقدم لك إجا...
2025-06-14 02:07:27,910 - AIEngine - INFO - LLM response generated successfully: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-14 02:07:27,910 - AIEngine - INFO - LLM generated successful response: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-14 02:07:27,914 - AIEngine - INFO - Final response generated: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-14 02:21:12,465 - SimpleInterface - ERROR - AI response error: 'NoneType' object has no attribute 'generate_response'
