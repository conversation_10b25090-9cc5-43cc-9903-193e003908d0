2025-06-19 02:10:36,544 - <PERSON><PERSON><PERSON> - INFO - 🤖 Starting Gideon AI Assistant Enterprise Edition
2025-06-19 02:10:47,719 - <PERSON><PERSON><PERSON> - INFO - Initializing Gideon Core System...
2025-06-19 02:10:47,725 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-19 02:10:47,725 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-19 02:10:47,725 - MemorySystem - INFO - Memory system initialized successfully
2025-06-19 02:10:47,727 - ModelManager - INFO - No existing models config found
2025-06-19 02:10:47,727 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-19 02:10:47,728 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-19 02:10:47,729 - AIEngine - INFO - Loaded 31 learned response patterns
2025-06-19 02:10:47,729 - AIEngine - INFO - Initializing LLM backends...
2025-06-19 02:10:47,730 - <PERSON><PERSON><PERSON>ine - INFO - ✅ Ollama backend available
2025-06-19 02:10:47,730 - <PERSON>E<PERSON>ine - INFO - ℹ️ llama.cpp backend not available
2025-06-19 02:10:47,730 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-19 02:10:47,731 - AIEngine - INFO - ✅ Transformers backend available
2025-06-19 02:10:47,731 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-19 02:10:47,731 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-19 02:10:48,038 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-19 02:10:48,039 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-19 02:10:48,039 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-19 02:10:48,039 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-19 02:10:48,041 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-19 02:10:48,041 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-19 02:10:48,041 - AIEngine - INFO - AI Engine initialized successfully
2025-06-19 02:10:48,159 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Speakers (DualSense Wireless Co', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-19 02:10:48,224 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-19 02:10:48,364 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-19 02:10:48,365 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-19 02:10:51,383 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 38.18555160813161
2025-06-19 02:10:51,401 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-19 02:10:51,402 - STTEngine - INFO -    Energy threshold: 150
2025-06-19 02:10:51,402 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-19 02:10:51,402 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-19 02:10:51,403 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-19 02:10:51,403 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-19 02:10:51,403 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-19 02:10:51,403 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-19 02:10:51,404 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-19 02:10:51,712 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-19 02:10:51,713 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-19 02:10:51,713 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-19 02:10:54,992 - STTEngine - INFO - Testing microphone... Say something!
2025-06-19 02:10:58,086 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-19 02:11:02,290 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-19 02:11:02,291 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-19 02:11:02,292 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-19 02:11:02,292 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-19 02:11:02,459 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-19 02:11:02,718 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-19 02:11:02,894 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-19 02:11:03,087 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-19 02:11:03,088 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-19 02:11:03,110 - UltraProfessionalInterface - INFO - ✅ Input entry created and tested successfully
2025-06-19 02:11:03,121 - UltraProfessionalInterface - INFO - ✅ Send button created with explicit command binding
2025-06-19 02:11:03,130 - UltraProfessionalInterface - INFO - 🔗 Setting up Enter key binding for CustomTkinter...
2025-06-19 02:11:03,130 - UltraProfessionalInterface - INFO - ✅ Enter key bound to CustomTkinter internal widget
2025-06-19 02:11:03,130 - UltraProfessionalInterface - INFO - ✅ Input entry focused
2025-06-19 02:11:03,131 - UltraProfessionalInterface - INFO - ✅ CustomTkinter internal widget binding successful
2025-06-19 02:11:03,132 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-19 02:11:03,134 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-19 02:11:03,135 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-19 02:11:03,138 - GideonMain - ERROR - Error during initialization: cannot access free variable '_add_enterprise_welcome_messages' where it is not associated with a value in enclosing scope
2025-06-19 02:11:10,834 - UltraProfessionalInterface - INFO - ⌨️ Enter key pressed!
2025-06-19 02:11:10,877 - UltraProfessionalInterface - INFO - 🔥 _send_message called!
2025-06-19 02:11:10,877 - UltraProfessionalInterface - INFO - 📝 Message retrieved: 'hi'
2025-06-19 02:11:10,904 - UltraProfessionalInterface - INFO - ✅ Input field cleared
2025-06-19 02:11:10,904 - UltraProfessionalInterface - INFO - 💬 Processing message: 'hi'
2025-06-19 02:11:10,907 - UltraProfessionalInterface - INFO - ✅ Message inserted: You
2025-06-19 02:11:10,920 - UltraProfessionalInterface - INFO - ✅ Auto-scroll applied
2025-06-19 02:11:10,926 - UltraProfessionalInterface - INFO - ✅ Display updated successfully
2025-06-19 02:11:10,926 - UltraProfessionalInterface - INFO - ✅ Message successfully displayed: You - hi...
2025-06-19 02:11:10,985 - UltraProfessionalInterface - INFO - 🌍 Detected input language: en
2025-06-19 02:11:10,987 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-19 02:11:10,988 - AIEngine - INFO - Generating response for: 'hi'
2025-06-19 02:11:10,990 - AIEngine - INFO - Attempting LLM response generation...
2025-06-19 02:11:10,990 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-19 02:11:10,991 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-19 02:11:10,991 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-19 02:11:10,991 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-19 02:11:15,631 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-19 02:11:15,815 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
2025-06-19 02:11:35,914 - CompactChatManager - INFO - Compact chat window hidden
2025-06-19 02:11:35,944 - UltraProfessionalInterface - INFO - ✅ Message inserted: System
2025-06-19 02:11:35,993 - UltraProfessionalInterface - INFO - ✅ Auto-scroll applied
2025-06-19 02:11:36,089 - UltraProfessionalInterface - INFO - ✅ Display updated successfully
2025-06-19 02:11:36,089 - UltraProfessionalInterface - INFO - ✅ Message successfully displayed: System - 💬 Compact chat closed. Main wi...
2025-06-19 02:13:25,243 - AIEngine - INFO - LLM response generated successfully: Hello there! How can I assist you today?...
2025-06-19 02:13:25,249 - AIEngine - INFO - LLM generated successful response: Hello there! How can I assist you today?...
2025-06-19 02:13:25,267 - AIEngine - INFO - Final response generated: Hello there! How can I assist you today?...
2025-06-19 02:13:25,268 - UltraProfessionalInterface - INFO - 🤖 AI response generated: Hello there! How can I assist you today?...
2025-06-19 02:13:26,384 - UltraProfessionalInterface - ERROR - Error processing message: main thread is not in main loop
2025-06-19 02:26:55,005 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant Enterprise Edition
2025-06-19 02:27:06,089 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-19 02:27:06,095 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-19 02:27:06,095 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-19 02:27:06,095 - MemorySystem - INFO - Memory system initialized successfully
2025-06-19 02:27:06,096 - ModelManager - INFO - No existing models config found
2025-06-19 02:27:06,097 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-19 02:27:06,097 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-19 02:27:06,098 - AIEngine - INFO - Loaded 31 learned response patterns
2025-06-19 02:27:06,098 - AIEngine - INFO - Initializing LLM backends...
2025-06-19 02:27:06,099 - AIEngine - INFO - ✅ Ollama backend available
2025-06-19 02:27:06,099 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-19 02:27:06,099 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-19 02:27:06,100 - AIEngine - INFO - ✅ Transformers backend available
2025-06-19 02:27:06,100 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-19 02:27:06,100 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-19 02:27:06,360 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-19 02:27:06,361 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-19 02:27:06,362 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-19 02:27:06,362 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-19 02:27:06,363 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-19 02:27:06,363 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-19 02:27:06,363 - AIEngine - INFO - AI Engine initialized successfully
2025-06-19 02:27:06,474 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Speakers (DualSense Wireless Co', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-19 02:27:06,536 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-19 02:27:06,675 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-19 02:27:06,675 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-19 02:27:09,694 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 46.52180432696139
2025-06-19 02:27:09,711 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-19 02:27:09,712 - STTEngine - INFO -    Energy threshold: 150
2025-06-19 02:27:09,712 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-19 02:27:09,712 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-19 02:27:09,712 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-19 02:27:09,712 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-19 02:27:09,712 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-19 02:27:09,712 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-19 02:27:09,713 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-19 02:27:09,957 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-19 02:27:09,957 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-19 02:27:09,957 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-19 02:27:13,231 - STTEngine - INFO - Testing microphone... Say something!
2025-06-19 02:27:16,256 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-19 02:27:20,472 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-19 02:27:20,473 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-19 02:27:20,474 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-19 02:27:20,474 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-19 02:27:20,628 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-19 02:27:20,903 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-19 02:27:21,036 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-19 02:27:21,228 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-19 02:27:21,228 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-19 02:27:21,248 - UltraProfessionalInterface - INFO - ✅ Input entry created and tested successfully
2025-06-19 02:27:21,259 - UltraProfessionalInterface - INFO - ✅ Send button created with explicit command binding
2025-06-19 02:27:21,266 - UltraProfessionalInterface - INFO - 🔗 Setting up Enter key binding for CustomTkinter...
2025-06-19 02:27:21,266 - UltraProfessionalInterface - INFO - ✅ Enter key bound to CustomTkinter internal widget
2025-06-19 02:27:21,266 - UltraProfessionalInterface - INFO - ✅ Input entry focused
2025-06-19 02:27:21,266 - UltraProfessionalInterface - INFO - ✅ CustomTkinter internal widget binding successful
2025-06-19 02:27:21,268 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-19 02:27:21,274 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-19 02:27:21,274 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-19 02:27:21,277 - GideonMain - ERROR - Error during initialization: cannot access free variable '_add_enterprise_welcome_messages' where it is not associated with a value in enclosing scope
2025-06-19 02:54:15,279 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-19 02:54:15,693 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
