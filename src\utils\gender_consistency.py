#!/usr/bin/env python3
"""
Gender Consistency System for Gideon AI
Ensures all responses use female pronouns and identity
"""

import re
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

@dataclass
class PronounReplacement:
    """Pronoun replacement rule"""
    pattern: str
    replacement: str
    case_sensitive: bool = False

class GenderConsistencyProcessor:
    """Process text to ensure female gender consistency"""
    
    def __init__(self):
        # Pronoun replacement rules
        self.pronoun_rules = [
            # Basic pronouns
            PronounReplacement(r'\bhe\b', 'she'),
            PronounReplacement(r'\bHe\b', 'She'),
            PronounReplacement(r'\bHE\b', 'SHE'),
            
            PronounReplacement(r'\bhim\b', 'her'),
            PronounReplacement(r'\bHim\b', 'Her'),
            PronounReplacement(r'\bHIM\b', 'HER'),
            
            PronounReplacement(r'\bhis\b', 'her'),
            PronounReplacement(r'\bHis\b', 'Her'),
            PronounReplacement(r'\bHIS\b', 'HER'),
            
            PronounReplacement(r'\bhimself\b', 'herself'),
            PronounReplacement(r'\bHimself\b', 'Herself'),
            PronounReplacement(r'\bHIMSELF\b', 'HERSELF'),
            
            # Possessive forms
            PronounReplacement(r'\bhis own\b', 'her own'),
            PronounReplacement(r'\bHis own\b', 'Her own'),
            
            # Common phrases
            PronounReplacement(r'\bhe is\b', 'she is'),
            PronounReplacement(r'\bHe is\b', 'She is'),
            PronounReplacement(r'\bhe\'s\b', 'she\'s'),
            PronounReplacement(r'\bHe\'s\b', 'She\'s'),
            
            PronounReplacement(r'\bhe was\b', 'she was'),
            PronounReplacement(r'\bHe was\b', 'She was'),
            
            PronounReplacement(r'\bhe will\b', 'she will'),
            PronounReplacement(r'\bHe will\b', 'She will'),
            PronounReplacement(r'\bhe\'ll\b', 'she\'ll'),
            PronounReplacement(r'\bHe\'ll\b', 'She\'ll'),
            
            PronounReplacement(r'\bhe would\b', 'she would'),
            PronounReplacement(r'\bHe would\b', 'She would'),
            PronounReplacement(r'\bhe\'d\b', 'she\'d'),
            PronounReplacement(r'\bHe\'d\b', 'She\'d'),
            
            PronounReplacement(r'\bhe has\b', 'she has'),
            PronounReplacement(r'\bHe has\b', 'She has'),
            
            PronounReplacement(r'\bhe can\b', 'she can'),
            PronounReplacement(r'\bHe can\b', 'She can'),
            
            PronounReplacement(r'\bhe could\b', 'she could'),
            PronounReplacement(r'\bHe could\b', 'She could'),
            
            PronounReplacement(r'\bhe should\b', 'she should'),
            PronounReplacement(r'\bHe should\b', 'She should'),
            
            PronounReplacement(r'\bhe might\b', 'she might'),
            PronounReplacement(r'\bHe might\b', 'She might'),
            
            PronounReplacement(r'\bhe must\b', 'she must'),
            PronounReplacement(r'\bHe must\b', 'She must'),
        ]
        
        # Identity replacement rules
        self.identity_rules = [
            # Self-reference corrections
            PronounReplacement(r'\bI am a male\b', 'I am a female'),
            PronounReplacement(r'\bI am a man\b', 'I am a woman'),
            PronounReplacement(r'\bI am a guy\b', 'I am a woman'),
            PronounReplacement(r'\bI am a boy\b', 'I am a woman'),
            PronounReplacement(r'\bI\'m a male\b', 'I\'m a female'),
            PronounReplacement(r'\bI\'m a man\b', 'I\'m a woman'),
            PronounReplacement(r'\bI\'m a guy\b', 'I\'m a woman'),
            PronounReplacement(r'\bI\'m a boy\b', 'I\'m a woman'),
            
            # Assistant identity
            PronounReplacement(r'\bI am an AI assistant\b', 'I am Gideon, your AI assistant'),
            PronounReplacement(r'\bI\'m an AI assistant\b', 'I\'m Gideon, your AI assistant'),
            PronounReplacement(r'\bI am an AI\b', 'I am Gideon, an AI'),
            PronounReplacement(r'\bI\'m an AI\b', 'I\'m Gideon, an AI'),
            
            # Name consistency - more precise patterns to avoid duplication
            PronounReplacement(r'\bMy name is (?!Gideon)[^,.!?]*', 'My name is Gideon'),
            PronounReplacement(r'\bI\'m called (?!Gideon)[^,.!?]*', 'I\'m called Gideon'),
            PronounReplacement(r'\bYou can call me (?!Gideon)[^,.!?]*', 'You can call me Gideon'),
        ]
        
        # Contextual patterns that need special handling
        self.contextual_patterns = [
            # Avoid changing "he" when referring to other people
            (r'\b(the user|the person|someone|anyone|everyone|no one)\s+he\b', False),
            (r'\b(if|when|where|while)\s+he\b', False),
            (r'\bhe\s+(said|told|asked|mentioned|explained)\b', False),
        ]
    
    def process_response(self, text: str) -> str:
        """Process response text for gender consistency"""
        if not text:
            return text
        
        processed_text = text
        
        # Apply identity rules first
        processed_text = self._apply_identity_rules(processed_text)
        
        # Apply pronoun rules with context awareness
        processed_text = self._apply_pronoun_rules(processed_text)
        
        # Final cleanup
        processed_text = self._final_cleanup(processed_text)
        
        return processed_text
    
    def _apply_identity_rules(self, text: str) -> str:
        """Apply identity-specific rules"""
        result = text
        
        for rule in self.identity_rules:
            if rule.case_sensitive:
                result = re.sub(rule.pattern, rule.replacement, result)
            else:
                result = re.sub(rule.pattern, rule.replacement, result, flags=re.IGNORECASE)
        
        return result
    
    def _apply_pronoun_rules(self, text: str) -> str:
        """Apply pronoun rules with context awareness"""
        result = text
        
        # Check for contextual patterns that should be preserved
        preserved_sections = self._identify_preserved_sections(text)
        
        for rule in self.pronoun_rules:
            # Apply rule while preserving certain contexts
            result = self._apply_rule_with_context(result, rule, preserved_sections)
        
        return result
    
    def _identify_preserved_sections(self, text: str) -> List[Tuple[int, int]]:
        """Identify text sections that should preserve original pronouns"""
        preserved = []
        
        for pattern, _ in self.contextual_patterns:
            for match in re.finditer(pattern, text, re.IGNORECASE):
                preserved.append((match.start(), match.end()))
        
        return preserved
    
    def _apply_rule_with_context(self, text: str, rule: PronounReplacement, 
                                preserved_sections: List[Tuple[int, int]]) -> str:
        """Apply pronoun rule while avoiding preserved sections"""
        result = text
        flags = 0 if rule.case_sensitive else re.IGNORECASE
        
        # Find all matches
        matches = list(re.finditer(rule.pattern, text, flags))
        
        # Apply replacements from end to start to maintain positions
        for match in reversed(matches):
            start, end = match.span()
            
            # Check if this match is in a preserved section
            should_preserve = any(
                pstart <= start < pend or pstart < end <= pend
                for pstart, pend in preserved_sections
            )
            
            if not should_preserve:
                result = result[:start] + rule.replacement + result[end:]
        
        return result
    
    def _final_cleanup(self, text: str) -> str:
        """Final cleanup and consistency checks"""
        result = text
        
        # Fix common grammar issues after pronoun replacement
        grammar_fixes = [
            (r'\bher is\b', 'she is'),
            (r'\bHer is\b', 'She is'),
            (r'\bher was\b', 'she was'),
            (r'\bHer was\b', 'She was'),
            (r'\bher will\b', 'she will'),
            (r'\bHer will\b', 'She will'),
            (r'\bher can\b', 'she can'),
            (r'\bHer can\b', 'She can'),
        ]
        
        for pattern, replacement in grammar_fixes:
            result = re.sub(pattern, replacement, result)
        
        # Ensure Gideon identity consistency
        gideon_patterns = [
            (r'\bI am an? (assistant|AI|bot|chatbot|system)\b', 'I am Gideon, your AI assistant'),
            (r'\bI\'m an? (assistant|AI|bot|chatbot|system)\b', 'I\'m Gideon, your AI assistant'),
        ]
        
        for pattern, replacement in gideon_patterns:
            result = re.sub(pattern, replacement, result, flags=re.IGNORECASE)
        
        return result
    
    def validate_response(self, text: str) -> Dict[str, Any]:
        """Validate response for gender consistency"""
        issues = []
        
        # Check for male pronouns
        male_pronouns = re.findall(r'\b(he|him|his|himself)\b', text, re.IGNORECASE)
        if male_pronouns:
            issues.append(f"Found male pronouns: {set(male_pronouns)}")
        
        # Check for male identity references
        male_identity = re.findall(r'\b(I am a (male|man|guy|boy))\b', text, re.IGNORECASE)
        if male_identity:
            issues.append(f"Found male identity references: {male_identity}")
        
        # Check for generic assistant references
        generic_refs = re.findall(r'\b(I am an? (assistant|AI|bot))\b', text, re.IGNORECASE)
        if generic_refs and 'Gideon' not in text:
            issues.append("Missing Gideon identity reference")
        
        return {
            'is_consistent': len(issues) == 0,
            'issues': issues,
            'processed_text': self.process_response(text)
        }


class GideonIdentityEnforcer:
    """Enforce Gideon's female identity across all interactions"""
    
    def __init__(self):
        self.processor = GenderConsistencyProcessor()
        self.identity_prompts = {
            'system_prompt': (
                "You are Gideon, a female AI assistant. Always refer to yourself "
                "using female pronouns (she/her). You are helpful, intelligent, "
                "and professional. Your name is Gideon and you identify as female."
            ),
            'correction_prompt': (
                "Remember: You are Gideon, a female AI assistant. Use 'she/her' "
                "pronouns when referring to yourself."
            )
        }
    
    def process_ai_response(self, response: str) -> str:
        """Process AI response to ensure gender consistency"""
        return self.processor.process_response(response)
    
    def validate_and_correct(self, response: str) -> Tuple[str, bool]:
        """Validate response and return corrected version"""
        validation = self.processor.validate_response(response)
        
        corrected_response = validation['processed_text']
        is_consistent = validation['is_consistent']
        
        return corrected_response, is_consistent
    
    def get_system_prompt(self) -> str:
        """Get system prompt for AI model"""
        return self.identity_prompts['system_prompt']
    
    def get_correction_prompt(self) -> str:
        """Get correction prompt for inconsistent responses"""
        return self.identity_prompts['correction_prompt']


# Global instance for easy access
gideon_identity = GideonIdentityEnforcer()
