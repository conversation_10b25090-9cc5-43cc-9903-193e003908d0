#!/usr/bin/env python3
"""
Gideon AI Assistant - Emergency Safe Mode

Ultra-minimal startup mode that bypasses all potentially problematic components.
Designed to prevent system freezes and allow progressive feature loading.
"""

import sys
import traceback
import time
import gc
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

print("🚨 GIDEON AI ASSISTANT - EMERGENCY SAFE MODE")
print("=" * 60)
print("This mode bypasses all potentially problematic components")
print("and loads only the absolute essentials.")
print("=" * 60)

class EmergencyLogger:
    """Ultra-lightweight logger for emergency mode"""
    
    def __init__(self, name):
        self.name = name
        self.log_file = Path("data/logs/emergency.log")
        self.log_file.parent.mkdir(parents=True, exist_ok=True)
        
    def log(self, level, message):
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"{timestamp} - {self.name} - {level} - {message}\n"
        print(f"[{level}] {message}")
        
        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(log_entry)
        except:
            pass  # Don't let logging errors crash emergency mode
    
    def info(self, message):
        self.log("INFO", message)
    
    def error(self, message):
        self.log("ERROR", message)
    
    def warning(self, message):
        self.log("WARNING", message)

class EmergencyInterface:
    """Ultra-minimal chat interface for emergency mode"""
    
    def __init__(self, logger):
        self.logger = logger
        self.root = None
        self.chat_display = None
        self.input_entry = None
        self.send_button = None
        self.feature_buttons = {}
        
    def create_emergency_window(self):
        """Create emergency safe window"""
        try:
            import customtkinter as ctk
            
            # Create root window
            self.root = ctk.CTk()
            self.root.title("Gideon AI Assistant - Emergency Safe Mode")
            self.root.geometry("800x600")
            
            # Configure grid
            self.root.grid_columnconfigure(0, weight=1)
            self.root.grid_rowconfigure(1, weight=1)
            
            # Status frame
            status_frame = ctk.CTkFrame(self.root)
            status_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=5)
            
            status_label = ctk.CTkLabel(
                status_frame, 
                text="🚨 EMERGENCY SAFE MODE - Basic Chat Only",
                font=ctk.CTkFont(size=16, weight="bold")
            )
            status_label.pack(pady=10)
            
            # Chat frame
            chat_frame = ctk.CTkFrame(self.root)
            chat_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=5)
            chat_frame.grid_columnconfigure(0, weight=1)
            chat_frame.grid_rowconfigure(0, weight=1)
            
            # Chat display
            self.chat_display = ctk.CTkTextbox(
                chat_frame,
                wrap="word",
                font=ctk.CTkFont(size=12)
            )
            self.chat_display.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
            
            # Input frame
            input_frame = ctk.CTkFrame(self.root)
            input_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=5)
            input_frame.grid_columnconfigure(0, weight=1)
            
            # Input entry
            self.input_entry = ctk.CTkEntry(
                input_frame,
                placeholder_text="Type your message here...",
                font=ctk.CTkFont(size=12)
            )
            self.input_entry.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
            self.input_entry.bind("<Return>", lambda e: self.send_message())
            
            # Send button
            self.send_button = ctk.CTkButton(
                input_frame,
                text="Send",
                command=self.send_message,
                width=80
            )
            self.send_button.grid(row=0, column=1, padx=5, pady=5)
            
            # Feature loading frame
            feature_frame = ctk.CTkFrame(self.root)
            feature_frame.grid(row=3, column=0, sticky="ew", padx=10, pady=5)
            
            feature_label = ctk.CTkLabel(
                feature_frame,
                text="Progressive Feature Loading (Load features one by one):",
                font=ctk.CTkFont(size=12, weight="bold")
            )
            feature_label.pack(pady=5)
            
            # Feature buttons
            button_frame = ctk.CTkFrame(feature_frame)
            button_frame.pack(fill="x", padx=5, pady=5)
            
            features = [
                ("Load AI Engine", self.load_ai_engine),
                ("Load Speech", self.load_speech),
                ("Load Models", self.load_models),
                ("Load Performance Monitor", self.load_performance),
                ("Enable Voice Chat", self.enable_voice)
            ]
            
            for i, (text, command) in enumerate(features):
                btn = ctk.CTkButton(
                    button_frame,
                    text=text,
                    command=command,
                    width=150,
                    height=30
                )
                btn.grid(row=0, column=i, padx=2, pady=2)
                self.feature_buttons[text] = btn
            
            # Add initial messages
            self.add_message("System", "🚨 Emergency Safe Mode Active")
            self.add_message("System", "✅ Basic chat interface loaded successfully")
            self.add_message("System", "💡 Use the buttons below to load features progressively")
            self.add_message("System", "⚠️ If any feature causes freezing, restart in safe mode")
            
            self.logger.info("Emergency interface created successfully")
            return self.root
            
        except Exception as e:
            self.logger.error(f"Failed to create emergency interface: {e}")
            raise
    
    def add_message(self, sender, message):
        """Add message to chat display"""
        try:
            timestamp = time.strftime("%H:%M:%S")
            formatted_message = f"[{timestamp}] {sender}: {message}\n"
            
            self.chat_display.insert("end", formatted_message)
            self.chat_display.see("end")
            
        except Exception as e:
            self.logger.error(f"Error adding message: {e}")
    
    def send_message(self):
        """Send user message"""
        try:
            message = self.input_entry.get().strip()
            if not message:
                return
            
            # Clear input
            self.input_entry.delete(0, "end")
            
            # Add user message
            self.add_message("You", message)
            
            # Simple rule-based response
            response = self.get_emergency_response(message)
            self.add_message("Gideon", response)
            
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            self.add_message("System", f"Error: {e}")
    
    def get_emergency_response(self, message):
        """Get emergency rule-based response"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["hello", "hi", "hey"]):
            return "Hello! I'm running in emergency safe mode. Basic chat is available."
        elif any(word in message_lower for word in ["help", "what", "how"]):
            return "I'm in emergency safe mode. Use the buttons below to load features progressively."
        elif any(word in message_lower for word in ["status", "mode"]):
            return "Currently running in Emergency Safe Mode - only basic chat is active."
        elif any(word in message_lower for word in ["load", "enable"]):
            return "Use the feature loading buttons below to enable additional capabilities."
        else:
            return "I'm in emergency safe mode. I can only provide basic responses right now."
    
    def load_ai_engine(self):
        """Load AI engine progressively"""
        try:
            self.add_message("System", "🧠 Loading AI Engine...")
            self.feature_buttons["Load AI Engine"].configure(state="disabled")
            
            # Import and initialize AI engine safely
            from src.core.ai_engine import AIEngine
            self.ai_engine = AIEngine(None)
            self.ai_engine.initialize_minimal()
            
            self.add_message("System", "✅ AI Engine loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load AI engine: {e}")
            self.add_message("System", f"❌ Failed to load AI Engine: {e}")
    
    def load_speech(self):
        """Load speech engines progressively"""
        try:
            self.add_message("System", "🎤 Loading Speech Engines...")
            self.feature_buttons["Load Speech"].configure(state="disabled")
            
            # This is where speech loading would cause freezes
            self.add_message("System", "⚠️ Speech loading disabled in emergency mode")
            
        except Exception as e:
            self.logger.error(f"Failed to load speech: {e}")
            self.add_message("System", f"❌ Failed to load Speech: {e}")
    
    def load_models(self):
        """Load AI models progressively"""
        try:
            self.add_message("System", "🤖 Loading AI Models...")
            self.feature_buttons["Load Models"].configure(state="disabled")
            
            # This is where model loading would cause freezes
            self.add_message("System", "⚠️ Model loading disabled in emergency mode")
            
        except Exception as e:
            self.logger.error(f"Failed to load models: {e}")
            self.add_message("System", f"❌ Failed to load Models: {e}")
    
    def load_performance(self):
        """Load performance monitoring progressively"""
        try:
            self.add_message("System", "📊 Loading Performance Monitor...")
            self.feature_buttons["Load Performance Monitor"].configure(state="disabled")
            
            self.add_message("System", "✅ Performance Monitor loaded (static mode)")
            
        except Exception as e:
            self.logger.error(f"Failed to load performance monitor: {e}")
            self.add_message("System", f"❌ Failed to load Performance Monitor: {e}")
    
    def enable_voice(self):
        """Enable voice chat progressively"""
        try:
            self.add_message("System", "🎙️ Enabling Voice Chat...")
            self.feature_buttons["Enable Voice Chat"].configure(state="disabled")
            
            self.add_message("System", "⚠️ Voice chat disabled in emergency mode")
            
        except Exception as e:
            self.logger.error(f"Failed to enable voice chat: {e}")
            self.add_message("System", f"❌ Failed to enable Voice Chat: {e}")
    
    def run(self):
        """Run the emergency interface"""
        try:
            self.logger.info("Starting emergency interface main loop")
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"Error in main loop: {e}")

def main():
    """Emergency safe mode main entry point"""
    print("🚨 Starting Emergency Safe Mode...")
    
    # Create emergency logger
    logger = EmergencyLogger("Emergency")
    logger.info("Emergency Safe Mode started")
    
    try:
        # Check for CustomTkinter (minimal check)
        try:
            import customtkinter
            logger.info("CustomTkinter available")
        except ImportError:
            print("❌ CustomTkinter required for emergency mode")
            print("Install with: pip install customtkinter")
            input("Press Enter to exit...")
            return 1
        
        # Create essential directories
        Path("data/logs").mkdir(parents=True, exist_ok=True)
        Path("data/cache").mkdir(parents=True, exist_ok=True)
        
        # Memory cleanup
        gc.collect()
        logger.info("Memory cleanup completed")
        
        # Create emergency interface
        interface = EmergencyInterface(logger)
        root = interface.create_emergency_window()
        
        logger.info("Emergency Safe Mode ready")
        print("✅ Emergency Safe Mode ready - Window should appear")
        
        # Run interface
        interface.run()
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Emergency mode interrupted by user")
        return 0
        
    except Exception as e:
        logger.error(f"Emergency mode fatal error: {e}")
        print(f"❌ Emergency mode failed: {e}")
        traceback.print_exc()
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
