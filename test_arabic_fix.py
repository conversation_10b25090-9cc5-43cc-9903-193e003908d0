#!/usr/bin/env python3
"""
Test script to verify Arabic language response fix
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def test_arabic_response():
    """Test Arabic response functionality"""
    print("Testing Arabic Response Fix...")
    print("=" * 50)
    
    try:
        # Import required modules
        from src.core.ai_engine import AIEngine
        from src.utils.config import Config
        
        # Initialize config and AI engine
        config = Config()
        ai_engine = AIEngine()
        ai_engine.initialize()
        
        # Test 1: Check default language
        print("Test 1: Checking default language...")
        print(f"Config language: {config.get('app.language', 'not set')}")
        print(f"AI Engine response language: {ai_engine.response_language}")
        
        # Test 2: Set response language to Arabic
        print("\nTest 2: Setting response language to Arabic...")
        ai_engine.set_response_language("ar")
        print(f"Response language set to: {ai_engine.response_language}")
        
        # Test 3: Test Arabic greeting
        print("\nTest 3: Testing Arabic greeting response...")
        arabic_input = "مرحبا"
        response = ai_engine.generate_response(arabic_input)
        print(f"Input: {arabic_input}")
        print(f"Response: {response}")
        
        # Test 4: Test Arabic identity question
        print("\nTest 4: Testing Arabic identity question...")
        arabic_question = "ما اسمك؟"
        response = ai_engine.generate_response(arabic_question)
        print(f"Input: {arabic_question}")
        print(f"Response: {response}")
        
        # Test 5: Test English to Arabic switch
        print("\nTest 5: Testing English input with Arabic response...")
        english_input = "hello"
        response = ai_engine.generate_response(english_input)
        print(f"Input: {english_input}")
        print(f"Response: {response}")
        
        # Test 6: Switch to English and test
        print("\nTest 6: Switching to English response...")
        ai_engine.set_response_language("en")
        response = ai_engine.generate_response("hello")
        print(f"Input: hello")
        print(f"Response: {response}")
        
        print("\nAll tests completed!")
        print("If you see Arabic responses in tests 3-5, the fix is working!")
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_arabic_response()