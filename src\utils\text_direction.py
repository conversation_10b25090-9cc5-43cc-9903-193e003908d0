"""
Text Direction Manager for Gideon AI Assistant
<PERSON>les <PERSON>TL (Arabic) and <PERSON><PERSON> (English) text display
"""

import tkinter as tk
from typing import <PERSON><PERSON>, Dict, Any

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False


class TextDirectionManager:
    """Manages text direction for bilingual support"""
    
    def __init__(self):
        self.rtl_languages = {'ar', 'he', 'fa', 'ur'}  # Arabic, Hebrew, Persian, Urdu
        
    def detect_text_direction(self, text: str) -> str:
        """Detect if text should be displayed RTL or LTR"""
        if not text or not text.strip():
            return "ltr"
        
        # Count RTL characters (Arabic, Hebrew, etc.)
        rtl_chars = 0
        total_chars = 0
        
        for char in text:
            if char.isalpha():
                total_chars += 1
                # Arabic range
                if '\u0600' <= char <= '\u06FF':
                    rtl_chars += 1
                # Hebrew range
                elif '\u0590' <= char <= '\u05FF':
                    rtl_chars += 1
                # Persian/Urdu additional ranges
                elif '\uFB50' <= char <= '\uFDFF' or '\uFE70' <= char <= '\uFEFF':
                    rtl_chars += 1
        
        if total_chars > 0 and rtl_chars / total_chars > 0.3:
            return "rtl"
        return "ltr"
    
    def format_text_for_display(self, text: str) -> Tuple[str, str]:
        """Format text for proper display and return (formatted_text, direction)"""
        direction = self.detect_text_direction(text)
        
        if direction == "rtl" and ARABIC_SUPPORT:
            try:
                # Reshape Arabic text for proper display
                reshaped_text = arabic_reshaper.reshape(text)
                # Apply bidirectional algorithm
                formatted_text = get_display(reshaped_text)
            except Exception:
                formatted_text = text
        else:
            formatted_text = text
        
        return formatted_text, direction
    
    def configure_widget_direction(self, widget, text: str = None, direction: str = None):
        """Configure widget for proper text direction"""
        if direction is None and text:
            direction = self.detect_text_direction(text)
        elif direction is None:
            direction = "ltr"
        
        try:
            if direction == "rtl":
                # Configure for RTL display
                if hasattr(widget, 'configure'):
                    widget.configure(justify='right')
                if hasattr(widget, 'tag_configure'):
                    widget.tag_configure("rtl", justify='right')
            else:
                # Configure for LTR display
                if hasattr(widget, 'configure'):
                    widget.configure(justify='left')
                if hasattr(widget, 'tag_configure'):
                    widget.tag_configure("ltr", justify='left')
        except Exception:
            pass  # Ignore if widget doesn't support these configurations
    
    def get_text_alignment(self, text: str) -> str:
        """Get text alignment based on direction"""
        direction = self.detect_text_direction(text)
        return "right" if direction == "rtl" else "left"
    
    def get_text_anchor(self, text: str) -> str:
        """Get text anchor for canvas/label positioning"""
        direction = self.detect_text_direction(text)
        return "ne" if direction == "rtl" else "nw"
    
    def format_message_for_chat(self, sender: str, message: str, timestamp: str = None) -> Dict[str, Any]:
        """Format message for chat display with proper direction"""
        formatted_message, direction = self.format_text_for_display(message)
        
        # Determine message alignment
        alignment = "right" if direction == "rtl" else "left"
        
        # Create formatted message data
        message_data = {
            'sender': sender,
            'original_message': message,
            'formatted_message': formatted_message,
            'direction': direction,
            'alignment': alignment,
            'timestamp': timestamp,
            'is_rtl': direction == "rtl"
        }
        
        return message_data
    
    def apply_direction_to_text_widget(self, widget, text: str, tag_name: str = None):
        """Apply proper direction formatting to text widget"""
        formatted_text, direction = self.format_text_for_display(text)
        
        if not tag_name:
            tag_name = f"dir_{direction}"
        
        # Configure tag for direction
        if hasattr(widget, 'tag_configure'):
            if direction == "rtl":
                widget.tag_configure(tag_name, justify='right', lmargin1=20, lmargin2=20)
            else:
                widget.tag_configure(tag_name, justify='left', lmargin1=20, lmargin2=20)
        
        return formatted_text, tag_name
    
    def create_directional_message(self, widget, sender: str, message: str, 
                                 timestamp: str = None, sender_color: str = None,
                                 message_color: str = None) -> str:
        """Create a properly formatted directional message for text widgets"""
        
        # Format the message content
        formatted_message, direction = self.format_text_for_display(message)
        
        # Create timestamp if not provided
        if not timestamp:
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")
        
        # Determine message structure based on direction
        if direction == "rtl":
            # RTL: timestamp - sender - message (right-aligned)
            full_message = f"{formatted_message} :{sender} [{timestamp}]"
            tag_name = "rtl_message"
            justify = 'right'
        else:
            # LTR: timestamp - sender - message (left-aligned)
            full_message = f"[{timestamp}] {sender}: {formatted_message}"
            tag_name = "ltr_message"
            justify = 'left'
        
        # Configure tag if widget supports it
        if hasattr(widget, 'tag_configure'):
            widget.tag_configure(tag_name, justify=justify)
            if sender_color:
                widget.tag_configure(f"{tag_name}_sender", foreground=sender_color, justify=justify)
            if message_color:
                widget.tag_configure(f"{tag_name}_message", foreground=message_color, justify=justify)
        
        return full_message, tag_name
    
    def is_arabic_text(self, text: str) -> bool:
        """Check if text contains Arabic characters"""
        return any('\u0600' <= char <= '\u06FF' for char in text)
    
    def is_hebrew_text(self, text: str) -> bool:
        """Check if text contains Hebrew characters"""
        return any('\u0590' <= char <= '\u05FF' for char in text)
    
    def get_reading_direction(self, text: str) -> str:
        """Get reading direction for text"""
        return self.detect_text_direction(text)


# Global instance
text_direction_manager = TextDirectionManager()


def detect_text_direction(text: str) -> str:
    """Convenience function to detect text direction"""
    return text_direction_manager.detect_text_direction(text)


def format_text_for_display(text: str) -> Tuple[str, str]:
    """Convenience function to format text for display"""
    return text_direction_manager.format_text_for_display(text)


def configure_widget_direction(widget, text: str = None, direction: str = None):
    """Convenience function to configure widget direction"""
    return text_direction_manager.configure_widget_direction(widget, text, direction)


def create_directional_message(widget, sender: str, message: str, **kwargs) -> str:
    """Convenience function to create directional message"""
    return text_direction_manager.create_directional_message(widget, sender, message, **kwargs)
