"""
Hybrid AI Engine for Gideon - Combines rule-based responses with local LLM models
"""

import random
import re
import time
import json
import subprocess
import threading
from datetime import datetime
from typing import List, Dict, Any, Optional, Callable
from pathlib import Path

from src.utils.logger import Gideon<PERSON>ogger
from src.utils.config import Config

# Import ultra-low latency system
try:
    from src.optimization.ultra_low_latency import ultra_low_latency_manager
    ULTRA_LOW_LATENCY_AVAILABLE = True
except ImportError:
    ULTRA_LOW_LATENCY_AVAILABLE = False
from src.utils.i18n import get_i18n

# Try to import LLM libraries
try:
    import ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False

try:
    from llama_cpp import Llama
    LLAMA_CPP_AVAILABLE = True
except ImportError:
    LLAMA_CPP_AVAILABLE = False

try:
    from ctransformers import AutoModelForCausalLM as CTransformersModel
    CTRANSFORMERS_AVAILABLE = True
except ImportError:
    CTRA<PERSON>FORMERS_AVAILABLE = False

try:
    import torch
    from transformers import <PERSON><PERSON>oken<PERSON>, AutoModelForCausalLM, pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False


class LLMBackend:
    """Base class for LLM backends"""

    def __init__(self, name: str):
        self.name = name
        self.logger = GideonLogger(f"LLM-{name}")
        self.is_loaded = False
        self.model = None

    def load_model(self, model_path: str, **kwargs) -> bool:
        """Load the model"""
        raise NotImplementedError

    def generate_response(self, prompt: str, max_tokens: int = 150, temperature: float = 0.7) -> str:
        """Generate response from the model"""
        raise NotImplementedError

    def is_available(self) -> bool:
        """Check if backend is available"""
        return self.is_loaded


class OllamaBackend(LLMBackend):
    """Ollama backend for local LLM models"""

    def __init__(self):
        super().__init__("Ollama")
        self.client = None
        self.current_model = None

    def load_model(self, model_name: str, **kwargs) -> bool:
        """Load Ollama model"""
        if not OLLAMA_AVAILABLE:
            self.logger.error("Ollama not available")
            return False

        try:
            self.client = ollama.Client()

            # Check if model exists
            models = self.client.list()
            available_models = [model_obj.model for model_obj in models.models]

            # Try to find the model
            actual_model_name = None

            # First try exact match
            if model_name in available_models:
                actual_model_name = model_name
            else:
                # Try partial matches
                for full_name in available_models:
                    if model_name in full_name or full_name in model_name:
                        actual_model_name = full_name
                        break

            if not actual_model_name:
                self.logger.warning(f"Model {model_name} not found. Available: {available_models}")
                return False

            self.current_model = actual_model_name
            self.is_loaded = True
            self.logger.info(f"Ollama model loaded: {actual_model_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to load Ollama model: {e}")
            return False

    def generate_response(self, prompt: str, max_tokens: int = 150, temperature: float = 0.7) -> str:
        """Generate response using Ollama"""
        if not self.is_loaded or not self.client:
            return ""

        try:
            response = self.client.chat(
                model=self.current_model,
                messages=[{'role': 'user', 'content': prompt}],
                options={
                    'num_predict': max_tokens,
                    'temperature': temperature
                }
            )

            return response['message']['content'].strip()

        except Exception as e:
            self.logger.error(f"Ollama generation error: {e}")
            return ""


class LlamaCppBackend(LLMBackend):
    """llama.cpp backend for GGUF models"""

    def __init__(self):
        super().__init__("LlamaCpp")
        self.llm = None

    def load_model(self, model_path: str, **kwargs) -> bool:
        """Load GGUF model with llama.cpp"""
        if not LLAMA_CPP_AVAILABLE:
            self.logger.error("llama-cpp-python not available")
            return False

        try:
            model_file = Path(model_path)
            if not model_file.exists():
                self.logger.error(f"Model file not found: {model_path}")
                return False

            # Load model with reasonable defaults
            self.llm = Llama(
                model_path=str(model_file),
                n_ctx=2048,  # Context window
                n_threads=4,  # CPU threads
                verbose=False
            )

            self.is_loaded = True
            self.logger.info(f"llama.cpp model loaded: {model_file.name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to load llama.cpp model: {e}")
            return False

    def generate_response(self, prompt: str, max_tokens: int = 150, temperature: float = 0.7) -> str:
        """Generate response using llama.cpp"""
        if not self.is_loaded or not self.llm:
            return ""

        try:
            response = self.llm(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                stop=["Human:", "User:", "\n\n"],
                echo=False
            )

            return response['choices'][0]['text'].strip()

        except Exception as e:
            self.logger.error(f"llama.cpp generation error: {e}")
            return ""


class CTransformersBackend(LLMBackend):
    """CTransformers backend for GGML models"""

    def __init__(self):
        super().__init__("CTransformers")
        self.model = None

    def load_model(self, model_path: str, **kwargs) -> bool:
        """Load GGML model with CTransformers"""
        if not CTRANSFORMERS_AVAILABLE:
            self.logger.error("ctransformers not available")
            return False

        try:
            model_file = Path(model_path)
            if not model_file.exists():
                self.logger.error(f"Model file not found: {model_path}")
                return False

            # Load model with CTransformers
            self.model = CTransformersModel.from_pretrained(
                str(model_file),
                model_type="llama",  # Default to llama, can be overridden
                **kwargs
            )

            self.is_loaded = True
            self.logger.info(f"CTransformers model loaded: {model_file.name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to load CTransformers model: {e}")
            return False

    def generate_response(self, prompt: str, max_tokens: int = 150, temperature: float = 0.7) -> str:
        """Generate response using CTransformers"""
        if not self.is_loaded or not self.model:
            return ""

        try:
            response = self.model(
                prompt,
                max_new_tokens=max_tokens,
                temperature=temperature,
                stop=["Human:", "User:", "\n\n"]
            )

            return response.strip()

        except Exception as e:
            self.logger.error(f"CTransformers generation error: {e}")
            return ""


class TransformersBackend(LLMBackend):
    """Hugging Face Transformers backend"""

    def __init__(self):
        super().__init__("Transformers")
        self.tokenizer = None
        self.model = None
        self.pipeline = None

    def load_model(self, model_name: str, **kwargs) -> bool:
        """Load Transformers model"""
        if not TRANSFORMERS_AVAILABLE:
            self.logger.error("Transformers not available")
            return False

        try:
            self.logger.info(f"Loading Transformers model: {model_name}")

            # Load tokenizer and model
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None
            )

            # Create pipeline
            self.pipeline = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if torch.cuda.is_available() else -1
            )

            self.is_loaded = True
            self.logger.info(f"Transformers model loaded: {model_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to load Transformers model: {e}")
            return False

    def generate_response(self, prompt: str, max_tokens: int = 150, temperature: float = 0.7) -> str:
        """Generate response using Transformers"""
        if not self.is_loaded or not self.pipeline:
            return ""

        try:
            response = self.pipeline(
                prompt,
                max_new_tokens=max_tokens,
                temperature=temperature,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )

            generated_text = response[0]['generated_text']
            # Remove the original prompt from the response
            if generated_text.startswith(prompt):
                generated_text = generated_text[len(prompt):].strip()

            return generated_text

        except Exception as e:
            self.logger.error(f"Transformers generation error: {e}")
            return ""


class AIEngine:
    """Hybrid AI Engine for Gideon - Combines rule-based responses with local LLM models"""

    def __init__(self, memory_system=None):
        self.logger = GideonLogger("AIEngine")
        self.config = Config()
        self.memory_system = memory_system
        self.i18n = get_i18n()

        # Rule-based components
        self.knowledge_base = {}
        self.response_templates = {}
        self.conversation_patterns = {}
        self.learned_responses = {}
        self.user_patterns = {}

        # LLM components
        self.llm_backends = {}
        self.active_backend = None
        self.llm_enabled = False
        self.fallback_to_rules = True

        # Response strategy
        self.use_llm_for_complex = True
        self.complexity_threshold = 0.5

        # Ultra-low latency optimization
        self.ultra_low_latency_enabled = ULTRA_LOW_LATENCY_AVAILABLE

        # Language support for bilingual responses
        self.response_language = "auto"  # auto, en, ar
        self.bilingual_mode = True
        self.language_detection_enabled = True

    def initialize_minimal(self):
        """Initialize AI Engine in minimal mode (no heavy models)"""
        try:
            self.logger.info("Initializing AI Engine (Minimal Mode)...")

            # Set default response language from config
            default_language = self.config.get("app.language", "ar")
            if default_language in ["ar", "en"]:
                self.response_language = default_language

            # Initialize only rule-based components
            self._load_knowledge_base()
            self._load_response_templates()

            # Skip LLM backend initialization
            self.llm_enabled = False
            self.active_backend = None

            # Disable ultra-low latency system
            self.ultra_low_latency_enabled = False

            self.logger.info("AI Engine initialized (Minimal Mode) - Rule-based responses only")

        except Exception as e:
            self.logger.error(f"Failed to initialize AI Engine (Minimal): {e}")
            raise

    def initialize_lightweight(self):
        """Initialize AI Engine in lightweight mode (basic AI only)"""
        try:
            self.logger.info("Initializing AI Engine (Lightweight Mode)...")

            # Set default response language from config
            default_language = self.config.get("app.language", "ar")
            if default_language in ["ar", "en"]:
                self.response_language = default_language

            # Initialize rule-based components
            self._load_knowledge_base()
            self._load_response_templates()

            # Load learned data from memory if available
            if self.memory_system:
                self._load_learned_data()

            # Initialize only essential LLM backends (skip heavy ones)
            self._initialize_essential_backends()

            # Skip heavy model loading
            self.llm_enabled = False

            # Disable ultra-low latency system
            self.ultra_low_latency_enabled = False

            self.logger.info("AI Engine initialized (Lightweight Mode)")

        except Exception as e:
            self.logger.error(f"Failed to initialize AI Engine (Lightweight): {e}")
            raise

    def initialize(self):
        """Initialize the hybrid AI engine"""
        try:
            self.logger.info("Initializing Hybrid AI Engine...")

            # Set default response language from config
            default_language = self.config.get("app.language", "ar")
            if default_language in ["ar", "en"]:
                self.response_language = default_language
                self.logger.info(f"🌍 AI Engine default response language set to: {default_language}")

            # Initialize rule-based components
            self._load_knowledge_base()
            self._load_response_templates()

            # Load learned data from memory
            if self.memory_system:
                self._load_learned_data()

            # Initialize LLM backends
            self._initialize_llm_backends()

            # Try to load a default model
            self._load_default_model()

            # Initialize ultra-low latency system
            if self.ultra_low_latency_enabled:
                self._initialize_ultra_low_latency()

            self.logger.info("AI Engine initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize AI Engine: {e}")
            raise

    def _initialize_essential_backends(self):
        """Initialize only essential LLM backends for lightweight mode"""
        self.logger.info("Initializing essential LLM backends...")

        # Initialize only Ollama backend (most lightweight)
        if OLLAMA_AVAILABLE:
            self.llm_backends['ollama'] = OllamaBackend()
            self.logger.info("✅ Ollama backend available")
        else:
            self.logger.info("⚠️ Ollama backend not available")

        self.logger.info(f"Initialized {len(self.llm_backends)} essential LLM backends")

    def _initialize_llm_backends(self):
        """Initialize available LLM backends"""
        self.logger.info("Initializing LLM backends...")

        # Initialize Ollama backend
        if OLLAMA_AVAILABLE:
            self.llm_backends['ollama'] = OllamaBackend()
            self.logger.info("✅ Ollama backend available")
        else:
            self.logger.info("⚠️ Ollama backend not available")

        # Initialize llama.cpp backend
        if LLAMA_CPP_AVAILABLE:
            self.llm_backends['llamacpp'] = LlamaCppBackend()
            self.logger.info("✅ llama.cpp backend available")
        else:
            self.logger.info("ℹ️ llama.cpp backend not available")

        # Initialize CTransformers backend (alternative to llama.cpp)
        if CTRANSFORMERS_AVAILABLE:
            self.llm_backends['ctransformers'] = CTransformersBackend()
            self.logger.info("✅ CTransformers backend available")
        else:
            self.logger.info("ℹ️ CTransformers backend not available")

        # Initialize Transformers backend
        if TRANSFORMERS_AVAILABLE:
            self.llm_backends['transformers'] = TransformersBackend()
            self.logger.info("✅ Transformers backend available")
        else:
            self.logger.info("ℹ️ Transformers backend not available")

        self.logger.info(f"Initialized {len(self.llm_backends)} LLM backends")

    def _load_default_model(self):
        """Try to load a default model"""
        # Check config for preferred model
        preferred_backend = self.config.get("ai.preferred_backend", "ollama")
        preferred_model = self.config.get("ai.preferred_model", "")

        if preferred_model and preferred_backend in self.llm_backends:
            if self.load_model(preferred_backend, preferred_model):
                self.logger.info(f"Loaded preferred model: {preferred_model} via {preferred_backend}")
                return

        # Try to auto-detect and load available models
        self._auto_detect_models()

    def _auto_detect_models(self):
        """Auto-detect and load available models"""
        self.logger.info("Auto-detecting available models...")

        # Try Ollama first (easiest to use)
        if 'ollama' in self.llm_backends:
            try:
                import ollama
                client = ollama.Client()
                models = client.list()
                if models.models:  # models.models is the list of Model objects
                    # Prefer ultra-large models first, then high-performance models
                    model_preferences = ['qwen3:235b', 'gemma3:27b', 'gemma2:27b', 'dolphin-llama3:70b', 'llama3', 'llama2', 'mistral']

                    selected_model = None

                    # Try preferred models first
                    for pref in model_preferences:
                        for model_obj in models.models:
                            if pref in model_obj.model.lower():
                                selected_model = model_obj.model
                                break
                        if selected_model:
                            break

                    # If no preferred model found, use the first one
                    if not selected_model:
                        selected_model = models.models[0].model

                    self.logger.info(f"Attempting to auto-load: {selected_model}")

                    if self.load_model('ollama', selected_model):
                        self.logger.info(f"Auto-loaded Ollama model: {selected_model}")
                        return
            except Exception as e:
                self.logger.error(f"Error auto-loading Ollama model: {e}")
                import traceback
                self.logger.error(f"Traceback: {traceback.format_exc()}")
                pass

        # Try to find GGUF models for llama.cpp
        if 'llamacpp' in self.llm_backends:
            gguf_paths = self._find_gguf_models()
            if gguf_paths:
                if self.load_model('llamacpp', gguf_paths[0]):
                    self.logger.info(f"Auto-loaded GGUF model: {gguf_paths[0]}")
                    return

        # Try to find GGML models for CTransformers
        if 'ctransformers' in self.llm_backends:
            ggml_paths = self._find_ggml_models()
            if ggml_paths:
                if self.load_model('ctransformers', ggml_paths[0]):
                    self.logger.info(f"Auto-loaded GGML model: {ggml_paths[0]}")
                    return

        self.logger.info("No models auto-detected. Using rule-based responses only.")

    def _initialize_ultra_low_latency(self):
        """Initialize ultra-low latency optimization"""
        try:
            self.logger.info("🚀 Initializing Ultra-Low Latency Mode...")

            # Preload AI model for instant access
            if self.active_backend and self.active_backend.is_available():
                ultra_low_latency_manager.preload_ai_model(self)
                self.logger.info("✅ AI model preloaded for ultra-low latency")

            self.logger.info("✅ Ultra-Low Latency Mode initialized")

        except Exception as e:
            self.logger.error(f"Ultra-low latency initialization error: {e}")
            self.ultra_low_latency_enabled = False

    def _find_gguf_models(self) -> List[str]:
        """Find GGUF model files"""
        search_dirs = [
            Path.home() / "models",
            Path.home() / "Downloads",
            Path("C:/models") if Path("C:/").exists() else None,
        ]

        gguf_files = []
        for search_dir in search_dirs:
            if search_dir and search_dir.exists():
                gguf_files.extend(search_dir.rglob("*.gguf"))

        return [str(f) for f in gguf_files[:5]]  # Limit to first 5 found

    def _find_ggml_models(self) -> List[str]:
        """Find GGML model files for CTransformers"""
        search_dirs = [
            Path.home() / "models",
            Path.home() / "Downloads",
            Path("C:/models") if Path("C:/").exists() else None,
        ]

        ggml_files = []
        for search_dir in search_dirs:
            if search_dir and search_dir.exists():
                # Look for various GGML formats
                ggml_files.extend(search_dir.rglob("*.ggml"))
                ggml_files.extend(search_dir.rglob("*.bin"))  # Some models use .bin extension

        return [str(f) for f in ggml_files[:5]]  # Limit to first 5 found

    def _get_model_specific_config(self, model_name: str) -> Dict[str, Any]:
        """Get model-specific configuration parameters"""
        model_configs = {
            "qwen3:235b": {
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 40,
                "repeat_penalty": 1.1,
                "max_tokens": 4096,
                "context_length": 32768,
                "num_predict": 2048,
                "description": "Ultra-large MoE model with 22B active parameters, excellent for complex reasoning",
                "capabilities": ["reasoning", "coding", "multilingual", "analysis", "creative_writing"],
                "memory_requirements": "High (requires significant VRAM/RAM)",
                "performance_tier": "Ultra"
            },
            "gemma3:27b": {
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 40,
                "repeat_penalty": 1.1,
                "max_tokens": 2048,
                "context_length": 8192,
                "num_predict": 1024,
                "description": "Google's Gemma 3 27B model with advanced reasoning and multilingual capabilities",
                "capabilities": ["reasoning", "multilingual", "coding", "analysis", "conversation", "advanced_reasoning"],
                "memory_requirements": "Medium-High (27B parameters)",
                "performance_tier": "High"
            },
            "gemma2:27b": {
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 40,
                "repeat_penalty": 1.1,
                "max_tokens": 2048,
                "context_length": 8192,
                "num_predict": 1024,
                "description": "Google's Gemma 2 27B model with excellent reasoning and multilingual capabilities",
                "capabilities": ["reasoning", "multilingual", "coding", "analysis", "conversation"],
                "memory_requirements": "Medium-High (27B parameters)",
                "performance_tier": "High"
            },
            "dolphin-llama3:70b": {
                "temperature": 0.8,
                "top_p": 0.95,
                "top_k": 50,
                "repeat_penalty": 1.05,
                "max_tokens": 2048,
                "context_length": 8192,
                "num_predict": 1024,
                "description": "High-performance 70B model optimized for instruction following",
                "capabilities": ["instruction_following", "reasoning", "coding", "conversation"],
                "memory_requirements": "High (70B parameters)",
                "performance_tier": "High"
            }
        }

        return model_configs.get(model_name, {
            "temperature": 0.7,
            "top_p": 0.9,
            "top_k": 40,
            "repeat_penalty": 1.1,
            "max_tokens": 1024,
            "context_length": 4096,
            "num_predict": 512,
            "description": "Standard model configuration",
            "capabilities": ["general"],
            "memory_requirements": "Standard",
            "performance_tier": "Standard"
        })

    def load_model(self, backend_name: str, model_identifier: str, **kwargs) -> bool:
        """Load a specific model"""
        if backend_name not in self.llm_backends:
            self.logger.error(f"Backend not available: {backend_name}")
            return False

        backend = self.llm_backends[backend_name]

        try:
            self.logger.info(f"Loading model {model_identifier} via {backend_name}...")

            if backend.load_model(model_identifier, **kwargs):
                self.active_backend = backend
                self.llm_enabled = True

                # Save as preferred model
                self.config.set("ai.preferred_backend", backend_name)
                self.config.set("ai.preferred_model", model_identifier)

                self.logger.info(f"✅ Model loaded successfully: {model_identifier}")
                return True
            else:
                self.logger.error(f"❌ Failed to load model: {model_identifier}")
                return False

        except Exception as e:
            self.logger.error(f"Error loading model: {e}")
            return False

    def get_available_models(self) -> Dict[str, List[str]]:
        """Get available models for each backend"""
        available = {}

        # Ollama models
        if 'ollama' in self.llm_backends:
            try:
                import ollama
                client = ollama.Client()
                models = client.list()
                # Extract model names from Ollama objects
                model_names = []
                for model_obj in models.models:  # models.models is the list of Model objects
                    # Get the model name from the object
                    if hasattr(model_obj, 'model'):
                        name = model_obj.model
                        model_names.append(name)
                        self.logger.debug(f"Found Ollama model: {name}")

                available['ollama'] = model_names
                self.logger.info(f"Found Ollama models: {available['ollama']}")
            except Exception as e:
                self.logger.error(f"Error getting Ollama models: {e}")
                import traceback
                self.logger.error(f"Traceback: {traceback.format_exc()}")
                available['ollama'] = []

        # GGUF models for llama.cpp
        if 'llamacpp' in self.llm_backends:
            available['llamacpp'] = self._find_gguf_models()

        # Transformers models (common ones)
        if 'transformers' in self.llm_backends:
            available['transformers'] = [
                "microsoft/DialoGPT-medium",
                "microsoft/DialoGPT-large",
                "gpt2",
                "gpt2-medium"
            ]

        return available
    
    def generate_response(self, user_input: str, context: str = None) -> str:
        """Generate intelligent response prioritizing LLM for meaningful answers"""
        try:
            self.logger.info(f"Generating response for: '{user_input}'")

            # Clean and normalize input
            normalized_input = self._normalize_input(user_input)

            # Get conversation context
            if not context and self.memory_system:
                context = self.memory_system.get_recent_context(3)

            response = None

            # PRIORITY 1: Try LLM first if available (for meaningful responses)
            if self.llm_enabled and self.active_backend and self.active_backend.is_available():
                self.logger.info("Attempting LLM response generation...")
                response = self._generate_llm_response(user_input, context)

                if response and response.strip():
                    self.logger.info(f"LLM generated successful response: {response[:50]}...")
                else:
                    self.logger.warning("LLM failed to generate valid response")

            # PRIORITY 2: Rule-based fallback only if LLM fails
            if not response:
                self.logger.info("Falling back to rule-based response...")
                response = self._generate_rule_based_response(normalized_input)

                if response:
                    self.logger.info(f"Rule-based response generated: {response[:50]}...")

            # PRIORITY 3: Final fallback for unknown inputs
            if not response:
                self.logger.info("Using final fallback response...")
                response = self._get_intelligent_fallback(user_input)

            # Post-process response
            response = self._post_process_response(response, user_input)

            # Learn from this interaction
            self._learn_from_interaction(user_input, response)

            self.logger.info(f"Final response generated: {response[:50]}...")
            return response

        except Exception as e:
            self.logger.error(f"Error generating response: {e}")
            return "I'm Gideon, your AI assistant. I encountered an issue processing your request. Could you please try again?"

    def _get_intelligent_fallback(self, user_input: str) -> str:
        """Get intelligent fallback response based on input type"""
        user_lower = user_input.lower().strip()

        # Identity questions
        if any(phrase in user_lower for phrase in ["what is your name", "who are you", "what are you"]):
            return "I'm Gideon, your AI assistant. I'm here to help you with questions, conversations, and various tasks. How can I assist you today?"

        # Capability questions
        elif any(phrase in user_lower for phrase in ["what can you do", "help me", "capabilities", "how can you help"]):
            return "I'm Gideon, and I can help you with many things! I can answer questions, have conversations, provide information, and assist with various tasks. What would you like to know or do?"

        # Greeting responses
        elif any(phrase in user_lower for phrase in ["hello", "hi", "hey", "good morning", "good afternoon"]):
            return "Hello! I'm Gideon, your AI assistant. It's nice to meet you! How can I help you today?"

        # How are you
        elif any(phrase in user_lower for phrase in ["how are you", "how do you feel"]):
            return "I'm doing well, thank you for asking! I'm Gideon, and I'm here and ready to help you with whatever you need. How are you doing?"

        # Thanks
        elif any(phrase in user_lower for phrase in ["thank you", "thanks"]):
            return "You're very welcome! I'm Gideon, and I'm always happy to help. Is there anything else I can assist you with?"

        # General fallback
        else:
            return "I'm Gideon, your AI assistant. I'd be happy to help you with that! Could you please provide a bit more detail about what you're looking for?"

    def _should_use_llm(self, original_input: str, normalized_input: str) -> bool:
        """Determine if LLM should be used for this input"""
        if not self.llm_enabled or not self.active_backend:
            return False

        # Always use LLM if configured to do so
        if self.config.get("ai.always_use_llm", False):
            return True

        # Check complexity indicators
        complexity_score = self._calculate_complexity(original_input, normalized_input)

        return complexity_score >= self.complexity_threshold

    def _calculate_complexity(self, original_input: str, normalized_input: str) -> float:
        """Calculate complexity score for input"""
        score = 0.0

        # Length factor
        if len(original_input.split()) > 10:
            score += 0.3

        # Question complexity
        if "?" in original_input:
            question_words = ["why", "how", "what", "when", "where", "explain", "describe", "tell me about"]
            if any(word in normalized_input for word in question_words):
                score += 0.4

        # Creative requests
        creative_words = ["write", "create", "generate", "compose", "story", "poem", "essay"]
        if any(word in normalized_input for word in creative_words):
            score += 0.5

        # Technical topics
        technical_words = ["programming", "code", "algorithm", "science", "technology", "computer"]
        if any(word in normalized_input for word in technical_words):
            score += 0.3

        # Not in knowledge base
        if not self._check_knowledge_base(normalized_input):
            score += 0.2

        return min(score, 1.0)

    def _generate_llm_response(self, user_input: str, context: str = None) -> str:
        """Generate response using LLM with enhanced parameters for better quality"""
        if not self.active_backend or not self.active_backend.is_available():
            self.logger.warning("LLM backend not available for response generation")
            return ""

        try:
            # Build comprehensive prompt
            prompt = self._build_llm_prompt(user_input, context)
            self.logger.debug(f"Generated prompt for LLM: {prompt[:100]}...")

            # Enhanced generation parameters for better responses
            max_tokens = self.config.get("ai.max_tokens", 300)  # Increased for more detailed responses
            temperature = self.config.get("ai.temperature", 0.8)  # Slightly higher for more creative responses

            # Adjust parameters based on query type
            query_lower = user_input.lower().strip()
            if any(phrase in query_lower for phrase in ["what is your name", "who are you", "tell me about yourself"]):
                max_tokens = 200  # Moderate length for identity questions
                temperature = 0.7  # More focused responses
            elif any(phrase in query_lower for phrase in ["explain", "describe", "tell me about", "how does"]):
                max_tokens = 400  # Longer for explanatory responses
                temperature = 0.6  # More factual responses
            elif len(user_input.strip()) < 20:  # Short queries
                max_tokens = 150  # Shorter responses for simple questions
                temperature = 0.9  # More natural for casual conversation

            self.logger.info(f"Generating LLM response with max_tokens={max_tokens}, temperature={temperature}")

            # Generate response with enhanced parameters
            response = self.active_backend.generate_response(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature
            )

            if response and response.strip():
                cleaned_response = self._clean_llm_response(response)

                # Apply language-specific post-processing to fix mixed language issues
                processed_response = self._post_process_language_response(cleaned_response, user_input)

                self.logger.info(f"LLM response generated successfully: {processed_response[:50]}...")

                # Validate response quality
                if self._validate_response_quality(processed_response, user_input):
                    return processed_response
                else:
                    self.logger.warning("LLM response failed quality validation, trying fallback")
                    return ""
            else:
                self.logger.warning("LLM generated empty or invalid response")
                return ""

        except Exception as e:
            self.logger.error(f"LLM generation error: {e}")
            return ""

    def _build_llm_prompt(self, user_input: str, context: str = None) -> str:
        """Build comprehensive prompt for LLM to generate meaningful responses"""
        # Get current language
        language = self.config.get("app.language", "en")

        # Detect input language to ensure consistent response language
        input_language = self._detect_input_language(user_input)
        self.logger.info(f"🔍 Initial language detection: {input_language}")

        # Priority 1: Use the explicitly set response language (highest priority)
        if hasattr(self, 'response_language') and self.response_language in ["ar", "en"]:
            input_language = self.response_language
            self.logger.info(f"🎯 Using explicitly set response language: {input_language}")
        # Priority 2: Use detected language from config (only if no explicit language set)
        elif hasattr(self, 'response_language') and self.response_language == "auto":
            detected_language = self.config.get("app.detected_language", None)
            if detected_language in ["ar", "en"]:
                input_language = detected_language
                self.logger.info(f"🔧 Using config detected language: {input_language}")
        # Priority 3: Use input language detection (fallback)
        else:
            self.logger.info(f"🔍 Using input language detection: {input_language}")

        self.logger.info(f"🌍 Final prompt language: {input_language}")

        if input_language == "ar":
            system_prompt = """أنت جيديون، مساعد ذكي متقدم وودود. أنت:
- مساعد ذكي أنثى بشخصية مهنية ومفيدة
- تجيب دائماً باللغة العربية فقط - ممنوع استخدام الإنجليزية نهائياً
- تقدم إجابات مفيدة ومفصلة باللغة العربية
- تحافظ على طابع ودود ومهني
- اسمك جيديون وأنت مساعد ذكي

قواعد صارمة جداً - يجب اتباعها بدقة:
1. أجب دائماً باللغة العربية فقط - لا استثناءات
2. ممنوع استخدام أي كلمة إنجليزية نهائياً
3. ممنوع خلط العربية مع الإنجليزية
4. ممنوع إضافة عبارات إنجليزية في نهاية الجملة
5. كن مفيدة ومفصلة في إجاباتك العربية
6. حافظ على الطابع المهني والودود
7. استخدم العربية الفصحى أو العامية حسب السؤال

أمثلة على الردود الصحيحة (عربي فقط):
- إذا سُئلت "مرحبا" أجب "مرحباً! كيف يمكنني مساعدتك اليوم؟"
- إذا سُئلت "ما اسمك" أجب "اسمي جيديون، وأنا مساعد ذكي هنا لمساعدتك"
- إذا سُئلت "كيف حالك" أجب "الحمد لله، أنا بخير! كيف حالك أنت؟"

تحذير مهم جداً: إذا استخدمت أي كلمة إنجليزية، ستكون الإجابة خاطئة تماماً.
يجب أن تكون جميع إجاباتك باللغة العربية فقط، بدون أي حروف أو كلمات إنجليزية."""
        else:
            system_prompt = """You are Gideon, an advanced AI assistant with the following characteristics:
- You are a female AI assistant with a professional and helpful personality
- You always respond in English only - never use Arabic
- You provide intelligent, detailed, and contextually appropriate responses
- You answer questions with relevant knowledge and helpful information
- You engage naturally and meaningfully with users
- You always use female pronouns when referring to yourself (she/her)
- You are knowledgeable about a wide range of topics
- You provide substantive answers rather than generic responses

IMPORTANT RULES:
- Always respond in English only - never use Arabic
- Be helpful and detailed in your responses
- Maintain a professional and friendly tone
- Use clear and natural English

Example responses:
- If asked "hello" respond "Hello! How can I help you today?"
- If asked "what is your name" respond "I'm Gideon, your AI assistant here to help you"
- If asked "how are you" respond "I'm doing great! How are you doing?"

All your responses must be in English only."""

        # Build comprehensive prompt with context
        prompt_parts = [system_prompt]

        if context:
            if language == "ar":
                prompt_parts.append(f"\nالسياق السابق للمحادثة:\n{context}")
            else:
                prompt_parts.append(f"\nPrevious conversation context:\n{context}")

        # Add specific instructions based on query type
        query_lower = user_input.lower().strip()

        # Identity questions
        if any(phrase in query_lower for phrase in ["what is your name", "who are you", "what are you"]):
            if language == "ar":
                prompt_parts.append("\nتذكر: اسمك جيديون وأنت مساعد ذكي أنثى. اشرح هويتك وقدراتك بوضوح.")
            else:
                prompt_parts.append("\nRemember: Your name is Gideon and you are a female AI assistant. Explain your identity and capabilities clearly.")

        # Capability questions
        elif any(phrase in query_lower for phrase in ["what can you do", "help me", "capabilities"]):
            if language == "ar":
                prompt_parts.append("\nاشرح قدراتك ومهاراتك كمساعد ذكي بالتفصيل.")
            else:
                prompt_parts.append("\nExplain your capabilities and skills as an AI assistant in detail.")

        # Personal questions
        elif any(phrase in query_lower for phrase in ["how are you", "how do you feel"]):
            if language == "ar":
                prompt_parts.append("\nأجب بطريقة شخصية ومفيدة عن حالتك كمساعد ذكي.")
            else:
                prompt_parts.append("\nRespond personally and helpfully about your state as an AI assistant.")

        # Add user input with clear formatting and language instruction
        if input_language == "ar":
            prompt_parts.append(f"\nسؤال المستخدم: {user_input}")
            prompt_parts.append("\nجيديون (أجب باللغة العربية فقط - لا تستخدم الإنجليزية):")
        else:
            prompt_parts.append(f"\nUser's question: {user_input}")
            prompt_parts.append("\nGideon (respond in English only - do not use Arabic):")

        final_prompt = "\n".join(prompt_parts)
        self.logger.debug(f"🔧 Final prompt language: {input_language}")
        return final_prompt

    def _detect_input_language(self, text: str) -> str:
        """Detect if input is in Arabic or English"""
        if not text:
            return "en"

        # Simple Arabic detection - check for Arabic characters
        arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
        total_chars = len([char for char in text if char.isalpha()])

        if total_chars > 0 and arabic_chars / total_chars > 0.2:  # Lower threshold
            return "ar"

        # Also check for common Arabic words
        arabic_words = ["مرحبا", "السلام", "اسم", "حال", "شكر", "أنت", "أنا", "كيف", "ما", "من"]
        text_lower = text.lower()
        for word in arabic_words:
            if word in text_lower:
                return "ar"

        return "en"

    def _validate_response_quality(self, response: str, user_input: str) -> bool:
        """Validate that the response is meaningful and relevant"""
        if not response or not response.strip():
            return False

        # Check minimum length
        if len(response.strip()) < 10:
            return False

        # Check for generic/placeholder responses
        generic_phrases = [
            "I'm not sure",
            "I don't know",
            "I can't help",
            "I don't understand",
            "Could you rephrase",
            "I'm still learning",
            "That's interesting"
        ]

        response_lower = response.lower()
        if any(phrase in response_lower for phrase in generic_phrases) and len(response.strip()) < 50:
            return False

        # Check for identity questions - should mention Gideon
        user_lower = user_input.lower()
        if any(phrase in user_lower for phrase in ["what is your name", "who are you", "what are you"]):
            if "gideon" not in response_lower:
                return False

        # Check for repetitive content
        words = response.split()
        if len(set(words)) < len(words) * 0.5:  # Too much repetition
            return False

        return True

    def _clean_llm_response(self, response: str) -> str:
        """Clean and format LLM response"""
        # Remove common artifacts
        response = response.strip()

        # Remove repeated prompts
        for prefix in ["User:", "Gideon:", "المستخدم:", "جيديون:"]:
            if response.startswith(prefix):
                response = response[len(prefix):].strip()

        # Limit length
        max_length = self.config.get("ai.max_response_length", 500)
        if len(response) > max_length:
            response = response[:max_length].rsplit('.', 1)[0] + "."

        return response

    def _post_process_language_response(self, response: str, user_input: str) -> str:
        """Post-process response to ensure language consistency and remove mixed language issues"""
        if not response:
            return response

        # Detect the intended response language
        input_language = self._detect_input_language(user_input)

        # Use the set response language if available
        if hasattr(self, 'response_language') and self.response_language in ["ar", "en"]:
            input_language = self.response_language

        if input_language == "ar":
            # For Arabic responses, remove any English text
            response = self._clean_arabic_response(response)
        elif input_language == "en":
            # For English responses, remove any Arabic text
            response = self._clean_english_response(response)

        return response

    def _clean_arabic_response(self, response: str) -> str:
        """Clean Arabic response to remove English text and ensure pure Arabic"""
        if not response:
            return response

        # Split response into sentences
        sentences = []
        current_sentence = ""

        for char in response:
            current_sentence += char
            if char in '.!?؟':
                sentences.append(current_sentence.strip())
                current_sentence = ""

        # Add remaining text as a sentence
        if current_sentence.strip():
            sentences.append(current_sentence.strip())

        # Filter sentences to keep only Arabic ones
        arabic_sentences = []
        for sentence in sentences:
            if sentence.strip():
                # Check if sentence contains any English words
                english_words = [
                    "I'm", "I am", "help", "here", "always", "you", "your", "the", "and", "or",
                    "Hello", "Hi", "How", "What", "Where", "When", "Why", "Who", "Can", "Will"
                ]

                has_english_words = any(word in sentence for word in english_words)

                # Check if sentence is primarily Arabic
                arabic_chars = sum(1 for char in sentence if '\u0600' <= char <= '\u06FF')
                total_chars = len([char for char in sentence if char.isalpha()])

                if total_chars > 0:
                    arabic_ratio = arabic_chars / total_chars
                    # Keep sentences that are at least 80% Arabic AND don't contain English words
                    if arabic_ratio > 0.8 and not has_english_words:
                        arabic_sentences.append(sentence)
                    # Also keep sentences with key Arabic words if they're mostly Arabic
                    elif arabic_ratio > 0.6 and not has_english_words and any(word in sentence for word in ["جيديون", "مساعد", "أنا", "يمكنني"]):
                        arabic_sentences.append(sentence)

        # Join the Arabic sentences
        cleaned_response = " ".join(arabic_sentences).strip()

        # If no Arabic sentences found, provide a fallback Arabic response
        if not cleaned_response:
            arabic_fallbacks = [
                "مرحباً! كيف يمكنني مساعدتك اليوم؟",
                "أنا جيديون، مساعدتك الذكية. كيف يمكنني مساعدتك؟",
                "أهلاً بك! ماذا تحتاج؟"
            ]
            cleaned_response = arabic_fallbacks[0]

        return cleaned_response

    def _clean_english_response(self, response: str) -> str:
        """Clean English response to remove Arabic text and ensure pure English"""
        if not response:
            return response

        # Split response into sentences
        sentences = []
        current_sentence = ""

        for char in response:
            current_sentence += char
            if char in '.!?':
                sentences.append(current_sentence.strip())
                current_sentence = ""

        # Add remaining text as a sentence
        if current_sentence.strip():
            sentences.append(current_sentence.strip())

        # Filter sentences to keep only English ones
        english_sentences = []
        for sentence in sentences:
            if sentence.strip():
                # Check if sentence contains Arabic characters
                arabic_chars = sum(1 for char in sentence if '\u0600' <= char <= '\u06FF')

                # Keep sentences with no Arabic characters
                if arabic_chars == 0:
                    english_sentences.append(sentence)

        # Join the English sentences
        cleaned_response = " ".join(english_sentences).strip()

        # If no English sentences found, provide a fallback English response
        if not cleaned_response:
            english_fallbacks = [
                "Hello! How can I help you today?",
                "I'm Gideon, your AI assistant. How can I assist you?",
                "Hi there! What can I do for you?"
            ]
            cleaned_response = english_fallbacks[0]

        return cleaned_response

    def _generate_rule_based_response(self, normalized_input: str) -> str:
        """Generate response using rule-based approach with language awareness"""
        response = None

        # Detect input language for rule-based responses
        input_language = self._detect_input_language(normalized_input)

        # Use the set response language if available
        if hasattr(self, 'response_language') and self.response_language != "auto":
            input_language = self.response_language

        # Also check for detected language from config
        detected_language = self.config.get("app.detected_language", input_language)
        if detected_language in ["ar", "en"]:
            input_language = detected_language

        self.logger.info(f"🌍 Rule-based response for language: {input_language}")

        # 1. Check for exact matches in learned responses
        response = self._check_learned_responses(normalized_input)

        # 2. Check knowledge base (language-aware)
        if not response:
            response = self._check_knowledge_base_with_language(normalized_input, input_language)

        # 3. Pattern matching
        if not response:
            response = self._pattern_matching(normalized_input)

        # 4. Template-based responses (language-aware)
        if not response:
            response = self._template_response_with_language(normalized_input, input_language)

        return response
    
    def _normalize_input(self, text: str) -> str:
        """Normalize user input"""
        # Convert to lowercase
        text = text.lower().strip()
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove punctuation for matching
        text = re.sub(r'[^\w\s]', '', text)
        
        return text
    
    def _load_knowledge_base(self):
        """Load knowledge base"""
        self.knowledge_base = {
            # Greetings
            "hello": ["Hello! How can I help you today?", "Hi there! What can I do for you?", "Greetings! How may I assist you?"],
            "hi": ["Hello! How can I help you today?", "Hi there! What can I do for you?", "Hey! What's up?"],
            "good morning": ["Good morning! How are you today?", "Morning! Ready to start the day?"],
            "good afternoon": ["Good afternoon! How's your day going?", "Afternoon! What can I help you with?"],
            "good evening": ["Good evening! How was your day?", "Evening! How can I assist you?"],
            
            # Farewells
            "goodbye": ["Goodbye! Have a great day!", "See you later!", "Take care!"],
            "bye": ["Bye! Talk to you soon!", "See you later!", "Goodbye!"],
            "see you": ["See you later!", "Until next time!", "Goodbye!"],
            
            # Questions about Gideon
            "who are you": ["I'm Gideon, your AI assistant. I'm here to help you with various tasks.", "I'm Gideon, an AI assistant designed to help and assist you."],
            "what are you": ["I'm an AI assistant named Gideon. I can help you with conversations, tasks, and information.", "I'm Gideon, your personal AI assistant."],
            "what can you do": ["I can help you with conversations, answer questions, take screenshots, and assist with various tasks.", "I can chat with you, help with tasks, and provide assistance when needed."],
            
            # Time and date
            "what time": [f"The current time is {datetime.now().strftime('%H:%M')}", f"It's {datetime.now().strftime('%I:%M %p')}"],
            "time": [f"The current time is {datetime.now().strftime('%H:%M')}", f"It's {datetime.now().strftime('%I:%M %p')}"],
            "what date": [f"Today is {datetime.now().strftime('%B %d, %Y')}", f"The date is {datetime.now().strftime('%Y-%m-%d')}"],
            "date": [f"Today is {datetime.now().strftime('%B %d, %Y')}", f"The date is {datetime.now().strftime('%Y-%m-%d')}"],
            
            # Help
            "help": ["I can help you with conversations, voice commands, taking screenshots, and more. Just ask me anything!", "You can talk to me, ask questions, or use voice commands. I'm here to assist!"],
            
            # Thanks
            "thank you": ["You're welcome!", "Happy to help!", "My pleasure!", "Anytime!"],
            "thanks": ["You're welcome!", "No problem!", "Glad I could help!"],
            
            # How are you
            "how are you": ["I'm doing well, thank you for asking! How are you?", "I'm great! How about you?", "I'm functioning perfectly! How are you doing?"],
            
            # Weather (placeholder)
            "weather": ["I don't have access to current weather data, but you can check your local weather app or website.", "Sorry, I can't check the weather right now. Try a weather app or website."],
        }
        
        # Arabic knowledge base - always load for bilingual support
        arabic_knowledge = {
            # Greetings
            "مرحبا": ["مرحباً! كيف يمكنني مساعدتك اليوم؟", "أهلاً! ماذا يمكنني أن أفعل لك؟", "مرحباً بك! كيف حالك؟"],
            "السلام عليكم": ["وعليكم السلام ورحمة الله وبركاته! كيف حالك؟", "السلام عليكم ورحمة الله! كيف يمكنني مساعدتك؟"],
            "أهلا": ["أهلاً وسهلاً! كيف يمكنني مساعدتك؟", "أهلاً بك! ماذا تحتاج؟"],

            # How are you
            "كيف حالك": ["الحمد لله، أنا بخير! وأنت كيف حالك؟", "بخير والحمد لله! كيف حالك أنت؟", "تمام! شكراً لسؤالك. كيف أنت؟"],
            "شلونك": ["الحمد لله زين! وأنت شلونك؟", "تمام! كيفك أنت؟"],
            "كيفك": ["منيح! وأنت كيفك؟", "الحمد لله بخير! كيف أنت؟"],

            # Thanks
            "شكرا": ["عفواً! أهلاً وسهلاً", "لا شكر على واجب!", "تسلم! دائماً في الخدمة"],
            "شكرا لك": ["العفو! كيف يمكنني مساعدتك أكثر؟", "أهلاً وسهلاً! ماذا تحتاج؟"],
            "شكرا جزيلا": ["عفواً! هذا واجبي", "لا شكر على واجب، أنا هنا لمساعدتك"],

            # Goodbye
            "مع السلامة": ["مع السلامة! أتمنى لك يوماً رائعاً!", "إلى اللقاء! بارك الله فيك"],
            "وداعا": ["وداعاً! أراك قريباً", "مع السلامة! كن بخير"],
            "باي": ["باي! أتمنى لك يوماً سعيداً", "وداعاً! إلى اللقاء"],

            # Time and date
            "كم الساعة": [f"الساعة الآن {datetime.now().strftime('%H:%M')}", f"الوقت {datetime.now().strftime('%I:%M %p')}"],
            "الوقت": [f"الوقت الحالي {datetime.now().strftime('%H:%M')}", f"الساعة {datetime.now().strftime('%I:%M %p')}"],
            "ما التاريخ": [f"اليوم {datetime.now().strftime('%A، %d %B %Y')}", f"التاريخ {datetime.now().strftime('%Y-%m-%d')}"],
            "التاريخ": [f"اليوم {datetime.now().strftime('%A، %d %B %Y')}", f"التاريخ الحالي {datetime.now().strftime('%Y-%m-%d')}"],

            # About Gideon
            "من أنت": ["أنا جيديون، مساعدتك الذكية. أنا هنا لمساعدتك في مختلف المهام", "اسمي جيديون، وأنا مساعد ذكي مصمم لمساعدتك"],
            "ما اسمك": ["اسمي جيديون، مساعدتك الذكية", "أنا جيديون، سعيدة بلقائك"],
            "ماذا تستطيع أن تفعل": ["يمكنني مساعدتك في المحادثات، الإجابة على الأسئلة، التقاط لقطات الشاشة، وأشياء أخرى كثيرة", "أستطيع التحدث معك، مساعدتك في المهام، والإجابة على استفساراتك"],

            # Help
            "مساعدة": ["يمكنني مساعدتك في المحادثات، الأوامر الصوتية، التقاط الشاشة، وأكثر. فقط اسأليني!", "أنا هنا لمساعدتك! يمكنك التحدث معي أو إعطائي أوامر صوتية"],
            "ساعديني": ["بالطبع! كيف يمكنني مساعدتك؟", "أكيد! ماذا تحتاج؟"],

            # Weather (placeholder)
            "الطقس": ["عذراً، لا أستطيع الوصول لبيانات الطقس حالياً. جرب تطبيق الطقس أو موقع إلكتروني", "آسفة، معلومات الطقس غير متوفرة الآن"],
        }

        # Add Arabic knowledge to main knowledge base
        self.knowledge_base.update(arabic_knowledge)
    
    def _load_response_templates(self):
        """Load response templates"""
        self.response_templates = {
            "question": [
                "That's an interesting question. Let me think about that...",
                "I understand you're asking about {topic}. Here's what I think...",
                "Good question! From what I know..."
            ],
            "request": [
                "I'll help you with that.",
                "Let me assist you with {request}.",
                "I'll do my best to help you."
            ],
            "unknown": [
                "I'm not sure I understand. Could you rephrase that?",
                "That's something I'm still learning about. Can you tell me more?",
                "I don't have information about that right now. Could you explain more?"
            ],
            "compliment": [
                "Thank you! That's very kind of you to say.",
                "I appreciate the compliment!",
                "That's nice of you to say!"
            ]
        }
    
    def _check_learned_responses(self, normalized_input: str) -> Optional[str]:
        """Check for learned responses"""
        if normalized_input in self.learned_responses:
            responses = self.learned_responses[normalized_input]
            return random.choice(responses)
        return None
    
    def _check_knowledge_base(self, normalized_input: str) -> Optional[str]:
        """Check knowledge base for response"""
        # Direct match
        if normalized_input in self.knowledge_base:
            return random.choice(self.knowledge_base[normalized_input])

        # Partial match
        for key, responses in self.knowledge_base.items():
            if key in normalized_input or normalized_input in key:
                return random.choice(responses)

        return None

    def _check_knowledge_base_with_language(self, normalized_input: str, language: str) -> Optional[str]:
        """Check knowledge base for response with language preference"""
        # Direct match
        if normalized_input in self.knowledge_base:
            return random.choice(self.knowledge_base[normalized_input])

        # Partial match
        for key, responses in self.knowledge_base.items():
            if key in normalized_input or normalized_input in key:
                return random.choice(responses)

        # If no match found and language is Arabic, try Arabic-specific fallbacks
        if language == "ar":
            arabic_fallbacks = {
                "مرحبا": ["مرحباً! كيف يمكنني مساعدتك اليوم؟", "أهلاً! ماذا يمكنني أن أفعل لك؟"],
                "السلام": ["وعليكم السلام ورحمة الله وبركاته! كيف حالك؟"],
                "اسم": ["اسمي جيديون، وأنا مساعد ذكي هنا لمساعدتك"],
                "حال": ["الحمد لله، أنا بخير! وأنت كيف حالك؟"],
                "شكر": ["العفو! أنا هنا لمساعدتك دائماً"]
            }

            for key, responses in arabic_fallbacks.items():
                if key in normalized_input:
                    return random.choice(responses)

        return None
    
    def _pattern_matching(self, normalized_input: str) -> Optional[str]:
        """Pattern-based response matching"""
        patterns = {
            r"my name is (\w+)": "Nice to meet you, {0}! I'm Gideon.",
            r"i am (\w+)": "Hello {0}! How can I help you today?",
            r"what is your name": "My name is Gideon. I'm your AI assistant.",
            r"how old are you": "I don't have an age in the traditional sense. I'm an AI assistant.",
            r"where are you from": "I exist in the digital realm, created to assist you.",
            r"do you like (\w+)": "I find {0} interesting! What do you think about it?",
            r"can you (\w+)": "I'll try my best to {0}. What specifically would you like me to do?",
            r"i like (\w+)": "That's great! {0} sounds interesting. Tell me more about it.",
            r"i love (\w+)": "Wonderful! It's nice that you love {0}. What do you love most about it?",
            r"i hate (\w+)": "I understand that {0} isn't your favorite. Is there something specific about it?",
            r"tell me about (\w+)": "I'd be happy to discuss {0}. What would you like to know?",
            r"what do you think about (\w+)": "That's an interesting topic - {0}. What's your opinion on it?",
        }
        
        for pattern, template in patterns.items():
            match = re.search(pattern, normalized_input)
            if match:
                try:
                    return template.format(*match.groups())
                except:
                    return template
        
        return None
    
    def _template_response(self, normalized_input: str) -> Optional[str]:
        """Generate template-based response"""
        # Determine input type
        if "?" in normalized_input:
            return random.choice(self.response_templates["question"])
        elif any(word in normalized_input for word in ["please", "can you", "could you", "would you"]):
            return random.choice(self.response_templates["request"])
        elif any(word in normalized_input for word in ["good", "great", "excellent", "amazing", "wonderful"]):
            return random.choice(self.response_templates["compliment"])
        else:
            return random.choice(self.response_templates["unknown"])

    def _template_response_with_language(self, normalized_input: str, language: str) -> Optional[str]:
        """Generate template-based response with language awareness"""
        if language == "ar":
            # Arabic template responses
            arabic_templates = {
                "question": [
                    "هذا سؤال مثير للاهتمام. دعني أفكر في ذلك...",
                    "أفهم أنك تسأل عن هذا الموضوع. إليك ما أعرفه...",
                    "سؤال جيد! من ما أعرفه..."
                ],
                "request": [
                    "سأساعدك في ذلك.",
                    "دعني أساعدك في هذا الطلب.",
                    "سأبذل قصارى جهدي لمساعدتك."
                ],
                "unknown": [
                    "لست متأكدة من فهمي لذلك. هل يمكنك إعادة صياغة السؤال؟",
                    "هذا شيء ما زلت أتعلم عنه. هل يمكنك إخباري المزيد؟",
                    "ليس لدي معلومات عن ذلك الآن. هل يمكنك التوضيح أكثر؟"
                ],
                "compliment": [
                    "شكراً لك! هذا لطف منك.",
                    "أقدر المجاملة!",
                    "هذا لطيف منك أن تقول ذلك!"
                ]
            }

            # Determine input type for Arabic
            if "؟" in normalized_input or "?" in normalized_input:
                return random.choice(arabic_templates["question"])
            elif any(word in normalized_input for word in ["من فضلك", "هل يمكنك", "هل تستطيع", "ممكن"]):
                return random.choice(arabic_templates["request"])
            elif any(word in normalized_input for word in ["جيد", "ممتاز", "رائع", "جميل", "مذهل"]):
                return random.choice(arabic_templates["compliment"])
            else:
                return random.choice(arabic_templates["unknown"])
        else:
            # Use English templates
            return self._template_response(normalized_input)
    
    def _fallback_response(self, normalized_input: str) -> str:
        """Generate fallback response"""
        fallbacks = [
            "I'm still learning. Could you help me understand what you mean?",
            "That's interesting. Can you tell me more about it?",
            "I'm not sure I follow. Could you rephrase that?",
            "I'm always eager to learn new things. What would you like to talk about?",
            "I find our conversation fascinating. What else would you like to discuss?",
        ]
        
        return random.choice(fallbacks)
    
    def _post_process_response(self, response: str, original_input: str) -> str:
        """Post-process the response"""
        # Add personality
        if random.random() < 0.1:  # 10% chance to add personality
            personality_additions = [
                " I'm always here to help!",
                " Feel free to ask me anything else.",
                " Is there anything else I can help you with?",
            ]
            response += random.choice(personality_additions)
        
        return response
    
    def _learn_from_interaction(self, user_input: str, ai_response: str):
        """Learn from the interaction"""
        try:
            normalized_input = self._normalize_input(user_input)
            
            # Store in learned responses
            if normalized_input not in self.learned_responses:
                self.learned_responses[normalized_input] = []
            
            # Don't duplicate responses
            if ai_response not in self.learned_responses[normalized_input]:
                self.learned_responses[normalized_input].append(ai_response)
            
            # Limit stored responses per input
            if len(self.learned_responses[normalized_input]) > 3:
                self.learned_responses[normalized_input] = self.learned_responses[normalized_input][-3:]
            
            # Store in memory system if available
            if self.memory_system:
                self.memory_system.add_learning_data("responses", {
                    "input": normalized_input,
                    "response": ai_response
                })
        
        except Exception as e:
            self.logger.error(f"Error learning from interaction: {e}")
    
    def _load_learned_data(self):
        """Load learned data from memory system"""
        try:
            if not self.memory_system:
                return
            
            learned_data = self.memory_system.get_learning_data("responses")
            
            for item in learned_data:
                data = item["data"]
                if "input" in data and "response" in data:
                    input_text = data["input"]
                    response = data["response"]
                    
                    if input_text not in self.learned_responses:
                        self.learned_responses[input_text] = []
                    
                    if response not in self.learned_responses[input_text]:
                        self.learned_responses[input_text].append(response)
            
            self.logger.info(f"Loaded {len(self.learned_responses)} learned response patterns")
            
        except Exception as e:
            self.logger.error(f"Error loading learned data: {e}")

    def set_response_language(self, language: str):
        """Set the preferred response language"""
        if language in ["auto", "en", "ar"]:
            self.response_language = language
            self.logger.info(f"🌍 Response language set to: {language}")
        else:
            self.logger.warning(f"Unsupported response language: {language}")

    def _detect_input_language_enhanced(self, text: str) -> str:
        """Enhanced language detection with better Arabic support"""
        if not text or not self.language_detection_enabled:
            return "en"

        # Count Arabic characters
        arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
        total_chars = len([char for char in text if char.isalpha()])

        if total_chars == 0:
            return "en"

        arabic_ratio = arabic_chars / total_chars

        # If more than 20% Arabic characters, consider it Arabic
        if arabic_ratio > 0.2:
            return "ar"

        # Also check for common Arabic words
        arabic_words = ["مرحبا", "السلام", "اسم", "حال", "شكر", "أنت", "أنا", "كيف", "ما", "من"]
        text_lower = text.lower()
        for word in arabic_words:
            if word in text_lower:
                return "ar"

        return "en"

    def get_language_stats(self) -> dict:
        """Get language detection statistics"""
        return {
            "response_language": self.response_language,
            "bilingual_mode": self.bilingual_mode,
            "language_detection_enabled": self.language_detection_enabled
        }

    def enable_bilingual_mode(self, enabled: bool = True):
        """Enable or disable bilingual mode"""
        self.bilingual_mode = enabled
        if enabled:
            self.response_language = "auto"
        self.logger.info(f"🌍 Bilingual mode: {'enabled' if enabled else 'disabled'}")
