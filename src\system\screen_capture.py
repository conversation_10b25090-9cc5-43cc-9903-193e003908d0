"""
Screen Capture functionality for Gideon AI Assistant
"""

import os
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, Tu<PERSON>

try:
    import mss
    MSS_AVAILABLE = True
except ImportError:
    MSS_AVAILABLE = False

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import pyautogui
    PYAUTOGUI_AVAILABLE = True
except ImportError:
    PYAUTOGUI_AVAILABLE = False

from src.utils.logger import <PERSON><PERSON>ogger
from src.utils.config import Config


class ScreenCapture:
    """Screen capture functionality"""
    
    def __init__(self):
        self.logger = GideonLogger("ScreenCapture")
        self.config = Config()
        self.screenshots_dir = Path("data/screenshots")
        
        # Ensure screenshots directory exists
        self.screenshots_dir.mkdir(parents=True, exist_ok=True)
        
        # Check available libraries
        self.mss_available = MSS_AVAILABLE
        self.pil_available = PIL_AVAILABLE
        self.pyautogui_available = PYAUTOGUI_AVAILABLE
        
        if not any([self.mss_available, self.pyautogui_available]):
            self.logger.warning("No screen capture libraries available")
    
    def is_available(self) -> bool:
        """Check if screen capture is available"""
        return self.mss_available or self.pyautogui_available
    
    def capture(self, filename: str = None, region: Tuple[int, int, int, int] = None) -> Optional[str]:
        """
        Capture screenshot
        
        Args:
            filename: Optional filename (will generate if not provided)
            region: Optional region to capture (x, y, width, height)
            
        Returns:
            Path to saved screenshot or None if failed
        """
        if not self.is_available():
            self.logger.error("Screen capture not available")
            return None
        
        try:
            # Generate filename if not provided
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"screenshot_{timestamp}.png"
            
            # Ensure filename has .png extension
            if not filename.endswith('.png'):
                filename += '.png'
            
            filepath = self.screenshots_dir / filename
            
            # Capture using available method
            if self.mss_available:
                success = self._capture_with_mss(filepath, region)
            elif self.pyautogui_available:
                success = self._capture_with_pyautogui(filepath, region)
            else:
                self.logger.error("No capture method available")
                return None
            
            if success:
                self.logger.info(f"Screenshot saved: {filepath}")
                return str(filepath)
            else:
                self.logger.error("Screenshot capture failed")
                return None
                
        except Exception as e:
            self.logger.error(f"Error capturing screenshot: {e}")
            return None
    
    def _capture_with_mss(self, filepath: Path, region: Tuple[int, int, int, int] = None) -> bool:
        """Capture screenshot using mss library"""
        try:
            with mss.mss() as sct:
                if region:
                    # Capture specific region
                    x, y, width, height = region
                    monitor = {
                        "top": y,
                        "left": x,
                        "width": width,
                        "height": height
                    }
                else:
                    # Capture primary monitor
                    monitor = sct.monitors[1]  # monitors[0] is all monitors combined
                
                # Capture
                screenshot = sct.grab(monitor)
                
                # Save
                mss.tools.to_png(screenshot.rgb, screenshot.size, output=str(filepath))
                
            return True
            
        except Exception as e:
            self.logger.error(f"MSS capture error: {e}")
            return False
    
    def _capture_with_pyautogui(self, filepath: Path, region: Tuple[int, int, int, int] = None) -> bool:
        """Capture screenshot using pyautogui library"""
        try:
            if region:
                x, y, width, height = region
                screenshot = pyautogui.screenshot(region=(x, y, width, height))
            else:
                screenshot = pyautogui.screenshot()
            
            screenshot.save(str(filepath))
            return True
            
        except Exception as e:
            self.logger.error(f"PyAutoGUI capture error: {e}")
            return False
    
    def capture_window(self, window_title: str = None) -> Optional[str]:
        """
        Capture specific window
        
        Args:
            window_title: Title of window to capture
            
        Returns:
            Path to saved screenshot or None if failed
        """
        if not self.pyautogui_available:
            self.logger.warning("Window capture requires pyautogui")
            return self.capture()  # Fallback to full screen
        
        try:
            # Find window
            windows = pyautogui.getWindowsWithTitle(window_title) if window_title else []
            
            if not windows:
                self.logger.warning(f"Window not found: {window_title}")
                return self.capture()  # Fallback to full screen
            
            window = windows[0]
            
            # Get window region
            region = (window.left, window.top, window.width, window.height)
            
            # Generate filename
            safe_title = "".join(c for c in window_title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"window_{safe_title}_{timestamp}.png"
            
            return self.capture(filename, region)
            
        except Exception as e:
            self.logger.error(f"Error capturing window: {e}")
            return self.capture()  # Fallback to full screen
    
    def capture_region(self, x: int, y: int, width: int, height: int, filename: str = None) -> Optional[str]:
        """
        Capture specific region of screen
        
        Args:
            x, y: Top-left coordinates
            width, height: Region dimensions
            filename: Optional filename
            
        Returns:
            Path to saved screenshot or None if failed
        """
        region = (x, y, width, height)
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"region_{x}_{y}_{width}_{height}_{timestamp}.png"
        
        return self.capture(filename, region)
    
    def get_screen_size(self) -> Tuple[int, int]:
        """Get screen size"""
        try:
            if self.pyautogui_available:
                return pyautogui.size()
            elif self.mss_available:
                with mss.mss() as sct:
                    monitor = sct.monitors[1]  # Primary monitor
                    return (monitor["width"], monitor["height"])
            else:
                return (1920, 1080)  # Default fallback
                
        except Exception as e:
            self.logger.error(f"Error getting screen size: {e}")
            return (1920, 1080)  # Default fallback
    
    def get_mouse_position(self) -> Tuple[int, int]:
        """Get current mouse position"""
        try:
            if self.pyautogui_available:
                return pyautogui.position()
            else:
                return (0, 0)
                
        except Exception as e:
            self.logger.error(f"Error getting mouse position: {e}")
            return (0, 0)
    
    def cleanup_old_screenshots(self, days: int = 7):
        """Clean up old screenshots"""
        try:
            cutoff_time = time.time() - (days * 24 * 60 * 60)
            deleted_count = 0
            
            for file_path in self.screenshots_dir.glob("*.png"):
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    deleted_count += 1
            
            if deleted_count > 0:
                self.logger.info(f"Cleaned up {deleted_count} old screenshots")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up screenshots: {e}")
    
    def get_screenshot_list(self, limit: int = 10) -> list:
        """Get list of recent screenshots"""
        try:
            screenshots = []
            
            for file_path in self.screenshots_dir.glob("*.png"):
                stat = file_path.stat()
                screenshots.append({
                    'filename': file_path.name,
                    'path': str(file_path),
                    'size': stat.st_size,
                    'created': datetime.fromtimestamp(stat.st_ctime),
                    'modified': datetime.fromtimestamp(stat.st_mtime)
                })
            
            # Sort by creation time (newest first)
            screenshots.sort(key=lambda x: x['created'], reverse=True)
            
            return screenshots[:limit]
            
        except Exception as e:
            self.logger.error(f"Error getting screenshot list: {e}")
            return []


# Fallback class when no capture libraries are available
class FallbackScreenCapture:
    """Fallback screen capture when libraries are not available"""
    
    def __init__(self):
        self.logger = GideonLogger("FallbackScreenCapture")
        self.logger.warning("Screen capture libraries not available")
    
    def is_available(self) -> bool:
        return False
    
    def capture(self, filename: str = None, region: Tuple[int, int, int, int] = None) -> Optional[str]:
        self.logger.warning("Screen capture not available")
        return None
    
    def capture_window(self, window_title: str = None) -> Optional[str]:
        self.logger.warning("Screen capture not available")
        return None
    
    def capture_region(self, x: int, y: int, width: int, height: int, filename: str = None) -> Optional[str]:
        self.logger.warning("Screen capture not available")
        return None
    
    def get_screen_size(self) -> Tuple[int, int]:
        return (1920, 1080)
    
    def get_mouse_position(self) -> Tuple[int, int]:
        return (0, 0)
    
    def cleanup_old_screenshots(self, days: int = 7):
        pass
    
    def get_screenshot_list(self, limit: int = 10) -> list:
        return []


# Use fallback if no capture libraries are available
if not (MSS_AVAILABLE or PYAUTOGUI_AVAILABLE):
    ScreenCapture = FallbackScreenCapture
