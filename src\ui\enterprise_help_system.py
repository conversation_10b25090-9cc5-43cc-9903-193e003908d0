#!/usr/bin/env python3
"""
Enterprise Help and Documentation System for Gideon AI Assistant
Professional in-app documentation, help system, tooltips, and contextual assistance
"""

import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from src.utils.logger import <PERSON><PERSON><PERSON><PERSON>

try:
    import customtkinter as ctk
    import tkinter as tk
    from tkinter import ttk
    CUSTOMTKINTER_AVAILABLE = True
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False

class EnterpriseHelpSystem:
    """Enterprise-grade help and documentation system"""
    
    def __init__(self):
        self.logger = GideonLogger("EnterpriseHelpSystem")
        
        # Help content directory
        self.help_dir = Path("data/help")
        self.help_dir.mkdir(parents=True, exist_ok=True)
        
        # Help window reference
        self.help_window = None
        
        # Design system for help interface
        self.design = {
            'colors': {
                'bg_primary': '#0a0e13',
                'bg_secondary': '#161b22',
                'bg_tertiary': '#21262d',
                'accent_primary': '#58a6ff',
                'accent_secondary': '#bc8cff',
                'text_primary': '#ffffff',
                'text_secondary': '#8b949e',
                'success': '#56d364',
                'warning': '#e3b341',
                'error': '#f85149'
            },
            'fonts': {
                'heading': ('Segoe UI Variable Display', 18, 'bold'),
                'subheading': ('Segoe UI Variable', 14, 'bold'),
                'body': ('Segoe UI Variable', 12, 'normal'),
                'code': ('Cascadia Code', 11, 'normal')
            }
        }
        
        # Help content structure
        self.help_content = {
            "getting_started": {
                "title": "Getting Started with Gideon AI Enterprise",
                "sections": [
                    {
                        "title": "Welcome to Gideon AI Assistant",
                        "content": """
Gideon AI Assistant Enterprise Edition is a professional-grade AI assistant designed for enterprise use. 
This guide will help you get started with all the powerful features available.

Key Features:
• Full HD (1920x1080) optimized interface
• High-DPI scaling for 4K displays
• Bilingual support (Arabic/English)
• Voice interaction with wake word detection
• Advanced AI model management
• Enterprise-grade security and data management
                        """
                    },
                    {
                        "title": "First Steps",
                        "content": """
1. Interface Overview: The main interface consists of a sidebar with navigation and status, 
   and a main content area for conversations.

2. Starting a Conversation: You can interact with Gideon in two ways:
   • Type your message in the chat input area
   • Use voice commands by saying "Gideon" followed by your question

3. Language Support: Gideon supports both Arabic (primary) and English (secondary).
   The interface automatically detects and responds in the appropriate language.
                        """
                    }
                ]
            },
            "voice_interaction": {
                "title": "Voice Interaction Guide",
                "sections": [
                    {
                        "title": "Voice Commands",
                        "content": """
Gideon supports advanced voice interaction with wake word detection.

Wake Word: "Gideon"
• Say "Gideon" followed by your question or command
• The system will automatically detect when you're speaking to Gideon
• Voice recognition works in both Arabic and English

Voice Settings:
• Adjust voice sensitivity in Settings > Voice
• Choose between different TTS voices
• Configure noise suppression and echo cancellation
                        """
                    },
                    {
                        "title": "Voice Troubleshooting",
                        "content": """
If voice interaction isn't working:

1. Check microphone permissions
2. Verify audio device settings
3. Adjust voice activation threshold
4. Test microphone in system settings
5. Restart audio services if needed

The system includes automatic audio device detection and recovery.
                        """
                    }
                ]
            },
            "ai_models": {
                "title": "AI Model Management",
                "sections": [
                    {
                        "title": "Managing AI Models",
                        "content": """
Gideon supports multiple AI models for enhanced capabilities:

Supported Models:
• Ollama models (dolphin-llama3:70b, qwen3:235b, gemma3:27b)
• Local transformer models
• Rule-based fallback system

Model Management:
• Drag and drop model files to add new models
• Switch between models in real-time
• Monitor model performance and resource usage
• Automatic fallback to rule-based responses if models fail
                        """
                    },
                    {
                        "title": "Model Performance",
                        "content": """
Optimizing AI Model Performance:

1. GPU Acceleration: Enable GPU acceleration in Settings > Performance
2. Memory Management: Monitor memory usage in the performance panel
3. Model Selection: Choose appropriate models based on your hardware
4. Caching: Enable response caching for faster repeated queries

The system automatically optimizes performance based on available resources.
                        """
                    }
                ]
            },
            "keyboard_shortcuts": {
                "title": "Keyboard Shortcuts",
                "sections": [
                    {
                        "title": "Essential Shortcuts",
                        "content": """
Master these keyboard shortcuts for efficient use:

Chat & Communication:
• Enter: Send message
• Shift+Enter: New line in message
• Ctrl+L: Clear chat history
• Ctrl+S: Save conversation

Navigation:
• F1: Show this help system
• Ctrl+,: Open settings
• Ctrl+Shift+T: Toggle terminal
• Ctrl+M: Toggle model manager

System:
• Ctrl+Q: Quit application
• F11: Toggle fullscreen
• Ctrl+R: Refresh interface
• Ctrl+Shift+D: Toggle debug mode
                        """
                    }
                ]
            },
            "enterprise_features": {
                "title": "Enterprise Features",
                "sections": [
                    {
                        "title": "Data Management",
                        "content": """
Enterprise Data Management Features:

Backup & Restore:
• Automatic daily backups
• Manual backup creation
• Full system restore capabilities
• Encrypted backup storage

Data Export:
• Export conversations in multiple formats (JSON, CSV, XML, SQLite)
• Bulk data export with date filtering
• Secure data transfer protocols

Data Retention:
• Configurable data retention policies
• Automatic cleanup of old data
• Compliance with data protection regulations
                        """
                    },
                    {
                        "title": "Security Features",
                        "content": """
Enterprise Security:

Authentication:
• Optional user authentication
• Session management
• Auto-lock functionality

Data Protection:
• End-to-end encryption
• Secure memory clearing
• Anonymized logging options
• Audit trail maintenance

Access Control:
• Role-based permissions
• Activity monitoring
• Secure configuration management
                        """
                    }
                ]
            },
            "troubleshooting": {
                "title": "Troubleshooting Guide",
                "sections": [
                    {
                        "title": "Common Issues",
                        "content": """
Common Issues and Solutions:

Performance Issues:
• Check system resources in Performance Monitor
• Reduce AI model complexity if needed
• Clear cache and temporary files
• Restart the application

Voice Recognition Problems:
• Verify microphone permissions
• Check audio device settings
• Adjust noise suppression settings
• Test with different wake word sensitivity

AI Response Issues:
• Check AI model status
• Verify internet connection for online models
• Try switching to a different model
• Check error logs for detailed information
                        """
                    },
                    {
                        "title": "Error Recovery",
                        "content": """
Automatic Error Recovery:

The system includes comprehensive error recovery:
• Automatic component restart
• Fallback to alternative systems
• Graceful degradation of features
• Detailed error reporting

Manual Recovery Steps:
1. Check the Performance Monitor for system status
2. Review error logs in the terminal
3. Try restarting individual components
4. Use the Settings > Advanced > Reset options if needed
5. Contact support with error reports if issues persist
                        """
                    }
                ]
            }
        }
        
        # Initialize help content files
        self._initialize_help_files()
    
    def _initialize_help_files(self):
        """Initialize help content files"""
        try:
            help_file = self.help_dir / "enterprise_help.json"
            if not help_file.exists():
                with open(help_file, 'w', encoding='utf-8') as f:
                    json.dump(self.help_content, f, indent=2, ensure_ascii=False)
                self.logger.info("Help content files initialized")
        except Exception as e:
            self.logger.error(f"Error initializing help files: {e}")
    
    def show_help_window(self, topic: Optional[str] = None):
        """Show the main help window"""
        if self.help_window and self.help_window.winfo_exists():
            self.help_window.lift()
            self.help_window.focus()
            return
        
        try:
            if CUSTOMTKINTER_AVAILABLE:
                self._create_modern_help_window(topic)
            else:
                self._create_basic_help_window(topic)
        except Exception as e:
            self.logger.error(f"Error showing help window: {e}")
    
    def _create_modern_help_window(self, topic: Optional[str] = None):
        """Create modern help window with CustomTkinter"""
        self.help_window = ctk.CTkToplevel()
        self.help_window.title("Gideon AI Enterprise - Help & Documentation")
        self.help_window.geometry("1200x800")
        self.help_window.minsize(800, 600)
        
        # Configure window
        self.help_window.configure(fg_color=self.design['colors']['bg_primary'])
        
        # Main container
        main_frame = ctk.CTkFrame(self.help_window, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Configure grid
        main_frame.grid_columnconfigure(1, weight=1)
        main_frame.grid_rowconfigure(0, weight=1)
        
        # Sidebar for navigation
        sidebar = ctk.CTkFrame(main_frame, width=250, fg_color=self.design['colors']['bg_secondary'])
        sidebar.grid(row=0, column=0, sticky="nsew", padx=(0, 20))
        sidebar.grid_propagate(False)
        
        # Sidebar title
        sidebar_title = ctk.CTkLabel(
            sidebar,
            text="📚 Help Topics",
            font=ctk.CTkFont(
                family=self.design['fonts']['heading'][0],
                size=self.design['fonts']['heading'][1],
                weight=self.design['fonts']['heading'][2]
            ),
            text_color=self.design['colors']['text_primary']
        )
        sidebar_title.pack(pady=(20, 10), padx=20)
        
        # Navigation buttons
        self.nav_buttons = {}
        for topic_key, topic_data in self.help_content.items():
            btn = ctk.CTkButton(
                sidebar,
                text=topic_data['title'],
                font=ctk.CTkFont(size=12),
                fg_color="transparent",
                text_color=self.design['colors']['text_secondary'],
                hover_color=self.design['colors']['bg_tertiary'],
                anchor="w",
                command=lambda k=topic_key: self._show_help_topic(k)
            )
            btn.pack(fill="x", padx=10, pady=2)
            self.nav_buttons[topic_key] = btn
        
        # Content area
        self.content_frame = ctk.CTkScrollableFrame(
            main_frame,
            fg_color=self.design['colors']['bg_secondary']
        )
        self.content_frame.grid(row=0, column=1, sticky="nsew")
        
        # Show initial topic
        initial_topic = topic if topic and topic in self.help_content else "getting_started"
        self._show_help_topic(initial_topic)
    
    def _create_basic_help_window(self, topic: Optional[str] = None):
        """Create basic help window with standard Tkinter"""
        self.help_window = tk.Toplevel()
        self.help_window.title("Gideon AI Enterprise - Help & Documentation")
        self.help_window.geometry("1000x700")
        self.help_window.configure(bg=self.design['colors']['bg_primary'])
        
        # Create notebook for tabs
        notebook = ttk.Notebook(self.help_window)
        notebook.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Add tabs for each help topic
        for topic_key, topic_data in self.help_content.items():
            frame = tk.Frame(notebook, bg=self.design['colors']['bg_secondary'])
            notebook.add(frame, text=topic_data['title'])
            
            # Add scrollable text widget
            text_widget = tk.Text(
                frame,
                bg=self.design['colors']['bg_secondary'],
                fg=self.design['colors']['text_primary'],
                font=self.design['fonts']['body'],
                wrap=tk.WORD,
                padx=20,
                pady=20
            )
            
            scrollbar = tk.Scrollbar(frame, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")
            
            # Add content
            content = f"{topic_data['title']}\n{'=' * len(topic_data['title'])}\n\n"
            for section in topic_data['sections']:
                content += f"{section['title']}\n{'-' * len(section['title'])}\n"
                content += f"{section['content'].strip()}\n\n"
            
            text_widget.insert("1.0", content)
            text_widget.configure(state="disabled")
    
    def _show_help_topic(self, topic_key: str):
        """Show specific help topic in the content area"""
        if not CUSTOMTKINTER_AVAILABLE or topic_key not in self.help_content:
            return
        
        # Clear content frame
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # Update navigation button states
        for key, btn in self.nav_buttons.items():
            if key == topic_key:
                btn.configure(
                    fg_color=self.design['colors']['accent_primary'],
                    text_color=self.design['colors']['text_primary']
                )
            else:
                btn.configure(
                    fg_color="transparent",
                    text_color=self.design['colors']['text_secondary']
                )
        
        # Get topic data
        topic_data = self.help_content[topic_key]
        
        # Topic title
        title_label = ctk.CTkLabel(
            self.content_frame,
            text=topic_data['title'],
            font=ctk.CTkFont(
                family=self.design['fonts']['heading'][0],
                size=20,
                weight="bold"
            ),
            text_color=self.design['colors']['text_primary']
        )
        title_label.pack(anchor="w", pady=(20, 10), padx=20)
        
        # Sections
        for section in topic_data['sections']:
            # Section title
            section_title = ctk.CTkLabel(
                self.content_frame,
                text=section['title'],
                font=ctk.CTkFont(
                    family=self.design['fonts']['subheading'][0],
                    size=self.design['fonts']['subheading'][1],
                    weight=self.design['fonts']['subheading'][2]
                ),
                text_color=self.design['colors']['accent_primary']
            )
            section_title.pack(anchor="w", pady=(15, 5), padx=20)
            
            # Section content
            content_label = ctk.CTkLabel(
                self.content_frame,
                text=section['content'].strip(),
                font=ctk.CTkFont(
                    family=self.design['fonts']['body'][0],
                    size=self.design['fonts']['body'][1]
                ),
                text_color=self.design['colors']['text_secondary'],
                justify="left",
                anchor="w"
            )
            content_label.pack(anchor="w", pady=(0, 10), padx=20, fill="x")
    
    def show_quick_help(self, topic: str, parent_widget=None):
        """Show quick help tooltip or popup"""
        quick_help_content = {
            "voice_button": "Click to start/stop voice recording. Say 'Gideon' + your question.",
            "send_button": "Send your message to Gideon AI. Shortcut: Enter",
            "model_selector": "Choose which AI model to use for responses.",
            "language_toggle": "Switch between Arabic and English languages.",
            "settings_button": "Open application settings and preferences.",
            "performance_monitor": "View real-time system performance metrics.",
            "terminal_button": "Open integrated terminal for system commands.",
            "export_button": "Export conversation data in various formats."
        }
        
        help_text = quick_help_content.get(topic, "No help available for this item.")
        
        if CUSTOMTKINTER_AVAILABLE and parent_widget:
            # Create tooltip-style help
            tooltip = ctk.CTkToplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.configure(fg_color=self.design['colors']['bg_tertiary'])
            
            label = ctk.CTkLabel(
                tooltip,
                text=help_text,
                font=ctk.CTkFont(size=11),
                text_color=self.design['colors']['text_primary'],
                wraplength=300
            )
            label.pack(padx=10, pady=5)
            
            # Position tooltip near parent widget
            x = parent_widget.winfo_rootx() + 20
            y = parent_widget.winfo_rooty() + parent_widget.winfo_height() + 5
            tooltip.geometry(f"+{x}+{y}")
            
            # Auto-hide after 3 seconds
            tooltip.after(3000, tooltip.destroy)
    
    def create_context_menu(self, parent_widget, items: List[Dict[str, Any]]):
        """Create context menu with help options"""
        if not CUSTOMTKINTER_AVAILABLE:
            return
        
        def show_context_menu(event):
            context_menu = tk.Menu(parent_widget, tearoff=0)
            
            for item in items:
                if item.get('separator'):
                    context_menu.add_separator()
                else:
                    context_menu.add_command(
                        label=item['label'],
                        command=item['command']
                    )
            
            # Add help option
            context_menu.add_separator()
            context_menu.add_command(
                label="Help",
                command=lambda: self.show_help_window()
            )
            
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
        
        parent_widget.bind("<Button-3>", show_context_menu)  # Right click
    
    def get_help_content(self, topic: str) -> Optional[Dict[str, Any]]:
        """Get help content for a specific topic"""
        return self.help_content.get(topic)
    
    def search_help(self, query: str) -> List[Dict[str, Any]]:
        """Search help content for specific terms"""
        results = []
        query_lower = query.lower()
        
        for topic_key, topic_data in self.help_content.items():
            # Search in title
            if query_lower in topic_data['title'].lower():
                results.append({
                    'topic': topic_key,
                    'title': topic_data['title'],
                    'type': 'title',
                    'content': topic_data['title']
                })
            
            # Search in sections
            for section in topic_data['sections']:
                if query_lower in section['title'].lower() or query_lower in section['content'].lower():
                    results.append({
                        'topic': topic_key,
                        'title': topic_data['title'],
                        'section': section['title'],
                        'type': 'section',
                        'content': section['content'][:200] + "..." if len(section['content']) > 200 else section['content']
                    })
        
        return results

# Global instance
enterprise_help_system = EnterpriseHelpSystem()
