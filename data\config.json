{"app": {"name": "Gideon AI Assistant", "version": "1.0.0", "theme": "dark", "language": "ar", "detected_language": "ar"}, "ui": {"window_width": 1200, "window_height": 800, "always_on_top": false, "transparency": 0.95, "font_family": "Segoe UI", "font_size": 12}, "speech": {"stt_enabled": true, "tts_enabled": true, "voice_activation": true, "wake_word": "gideon", "tts_rate": 200, "tts_volume": 0.8, "language": "auto", "primary_language": "ar-SA", "secondary_language": "en-US", "bilingual_mode": true, "tts_voice_id": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Speech\\Voices\\Tokens\\TTS_MS_EN-US_ZIRA_11.0", "tts_voice_name": "Microsoft Zira Desktop - English (United States)", "prefer_female_voice": true}, "ai": {"model_name": "gpt2", "max_tokens": 100, "temperature": 0.8, "response_timeout": 30, "always_use_llm": true, "fallback_to_rules": true, "preferred_backend": "ollama", "preferred_model": "dolphin-llama3:70b"}, "system": {"auto_start": false, "minimize_to_tray": true, "screen_capture_enabled": true, "voice_commands_enabled": true}, "memory": {"max_conversations": 1000, "auto_save": true, "learning_enabled": true}, "languages": {"supported": ["ar", "en"], "default": "ar", "primary": "ar", "secondary": "en", "rtl_support": true}, "models": {"max_models": 10, "auto_load": true, "default_model": null, "multi_model_enabled": true, "model_switching": true, "drag_drop_enabled": true}}