#!/usr/bin/env python3
"""
Debug Chat Issue - Quick diagnostic test
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def debug_chat_functionality():
    """Debug the chat functionality step by step"""
    print("🔍 Debugging Chat Functionality")
    print("=" * 50)
    
    try:
        # Test 1: Import modules
        print("Test 1: Importing modules...")
        from src.core.gideon_core import Gideon<PERSON>ore
        from src.ui.ultra_professional_interface import UltraProfessionalInterface
        print("✅ Modules imported successfully")
        
        # Test 2: Initialize core
        print("\nTest 2: Initializing Gideon core...")
        gideon_core = GideonCore()
        gideon_core.initialize()
        print("✅ Gideon core initialized")
        
        # Test 3: Check AI engine
        print("\nTest 3: Checking AI engine...")
        if hasattr(gideon_core, 'ai_engine') and gideon_core.ai_engine:
            print(f"✅ AI engine available: {type(gideon_core.ai_engine)}")
            print(f"   LLM enabled: {gideon_core.ai_engine.llm_enabled}")
            print(f"   Response language: {gideon_core.ai_engine.response_language}")
            
            # Test AI response generation
            try:
                test_response = gideon_core.ai_engine.generate_response("hello")
                print(f"   Test response: '{test_response[:50]}...'")
                print("✅ AI engine responding correctly")
            except Exception as e:
                print(f"⚠️ AI engine error: {e}")
        else:
            print("❌ AI engine not available")
        
        # Test 4: Create interface (without showing)
        print("\nTest 4: Creating interface...")
        interface = UltraProfessionalInterface(gideon_core)
        print("✅ Interface created")
        
        # Test 5: Check interface methods
        print("\nTest 5: Checking interface methods...")
        required_methods = ['_send_message', '_add_message', '_display_ai_response']
        for method in required_methods:
            if hasattr(interface, method):
                print(f"✅ Method '{method}' available")
            else:
                print(f"❌ Method '{method}' missing")
        
        # Test 6: Check if interface has gideon_core reference
        print("\nTest 6: Checking interface-core connection...")
        if hasattr(interface, 'gideon_core') and interface.gideon_core:
            print("✅ Interface has gideon_core reference")
            if hasattr(interface.gideon_core, 'ai_engine'):
                print("✅ Interface can access AI engine")
            else:
                print("❌ Interface cannot access AI engine")
        else:
            print("❌ Interface missing gideon_core reference")
        
        # Test 7: Test message processing logic
        print("\nTest 7: Testing message processing logic...")
        
        # Simulate the _send_message process
        test_message = "hello"
        print(f"Simulating message: '{test_message}'")
        
        # Check language detection
        try:
            from src.utils.text_direction import text_direction_manager
            input_language = "ar" if text_direction_manager.is_arabic_text(test_message) else "en"
            print(f"✅ Language detected: {input_language}")
        except Exception as e:
            print(f"❌ Language detection error: {e}")
        
        # Check AI response generation
        if interface.gideon_core and interface.gideon_core.ai_engine:
            try:
                # Set response language
                interface.gideon_core.ai_engine.set_response_language(input_language)
                
                # Generate response
                response = interface.gideon_core.ai_engine.generate_response(test_message)
                print(f"✅ AI response generated: '{response[:50]}...'")
                
                if not response or not response.strip():
                    print("⚠️ Empty response - will use fallback")
                    fallback_response = "Hello! I'm Gideon, your AI assistant. How can I help you today?"
                    print(f"✅ Fallback response: '{fallback_response[:50]}...'")
                
            except Exception as e:
                print(f"❌ AI response generation error: {e}")
        else:
            print("❌ Cannot test AI response - engine not available")
        
        # Test 8: Check input/output widgets
        print("\nTest 8: Checking UI widgets...")
        
        # Create window to check widgets
        try:
            root = interface.create_window()
            print("✅ Window created successfully")
            
            # Check if input entry exists
            if hasattr(interface, 'input_entry') and interface.input_entry:
                print("✅ Input entry widget available")
            else:
                print("❌ Input entry widget missing")
            
            # Check if chat display exists
            if hasattr(interface, 'chat_display') and interface.chat_display:
                print("✅ Chat display widget available")
            else:
                print("❌ Chat display widget missing")
            
            # Check if send button exists
            if hasattr(interface, 'send_button') and interface.send_button:
                print("✅ Send button widget available")
            else:
                print("❌ Send button widget missing")
            
            # Don't actually show the window
            root.destroy()
            
        except Exception as e:
            print(f"❌ Window creation error: {e}")
        
        print("\n" + "=" * 50)
        print("🔍 Chat Debug Summary:")
        print("If all tests pass, the issue might be:")
        print("1. Event binding not working (Enter key)")
        print("2. Button click handler not connected")
        print("3. Threading issue with AI responses")
        print("4. Widget focus or state issues")
        print("\n💡 Try typing a message and pressing Enter")
        print("💡 Check if the send button is clickable")
        print("💡 Look for any error messages in the console")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_chat_functionality()
