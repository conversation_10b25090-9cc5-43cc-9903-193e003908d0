#!/usr/bin/env python3
"""
Enterprise Assets Manager for Gideon AI Assistant
Handles high-resolution icons, vector graphics, and scalable UI elements
"""

import os
import base64
from pathlib import Path
from typing import Dict, Optional, Tuple
from src.utils.logger import GideonLogger

class EnterpriseAssetsManager:
    """Manages enterprise-grade assets with Full HD and high-DPI support"""
    
    def __init__(self):
        self.logger = GideonLogger("EnterpriseAssets")
        self.assets_dir = Path("assets")
        self.icons_dir = self.assets_dir / "icons"
        self.images_dir = self.assets_dir / "images"
        self.fonts_dir = self.assets_dir / "fonts"
        
        # Create directories if they don't exist
        self._ensure_directories()
        
        # High-resolution icon cache
        self.icon_cache = {}
        
        # Vector icon definitions (SVG-like)
        self.vector_icons = {
            'ai_brain': {
                'path': 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z',
                'viewBox': '0 0 24 24',
                'color': '#58a6ff'
            },
            'voice_wave': {
                'path': 'M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z',
                'viewBox': '0 0 24 24',
                'color': '#56d364'
            },
            'terminal': {
                'path': 'M2 3h20c1.1 0 2 .9 2 2v14c0 1.1-.9 2-2 2H2c-1.1 0-2-.9-2-2V5c0-1.1.9-2 2-2zm0 2v14h20V5H2zm2 2h2v2H4V7zm4 0h2v2H8V7zm-4 4h2v2H4v-2zm4 0h2v2H8v-2z',
                'viewBox': '0 0 24 24',
                'color': '#f78166'
            },
            'analytics': {
                'path': 'M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z',
                'viewBox': '0 0 24 24',
                'color': '#bc8cff'
            },
            'settings': {
                'path': 'M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z',
                'viewBox': '0 0 24 24',
                'color': '#e3b341'
            },
            'export': {
                'path': 'M9,16V10H5L12,3L19,10H15V16H9M5,20V18H19V20H5Z',
                'viewBox': '0 0 24 24',
                'color': '#56d364'
            },
            'import': {
                'path': 'M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z',
                'viewBox': '0 0 24 24',
                'color': '#58a6ff'
            },
            'help': {
                'path': 'M12,2C6.48,2 2,6.48 2,12C2,17.52 6.48,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2ZM13,19H11V17H13V19ZM15.07,11.25L14.17,12.17C13.45,12.9 13,13.5 13,15H11V14.5C11,13.4 11.45,12.4 12.17,11.67L13.41,10.41C13.78,10.05 14,9.55 14,9C14,7.9 13.1,7 12,7C10.9,7 10,7.9 10,9H8C8,6.79 9.79,5 12,5C14.21,5 16,6.79 16,9C16,9.88 15.64,10.68 15.07,11.25Z',
                'viewBox': '0 0 24 24',
                'color': '#f78166'
            }
        }
        
        # High-resolution image assets (base64 encoded for embedding)
        self.embedded_assets = {}
        
    def _ensure_directories(self):
        """Ensure asset directories exist"""
        for directory in [self.assets_dir, self.icons_dir, self.images_dir, self.fonts_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    def get_icon(self, name: str, size: int = 24, color: str = None, dpi_scale: float = 1.0) -> str:
        """Get vector icon as SVG string with DPI scaling"""
        if name not in self.vector_icons:
            self.logger.warning(f"Icon '{name}' not found, using fallback")
            return self._get_fallback_icon(size, color)
        
        icon_data = self.vector_icons[name]
        actual_size = int(size * dpi_scale)
        icon_color = color or icon_data.get('color', '#ffffff')
        
        # Generate SVG
        svg = f'''<svg width="{actual_size}" height="{actual_size}" viewBox="{icon_data['viewBox']}" 
                      xmlns="http://www.w3.org/2000/svg">
                    <path d="{icon_data['path']}" fill="{icon_color}" stroke="none"/>
                  </svg>'''
        
        return svg
    
    def _get_fallback_icon(self, size: int, color: str = None) -> str:
        """Get fallback icon when requested icon is not found"""
        color = color or '#ffffff'
        return f'''<svg width="{size}" height="{size}" viewBox="0 0 24 24" 
                       xmlns="http://www.w3.org/2000/svg">
                     <circle cx="12" cy="12" r="10" fill="{color}" opacity="0.3"/>
                     <text x="12" y="16" text-anchor="middle" fill="{color}" font-size="12">?</text>
                   </svg>'''
    
    def get_unicode_icon(self, name: str, fallback: str = "❓") -> str:
        """Get Unicode emoji icon as fallback"""
        unicode_icons = {
            'ai': '🤖',
            'voice': '🎤',
            'settings': '⚙️',
            'analytics': '📊',
            'terminal': '💻',
            'chat': '💬',
            'models': '🧠',
            'performance': '⚡',
            'status': '📡',
            'help': '❓',
            'export': '📤',
            'import': '📥',
            'save': '💾',
            'load': '📂',
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️'
        }
        return unicode_icons.get(name, fallback)
    
    def create_high_res_icon(self, name: str, base_path: str, sizes: list = None) -> bool:
        """Create high-resolution icon variants"""
        if sizes is None:
            sizes = [16, 24, 32, 48, 64, 128, 256]
        
        try:
            # This would integrate with an icon generation library
            # For now, we'll create placeholder high-res icons
            for size in sizes:
                icon_path = self.icons_dir / f"{name}_{size}x{size}.png"
                # Placeholder - in real implementation, would generate actual icons
                self.logger.info(f"Would create high-res icon: {icon_path}")
            
            return True
        except Exception as e:
            self.logger.error(f"Failed to create high-res icons for {name}: {e}")
            return False
    
    def get_font_path(self, font_name: str) -> Optional[str]:
        """Get path to custom font file"""
        font_extensions = ['.ttf', '.otf', '.woff2']
        
        for ext in font_extensions:
            font_path = self.fonts_dir / f"{font_name}{ext}"
            if font_path.exists():
                return str(font_path)
        
        return None
    
    def register_custom_font(self, font_name: str, font_path: str) -> bool:
        """Register a custom font for use in the application"""
        try:
            # This would integrate with the GUI framework's font system
            self.logger.info(f"Registering custom font: {font_name} from {font_path}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to register font {font_name}: {e}")
            return False

# Global instance
enterprise_assets = EnterpriseAssetsManager()
