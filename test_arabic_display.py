#!/usr/bin/env python3
"""
Test Arabic text display functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.text_direction import text_direction_manager

def test_arabic_processing():
    """Test Arabic text processing"""
    
    # Test Arabic text
    arabic_text = "مرحبا كيف حالك؟"
    english_text = "Hello, how are you?"
    mixed_text = "Hello مرحبا world"
    
    print("=== Arabic Text Processing Test ===")
    
    # Test direction detection
    print(f"\nDirection Detection:")
    print(f"Arabic text: '{arabic_text}' -> {text_direction_manager.detect_text_direction(arabic_text)}")
    print(f"English text: '{english_text}' -> {text_direction_manager.detect_text_direction(english_text)}")
    print(f"Mixed text: '{mixed_text}' -> {text_direction_manager.detect_text_direction(mixed_text)}")
    
    # Test text formatting
    print(f"\nText Formatting:")
    formatted_arabic, direction_ar = text_direction_manager.format_text_for_display(arabic_text)
    formatted_english, direction_en = text_direction_manager.format_text_for_display(english_text)
    formatted_mixed, direction_mx = text_direction_manager.format_text_for_display(mixed_text)
    
    print(f"Original Arabic: '{arabic_text}'")
    print(f"Formatted Arabic: '{formatted_arabic}' (direction: {direction_ar})")
    print(f"Original English: '{english_text}'")
    print(f"Formatted English: '{formatted_english}' (direction: {direction_en})")
    print(f"Original Mixed: '{mixed_text}'")
    print(f"Formatted Mixed: '{formatted_mixed}' (direction: {direction_mx})")
    
    # Test Arabic character detection
    print(f"\nArabic Character Detection:")
    print(f"Is Arabic: '{arabic_text}' -> {text_direction_manager.is_arabic_text(arabic_text)}")
    print(f"Is Arabic: '{english_text}' -> {text_direction_manager.is_arabic_text(english_text)}")
    print(f"Is Arabic: '{mixed_text}' -> {text_direction_manager.is_arabic_text(mixed_text)}")
    
    return True

if __name__ == "__main__":
    try:
        test_arabic_processing()
        print("\n✅ Arabic text processing test completed successfully!")
    except Exception as e:
        print(f"\n❌ Arabic text processing test failed: {e}")
        import traceback
        traceback.print_exc()
