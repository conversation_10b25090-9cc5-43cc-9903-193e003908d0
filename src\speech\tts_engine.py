"""
Text-to-Speech Engine for Gideon AI Assistant
Offline text-to-speech capabilities
"""

import threading
import time
from typing import Optional, Callable

try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False

from src.utils.logger import <PERSON><PERSON><PERSON><PERSON>
from src.utils.config import Config


class TTSEngine:
    """Text-to-Speech Engine"""
    
    def __init__(self):
        self.logger = GideonLogger("TTSEngine")
        self.config = Config()
        
        self.engine = None
        self.is_speaking = False
        self.is_initialized = False
        self.current_voice = None
        
        # Callbacks
        self.on_speech_started = None
        self.on_speech_finished = None
        self.on_error = None
        
        # Initialize if available
        if PYTTSX3_AVAILABLE:
            self._initialize()
    
    def _initialize(self):
        """Initialize text-to-speech engine"""
        try:
            self.engine = pyttsx3.init()
            
            # Configure engine
            self._configure_engine()
            
            # Set up callbacks
            self.engine.connect('started-utterance', self._on_speech_started)
            self.engine.connect('finished-utterance', self._on_speech_finished)
            
            self.is_initialized = True
            self.logger.info("Text-to-speech engine initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize TTS engine: {e}")
            self.is_initialized = False
    
    def _configure_engine(self):
        """Configure TTS engine settings"""
        try:
            # Set speech rate
            rate = self.config.get("speech.tts_rate", 200)
            self.engine.setProperty('rate', rate)
            
            # Set volume
            volume = self.config.get("speech.tts_volume", 0.8)
            self.engine.setProperty('volume', volume)
            
            # Set voice
            self._set_voice()
            
        except Exception as e:
            self.logger.error(f"Error configuring TTS engine: {e}")
    
    def _set_voice(self):
        """Set voice based on language and preferences - prioritize female voices"""
        try:
            voices = self.engine.getProperty('voices')
            if not voices:
                self.logger.warning("No voices available")
                return

            # Check if there's a preferred voice in config
            preferred_voice_id = self.config.get("speech.tts_voice_id")
            if preferred_voice_id:
                for voice in voices:
                    if voice.id == preferred_voice_id:
                        self.engine.setProperty('voice', voice.id)
                        self.current_voice = voice
                        self.logger.info(f"🎤 Using configured voice: {voice.name}")
                        return

            language = self.config.get("app.language", "en")

            # Try to find appropriate female voice
            selected_voice = None
            female_voices = []
            male_voices = []

            # Categorize voices by gender and language
            for voice in voices:
                voice_id = voice.id.lower()
                voice_name = voice.name.lower() if voice.name else ""

                # Check if it's the right language
                is_right_language = False
                if language == "ar":
                    # Arabic language detection
                    arabic_indicators = [
                        "arabic", "ar-", "saudi", "egypt", "uae", "lebanon", "jordan",
                        "morocco", "tunisia", "algeria", "iraq", "syria", "palestine",
                        "hoda", "naayf", "salma", "laila", "amina", "fatima"
                    ]
                    if any(indicator in voice_name or indicator in voice_id for indicator in arabic_indicators):
                        is_right_language = True
                elif language == "en":
                    # English language detection
                    english_indicators = [
                        "english", "en-", "united states", "us", "uk", "british", "american",
                        "australia", "canada", "ireland", "new zealand"
                    ]
                    if any(indicator in voice_name or indicator in voice_id for indicator in english_indicators):
                        is_right_language = True
                    elif not any(lang in voice_name for lang in ["spanish", "french", "german", "chinese", "japanese", "arabic", "hindi", "russian"]):
                        is_right_language = True  # Default to English if no specific language detected

                if is_right_language:
                    # Check for female indicators (English and Arabic)
                    female_indicators = [
                        # English female names
                        "zira", "hazel", "eva", "susan", "helen", "female", "woman", "girl",
                        "cortana", "helena", "mary", "sarah", "anna", "emma", "olivia",
                        # Arabic female names and indicators
                        "hoda", "salma", "laila", "amina", "fatima", "aisha", "khadija",
                        "maryam", "zahra", "nour", "rana", "dina", "sara", "lina", "maya",
                        "yasmin", "reem", "laith", "rania", "noura", "hala", "widad", "samira"
                    ]

                    male_indicators = [
                        # English male names
                        "david", "mark", "male", "man", "boy", "george", "richard", "james",
                        "john", "michael", "william", "robert",
                        # Arabic male names
                        "naayf", "ahmed", "mohammed", "ali", "omar", "hassan", "khalid",
                        "abdullah", "ibrahim", "youssef", "mahmoud", "saeed"
                    ]

                    # Check female indicators first (prioritize female voices)
                    is_female = any(female_indicator in voice_name for female_indicator in female_indicators)
                    is_male = any(male_indicator in voice_name for male_indicator in male_indicators)

                    if is_female:
                        female_voices.append(voice)
                        self.logger.info(f"✅ Found female voice: {voice.name}")
                    elif is_male:
                        male_voices.append(voice)
                        self.logger.debug(f"Found male voice: {voice.name}")
                    else:
                        # If gender is unclear, use heuristics (prefer female)
                        if voice_name.endswith("a") or "female" in voice_id.lower():
                            female_voices.append(voice)
                            self.logger.info(f"✅ Assumed female voice: {voice.name}")
                        else:
                            male_voices.append(voice)
                            self.logger.debug(f"Assumed male voice: {voice.name}")

            # Prioritize female voices
            if female_voices:
                selected_voice = female_voices[0]
                self.logger.info(f"🎤 Selected female voice: {selected_voice.name}")
            elif male_voices:
                selected_voice = male_voices[0]
                self.logger.warning(f"⚠️ No female voice found, using male voice: {selected_voice.name}")
            else:
                # Fallback to first available voice
                selected_voice = voices[0]
                self.logger.warning(f"⚠️ Using fallback voice: {selected_voice.name}")

            # Double-check: if we still have a male voice but female voices exist, switch
            if selected_voice and female_voices and selected_voice not in female_voices:
                self.logger.info("🔄 Switching to female voice for better user experience")
                selected_voice = female_voices[0]
                self.logger.info(f"🎤 Switched to female voice: {selected_voice.name}")

            self.engine.setProperty('voice', selected_voice.id)
            self.current_voice = selected_voice

            self.logger.info(f"Voice set to: {selected_voice.name if selected_voice.name else selected_voice.id}")

        except Exception as e:
            self.logger.error(f"Error setting voice: {e}")
    
    def is_available(self) -> bool:
        """Check if TTS is available"""
        return PYTTSX3_AVAILABLE and self.is_initialized
    
    def speak(self, text: str, wait: bool = True) -> bool:
        """Speak the given text"""
        if not self.is_available():
            self.logger.warning("TTS engine not available")
            return False
        
        if not text or not text.strip():
            return False
        
        try:
            self.logger.debug(f"Speaking: {text[:50]}...")
            
            if wait:
                # Synchronous speech
                self.is_speaking = True
                self.engine.say(text)
                self.engine.runAndWait()
                self.is_speaking = False
            else:
                # Asynchronous speech
                def speak_async():
                    self.is_speaking = True
                    self.engine.say(text)
                    self.engine.runAndWait()
                    self.is_speaking = False
                
                thread = threading.Thread(target=speak_async, daemon=True)
                thread.start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error during speech: {e}")
            self.is_speaking = False
            if self.on_error:
                self.on_error(str(e))
            return False
    
    def speak_async(self, text: str, callback: Callable[[bool], None] = None):
        """Speak text asynchronously with callback"""
        def speak_thread():
            success = self.speak(text, wait=True)
            if callback:
                callback(success)
        
        thread = threading.Thread(target=speak_thread, daemon=True)
        thread.start()
    
    def stop(self):
        """Stop current speech"""
        if self.is_available() and self.is_speaking:
            try:
                self.engine.stop()
                self.is_speaking = False
                self.logger.debug("Speech stopped")
            except Exception as e:
                self.logger.error(f"Error stopping speech: {e}")
    
    def pause(self):
        """Pause speech (if supported)"""
        # Note: pyttsx3 doesn't support pause/resume
        self.stop()
    
    def resume(self):
        """Resume speech (if supported)"""
        # Note: pyttsx3 doesn't support pause/resume
        pass
    
    def get_voices(self) -> list:
        """Get list of available voices"""
        if not self.is_available():
            return []
        
        try:
            voices = self.engine.getProperty('voices')
            voice_list = []
            
            for voice in voices:
                voice_info = {
                    'id': voice.id,
                    'name': voice.name if voice.name else voice.id,
                    'languages': getattr(voice, 'languages', []),
                    'gender': getattr(voice, 'gender', 'unknown')
                }
                voice_list.append(voice_info)
            
            return voice_list
            
        except Exception as e:
            self.logger.error(f"Error getting voices: {e}")
            return []
    
    def set_voice(self, voice_id: str) -> bool:
        """Set voice by ID"""
        if not self.is_available():
            return False
        
        try:
            voices = self.engine.getProperty('voices')
            
            for voice in voices:
                if voice.id == voice_id:
                    self.engine.setProperty('voice', voice_id)
                    self.current_voice = voice
                    self.logger.info(f"Voice changed to: {voice.name if voice.name else voice_id}")
                    return True
            
            self.logger.warning(f"Voice not found: {voice_id}")
            return False
            
        except Exception as e:
            self.logger.error(f"Error setting voice: {e}")
            return False
    
    def set_rate(self, rate: int) -> bool:
        """Set speech rate (words per minute)"""
        if not self.is_available():
            return False
        
        try:
            # Clamp rate to reasonable values
            rate = max(50, min(400, rate))
            self.engine.setProperty('rate', rate)
            self.config.set("speech.tts_rate", rate)
            self.logger.info(f"Speech rate set to: {rate}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting speech rate: {e}")
            return False
    
    def set_volume(self, volume: float) -> bool:
        """Set speech volume (0.0 to 1.0)"""
        if not self.is_available():
            return False
        
        try:
            # Clamp volume to valid range
            volume = max(0.0, min(1.0, volume))
            self.engine.setProperty('volume', volume)
            self.config.set("speech.tts_volume", volume)
            self.logger.info(f"Speech volume set to: {volume}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting speech volume: {e}")
            return False
    
    def get_current_settings(self) -> dict:
        """Get current TTS settings"""
        if not self.is_available():
            return {}
        
        try:
            return {
                'rate': self.engine.getProperty('rate'),
                'volume': self.engine.getProperty('volume'),
                'voice': self.current_voice.name if self.current_voice else None,
                'voice_id': self.current_voice.id if self.current_voice else None
            }
        except Exception as e:
            self.logger.error(f"Error getting TTS settings: {e}")
            return {}
    
    def test_speech(self, text: str = None) -> bool:
        """Test TTS functionality"""
        if not text:
            language = self.config.get("app.language", "en")
            if language == "ar":
                text = "مرحباً، أنا جيديون مساعدك الذكي"
            else:
                text = "Hello, I am Gideon, your AI assistant"
        
        return self.speak(text)
    
    # Callback methods
    def _on_speech_started(self, name):
        """Called when speech starts"""
        if self.on_speech_started:
            self.on_speech_started()
    
    def _on_speech_finished(self, name, completed):
        """Called when speech finishes"""
        if self.on_speech_finished:
            self.on_speech_finished(completed)
    
    # Callback setters
    def set_speech_started_callback(self, callback: Callable):
        """Set callback for when speech starts"""
        self.on_speech_started = callback
    
    def set_speech_finished_callback(self, callback: Callable[[bool], None]):
        """Set callback for when speech finishes"""
        self.on_speech_finished = callback
    
    def set_error_callback(self, callback: Callable[[str], None]):
        """Set callback for errors"""
        self.on_error = callback


# Fallback TTS Engine for when pyttsx3 is not available
class FallbackTTSEngine:
    """Fallback TTS Engine when pyttsx3 is not available"""
    
    def __init__(self):
        self.logger = GideonLogger("FallbackTTSEngine")
        self.logger.warning("TTS engine not available - using fallback")
        self.is_speaking = False
    
    def is_available(self) -> bool:
        return False
    
    def speak(self, text: str, wait: bool = True) -> bool:
        self.logger.warning(f"TTS not available - would speak: {text[:50]}...")
        return False
    
    def speak_async(self, text: str, callback: Callable[[bool], None] = None):
        if callback:
            callback(False)
    
    def stop(self):
        pass
    
    def pause(self):
        pass
    
    def resume(self):
        pass
    
    def get_voices(self) -> list:
        return []
    
    def set_voice(self, voice_id: str) -> bool:
        return False
    
    def set_rate(self, rate: int) -> bool:
        return False
    
    def set_volume(self, volume: float) -> bool:
        return False
    
    def get_current_settings(self) -> dict:
        return {}
    
    def test_speech(self, text: str = None) -> bool:
        return False
    
    def set_speech_started_callback(self, callback: Callable):
        pass
    
    def set_speech_finished_callback(self, callback: Callable[[bool], None]):
        pass
    
    def set_error_callback(self, callback: Callable[[str], None]):
        pass


# Use fallback if pyttsx3 is not available
if not PYTTSX3_AVAILABLE:
    TTSEngine = FallbackTTSEngine
