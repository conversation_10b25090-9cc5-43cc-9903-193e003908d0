#!/usr/bin/env python3
"""
Enterprise Settings Manager for Gideon AI Assistant
Comprehensive settings management, configuration panels, and user preferences
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, Callable
from src.utils.logger import <PERSON><PERSON>ogger
from src.utils.config import Config

try:
    import customtkinter as ctk
    CUSTOMTKINTER_AVAILABLE = True
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False

class EnterpriseSettingsManager:
    """Enterprise-grade settings management system"""
    
    def __init__(self):
        self.logger = GideonLogger("EnterpriseSettings")
        self.config = Config()
        
        # Settings file paths
        self.settings_dir = Path("data/settings")
        self.settings_dir.mkdir(parents=True, exist_ok=True)
        
        self.user_settings_file = self.settings_dir / "user_preferences.json"
        self.ui_settings_file = self.settings_dir / "ui_preferences.json"
        self.performance_settings_file = self.settings_dir / "performance_settings.json"
        
        # Default enterprise settings
        self.default_settings = {
            "display": {
                "resolution": "1920x1080",
                "dpi_scaling": "auto",
                "theme": "enterprise_dark",
                "font_family": "Segoe UI Variable",
                "font_size": 14,
                "animation_speed": "normal",
                "transparency": 0.98,
                "glass_effects": True,
                "smooth_scrolling": True,
                "high_contrast": False
            },
            "interface": {
                "sidebar_width": 320,
                "chat_font_size": 14,
                "show_timestamps": True,
                "show_status_bar": True,
                "show_performance_monitor": True,
                "compact_mode": False,
                "always_on_top": False,
                "minimize_to_tray": True,
                "startup_position": "center",
                "remember_window_state": True
            },
            "ai": {
                "default_model": "auto",
                "response_timeout": 30,
                "max_tokens": 2048,
                "temperature": 0.7,
                "enable_thinking_animation": True,
                "auto_save_conversations": True,
                "conversation_history_limit": 1000,
                "enable_context_memory": True
            },
            "voice": {
                "stt_enabled": True,
                "tts_enabled": True,
                "wake_word": "gideon",
                "voice_activation_threshold": 0.5,
                "tts_voice": "female",
                "tts_speed": 1.0,
                "tts_volume": 0.8,
                "noise_suppression": True,
                "echo_cancellation": True
            },
            "language": {
                "primary_language": "ar",
                "secondary_language": "en",
                "auto_detect_language": True,
                "rtl_support": True,
                "font_arabic": "Segoe UI Historic",
                "font_english": "Segoe UI Variable"
            },
            "performance": {
                "enable_gpu_acceleration": True,
                "max_cpu_usage": 80,
                "memory_limit_mb": 4096,
                "cache_size_mb": 512,
                "background_processing": True,
                "low_latency_mode": True,
                "optimize_for_battery": False
            },
            "security": {
                "enable_encryption": True,
                "auto_lock_timeout": 30,
                "require_authentication": False,
                "log_conversations": True,
                "anonymize_logs": False,
                "secure_memory_clear": True
            },
            "advanced": {
                "debug_mode": False,
                "verbose_logging": False,
                "experimental_features": False,
                "telemetry_enabled": False,
                "auto_update": True,
                "backup_settings": True
            }
        }
        
        # Current settings
        self.current_settings = self.default_settings.copy()
        self.load_settings()
        
        # Settings change callbacks
        self.change_callbacks = {}
    
    def load_settings(self):
        """Load settings from files"""
        try:
            # Load user preferences
            if self.user_settings_file.exists():
                with open(self.user_settings_file, 'r', encoding='utf-8') as f:
                    user_settings = json.load(f)
                    self._merge_settings(self.current_settings, user_settings)
            
            # Load UI preferences
            if self.ui_settings_file.exists():
                with open(self.ui_settings_file, 'r', encoding='utf-8') as f:
                    ui_settings = json.load(f)
                    self._merge_settings(self.current_settings, ui_settings)
            
            # Load performance settings
            if self.performance_settings_file.exists():
                with open(self.performance_settings_file, 'r', encoding='utf-8') as f:
                    perf_settings = json.load(f)
                    self._merge_settings(self.current_settings, perf_settings)
            
            self.logger.info("Enterprise settings loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Error loading settings: {e}")
            self.current_settings = self.default_settings.copy()
    
    def save_settings(self):
        """Save current settings to files"""
        try:
            # Save user preferences
            user_prefs = {
                "display": self.current_settings["display"],
                "interface": self.current_settings["interface"],
                "language": self.current_settings["language"]
            }
            with open(self.user_settings_file, 'w', encoding='utf-8') as f:
                json.dump(user_prefs, f, indent=2, ensure_ascii=False)
            
            # Save UI preferences
            ui_prefs = {
                "interface": self.current_settings["interface"],
                "display": self.current_settings["display"]
            }
            with open(self.ui_settings_file, 'w', encoding='utf-8') as f:
                json.dump(ui_prefs, f, indent=2)
            
            # Save performance settings
            perf_prefs = {
                "performance": self.current_settings["performance"],
                "ai": self.current_settings["ai"]
            }
            with open(self.performance_settings_file, 'w', encoding='utf-8') as f:
                json.dump(perf_prefs, f, indent=2)
            
            self.logger.info("Enterprise settings saved successfully")
            
        except Exception as e:
            self.logger.error(f"Error saving settings: {e}")
    
    def _merge_settings(self, target: Dict, source: Dict):
        """Recursively merge settings dictionaries"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_settings(target[key], value)
            else:
                target[key] = value
    
    def get_setting(self, path: str, default: Any = None) -> Any:
        """Get a setting value using dot notation (e.g., 'display.resolution')"""
        try:
            keys = path.split('.')
            value = self.current_settings
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set_setting(self, path: str, value: Any, save: bool = True):
        """Set a setting value using dot notation"""
        try:
            keys = path.split('.')
            target = self.current_settings
            
            # Navigate to the parent dictionary
            for key in keys[:-1]:
                if key not in target:
                    target[key] = {}
                target = target[key]
            
            # Set the value
            old_value = target.get(keys[-1])
            target[keys[-1]] = value
            
            # Trigger callbacks
            if path in self.change_callbacks:
                for callback in self.change_callbacks[path]:
                    try:
                        callback(old_value, value)
                    except Exception as e:
                        self.logger.error(f"Error in settings callback: {e}")
            
            # Save if requested
            if save:
                self.save_settings()
            
            self.logger.debug(f"Setting '{path}' changed from {old_value} to {value}")
            
        except Exception as e:
            self.logger.error(f"Error setting '{path}' to {value}: {e}")
    
    def register_change_callback(self, path: str, callback: Callable):
        """Register a callback for when a setting changes"""
        if path not in self.change_callbacks:
            self.change_callbacks[path] = []
        self.change_callbacks[path].append(callback)
    
    def reset_to_defaults(self, category: Optional[str] = None):
        """Reset settings to defaults"""
        if category:
            if category in self.default_settings:
                self.current_settings[category] = self.default_settings[category].copy()
                self.logger.info(f"Reset {category} settings to defaults")
        else:
            self.current_settings = self.default_settings.copy()
            self.logger.info("Reset all settings to defaults")
        
        self.save_settings()
    
    def export_settings(self, file_path: str) -> bool:
        """Export current settings to a file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.current_settings, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Settings exported to {file_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error exporting settings: {e}")
            return False
    
    def import_settings(self, file_path: str) -> bool:
        """Import settings from a file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            
            # Validate and merge imported settings
            self._merge_settings(self.current_settings, imported_settings)
            self.save_settings()
            
            self.logger.info(f"Settings imported from {file_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error importing settings: {e}")
            return False
    
    def get_display_settings(self) -> Dict[str, Any]:
        """Get display-related settings"""
        return self.current_settings.get("display", {})
    
    def get_performance_settings(self) -> Dict[str, Any]:
        """Get performance-related settings"""
        return self.current_settings.get("performance", {})
    
    def get_ai_settings(self) -> Dict[str, Any]:
        """Get AI-related settings"""
        return self.current_settings.get("ai", {})
    
    def get_voice_settings(self) -> Dict[str, Any]:
        """Get voice-related settings"""
        return self.current_settings.get("voice", {})
    
    def get_language_settings(self) -> Dict[str, Any]:
        """Get language-related settings"""
        return self.current_settings.get("language", {})

# Global instance
enterprise_settings = EnterpriseSettingsManager()
