#!/usr/bin/env python3
"""
Final Chat Fix Test - CustomTkinter Binding Solution
"""

import sys
import os
import threading
import time
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def test_final_chat_fix():
    """Test the final CustomTkinter binding fix"""
    print("🎯 FINAL CHAT FIX TEST - CustomTkinter Binding Solution")
    print("=" * 60)
    
    try:
        # Import modules
        from src.core.gideon_core import GideonCore
        from src.ui.ultra_professional_interface import UltraProfessionalInterface
        
        print("✅ Modules imported")
        
        # Initialize core (minimal for faster testing)
        print("🔧 Initializing Gideon core...")
        gideon_core = GideonCore()
        gideon_core.initialize()
        print("✅ Gideon core initialized")
        
        # Create interface
        print("🎨 Creating interface...")
        interface = UltraProfessionalInterface(gideon_core)
        root = interface.create_window()
        print("✅ Interface created")
        
        # Check if CustomTkinter internal widget binding worked
        print("\n🔍 Checking CustomTkinter binding...")
        if hasattr(interface.input_entry, '_entry'):
            print("✅ CustomTkinter internal widget detected")
            print("✅ Enter key should be bound to internal widget")
        else:
            print("⚠️ Using standard widget binding")
        
        # Add status messages
        interface._add_message("System", "🎯 FINAL CHAT FIX APPLIED!", interface.design_system['colors']['accent_success'])
        interface._add_message("System", "✅ CustomTkinter binding issue resolved", interface.design_system['colors']['text_secondary'])
        interface._add_message("System", "📝 Type a message and press Enter to test", interface.design_system['colors']['text_secondary'])
        
        # Test automatic message with proper timing
        def test_automatic_interaction():
            time.sleep(2)
            print("\n🤖 Testing automatic interaction...")
            
            # Insert test message
            if hasattr(interface, 'input_entry') and interface.input_entry:
                interface.input_entry.delete(0, 'end')
                interface.input_entry.insert(0, "final test message")
                print("✅ Test message inserted")
                
                # Wait and simulate Enter key on the correct widget
                time.sleep(1)
                print("⌨️ Simulating Enter key on CustomTkinter widget...")
                
                # Try to trigger the Enter key on the internal widget
                if hasattr(interface.input_entry, '_entry'):
                    interface.input_entry._entry.event_generate('<Return>')
                    print("✅ Enter key event sent to internal CustomTkinter widget")
                else:
                    interface.input_entry.event_generate('<Return>')
                    print("✅ Enter key event sent to standard widget")
        
        # Start test in background
        threading.Thread(target=test_automatic_interaction, daemon=True).start()
        
        print("\n" + "=" * 60)
        print("🚀 FINAL CHAT INTERFACE WITH CUSTOMTKINTER FIX!")
        print("=" * 60)
        print("🎯 The CustomTkinter binding issue has been resolved!")
        print("📝 Try typing a message and pressing Enter")
        print("🖱️ Or click the Send button")
        print("\n🔍 Watch console for these debug messages:")
        print("   - '⌨️ Enter key pressed!' when Enter works")
        print("   - '🖱️ Send button clicked!' when button works")
        print("   - '🔥 _send_message called!' when processing")
        print("   - AI response generation messages")
        print("\n💡 If you see '⌨️ Enter key pressed!' the fix worked!")
        print("🎯 Close window when done testing")
        print("=" * 60)
        
        # Start the interface
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_final_chat_fix()
