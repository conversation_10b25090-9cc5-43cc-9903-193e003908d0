#!/usr/bin/env python3
"""
Test script for terminal functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.system.terminal_manager import TerminalManager

def test_terminal_manager():
    """Test the terminal manager functionality"""
    print("🧪 Testing Terminal Manager...")
    
    try:
        # Create terminal manager
        terminal = TerminalManager()
        print("✅ Terminal manager created successfully")
        
        # Check status
        status = terminal.get_status()
        print(f"📊 Terminal status: {status}")
        
        # Test simple command
        print("\n🔧 Testing simple command: 'echo Hello World'")
        result = terminal.execute_command("echo Hello World", require_confirmation=False)
        print(f"📤 Command result: {result}")
        
        # Test directory listing
        print("\n📁 Testing directory listing...")
        import platform
        if platform.system().lower() == 'windows':
            result = terminal.execute_command("dir", require_confirmation=False)
        else:
            result = terminal.execute_command("ls", require_confirmation=False)
        print(f"📤 Directory listing result: {result.get('success', False)}")
        if result.get('output'):
            print(f"📄 Output preview: {result['output'][:200]}...")
        
        # Test command history
        history = terminal.get_command_history()
        print(f"\n📚 Command history: {len(history)} commands")
        
        # Test sessions
        sessions = terminal.get_sessions()
        print(f"\n🖥️ Active sessions: {len(sessions)}")
        
        # Cleanup
        terminal.shutdown()
        print("\n✅ Terminal manager test completed successfully!")
        
    except Exception as e:
        print(f"❌ Terminal manager test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_terminal_manager()
