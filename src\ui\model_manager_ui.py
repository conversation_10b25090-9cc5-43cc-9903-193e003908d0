"""
Model Manager UI with Drag and Drop Support
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from pathlib import Path
from typing import Optional, Callable

try:
    import customtkinter as ctk
    CUSTOMTKINTER_AVAILABLE = True
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False

from src.core.model_manager import ModelManager, ModelInfo
from src.utils.logger import Gideon<PERSON><PERSON><PERSON>
from src.utils.config import Config


class DragDropFrame:
    """Drag and drop frame for model files"""
    
    def __init__(self, parent, on_drop_callback: Callable[[str], None]):
        self.parent = parent
        self.on_drop_callback = on_drop_callback
        self.logger = GideonLogger("DragDropFrame")
        
        # Create drag and drop area
        if CUSTOMTKINTER_AVAILABLE:
            self.frame = ctk.CTkFrame(parent)
            self.label = ctk.CTkLabel(
                self.frame,
                text="🎯 Drag & Drop AI Models Here\n\nSupported formats: .gguf, .bin, .safetensors, .pt, .onnx\n\nOr click to browse files",
                font=ctk.CTkFont(size=14),
                justify="center"
            )
        else:
            self.frame = tk.Frame(parent, bg="#2b2b2b", relief="ridge", bd=2)
            self.label = tk.Label(
                self.frame,
                text="🎯 Drag & Drop AI Models Here\n\nSupported formats: .gguf, .bin, .safetensors, .pt, .onnx\n\nOr click to browse files",
                font=("Arial", 12),
                bg="#2b2b2b",
                fg="#ffffff",
                justify="center"
            )
        
        self.label.pack(expand=True, fill="both", padx=20, pady=20)
        
        # Bind events
        self._setup_drag_drop()
        self.label.bind("<Button-1>", self._on_click)
    
    def _setup_drag_drop(self):
        """Setup drag and drop functionality"""
        try:
            # Enable drag and drop (Windows specific)
            if os.name == 'nt':
                import tkinter.dnd as dnd
                
                def drop_enter(event):
                    self._highlight_drop_zone(True)
                    return "copy"
                
                def drop_leave(event):
                    self._highlight_drop_zone(False)
                
                def drop_files(event):
                    self._highlight_drop_zone(False)
                    files = event.data.split()
                    for file_path in files:
                        # Remove curly braces if present
                        file_path = file_path.strip('{}')
                        if os.path.isfile(file_path):
                            self.on_drop_callback(file_path)
                
                # Bind drag and drop events
                self.frame.drop_target_register("DND_Files")
                self.frame.dnd_bind("<<DropEnter>>", drop_enter)
                self.frame.dnd_bind("<<DropLeave>>", drop_leave)
                self.frame.dnd_bind("<<Drop>>", drop_files)
                
        except Exception as e:
            self.logger.warning(f"Drag and drop not available: {e}")
    
    def _highlight_drop_zone(self, highlight: bool):
        """Highlight drop zone during drag operation"""
        if CUSTOMTKINTER_AVAILABLE:
            if highlight:
                self.frame.configure(border_color="#00d4ff", border_width=3)
            else:
                self.frame.configure(border_color="gray", border_width=1)
        else:
            if highlight:
                self.frame.configure(bg="#004466", relief="solid", bd=3)
            else:
                self.frame.configure(bg="#2b2b2b", relief="ridge", bd=2)
    
    def _on_click(self, event):
        """Handle click to browse files"""
        file_path = filedialog.askopenfilename(
            title="Select AI Model File",
            filetypes=[
                ("All Supported", "*.gguf;*.bin;*.safetensors;*.pt;*.pth;*.onnx;*.tflite;*.h5"),
                ("GGUF Files", "*.gguf"),
                ("Binary Files", "*.bin"),
                ("SafeTensors", "*.safetensors"),
                ("PyTorch", "*.pt;*.pth"),
                ("ONNX", "*.onnx"),
                ("TensorFlow Lite", "*.tflite"),
                ("Keras/HDF5", "*.h5"),
                ("All Files", "*.*")
            ]
        )
        
        if file_path:
            self.on_drop_callback(file_path)


class ModelListWidget:
    """Widget to display and manage model list"""
    
    def __init__(self, parent, model_manager: ModelManager):
        self.parent = parent
        self.model_manager = model_manager
        self.logger = GideonLogger("ModelListWidget")
        
        # Create treeview for model list
        if CUSTOMTKINTER_AVAILABLE:
            self.frame = ctk.CTkFrame(parent)
        else:
            self.frame = tk.Frame(parent, bg="#2b2b2b")
        
        # Create treeview
        self.tree = ttk.Treeview(
            self.frame,
            columns=("name", "type", "size", "status", "capabilities"),
            show="headings",
            height=10
        )
        
        # Configure columns
        self.tree.heading("name", text="Model Name")
        self.tree.heading("type", text="Type")
        self.tree.heading("size", text="Size")
        self.tree.heading("status", text="Status")
        self.tree.heading("capabilities", text="Capabilities")
        
        self.tree.column("name", width=200)
        self.tree.column("type", width=100)
        self.tree.column("size", width=80)
        self.tree.column("status", width=80)
        self.tree.column("capabilities", width=200)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(self.frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack widgets
        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Context menu
        self._create_context_menu()
        
        # Bind events
        self.tree.bind("<Button-3>", self._show_context_menu)  # Right click
        self.tree.bind("<Double-1>", self._on_double_click)
        
        # Load models
        self.refresh_models()
    
    def _create_context_menu(self):
        """Create context menu for model items"""
        self.context_menu = tk.Menu(self.tree, tearoff=0)
        self.context_menu.add_command(label="Activate", command=self._activate_model)
        self.context_menu.add_command(label="Deactivate", command=self._deactivate_model)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="View Details", command=self._view_model_details)
        self.context_menu.add_command(label="Remove", command=self._remove_model)
    
    def _show_context_menu(self, event):
        """Show context menu"""
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def _on_double_click(self, event):
        """Handle double click on model"""
        self._view_model_details()
    
    def _activate_model(self):
        """Activate selected model"""
        selected = self.tree.selection()
        if selected:
            model_id = self.tree.item(selected[0])["values"][0]
            if self.model_manager.activate_model(model_id):
                self.refresh_models()
                messagebox.showinfo("Success", f"Model '{model_id}' activated")
    
    def _deactivate_model(self):
        """Deactivate selected model"""
        selected = self.tree.selection()
        if selected:
            model_id = self.tree.item(selected[0])["values"][0]
            if self.model_manager.deactivate_model(model_id):
                self.refresh_models()
                messagebox.showinfo("Success", f"Model '{model_id}' deactivated")
    
    def _remove_model(self):
        """Remove selected model"""
        selected = self.tree.selection()
        if selected:
            model_id = self.tree.item(selected[0])["values"][0]
            
            if messagebox.askyesno("Confirm", f"Remove model '{model_id}'?\nThis will delete the model file."):
                if self.model_manager.remove_model(model_id):
                    self.refresh_models()
                    messagebox.showinfo("Success", f"Model '{model_id}' removed")
    
    def _view_model_details(self):
        """View model details"""
        selected = self.tree.selection()
        if selected:
            model_id = self.tree.item(selected[0])["values"][0]
            model_info = self.model_manager.get_model_info(model_id)
            
            if model_info:
                details = f"""Model Details:

Name: {model_info.name}
Type: {model_info.model_type}
Size: {model_info.size / (1024*1024):.1f} MB
Path: {model_info.path}
Capabilities: {', '.join(model_info.capabilities)}
Added: {model_info.added_date}
Status: {'Active' if model_info.is_active else 'Inactive'}

Description: {model_info.description or 'No description'}"""
                
                messagebox.showinfo("Model Details", details)
    
    def refresh_models(self):
        """Refresh model list"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Add models
        for model_id, model_info in self.model_manager.models.items():
            size_mb = f"{model_info.size / (1024*1024):.1f} MB"
            status = "Active" if model_info.is_active else "Inactive"
            capabilities = ", ".join(model_info.capabilities[:3])  # Show first 3
            if len(model_info.capabilities) > 3:
                capabilities += "..."
            
            self.tree.insert("", "end", values=(
                model_info.name,
                model_info.model_type,
                size_mb,
                status,
                capabilities
            ))


class ModelManagerWindow:
    """Main model manager window"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.logger = GideonLogger("ModelManagerWindow")
        self.config = Config()
        self.model_manager = ModelManager()
        
        # Create window
        if CUSTOMTKINTER_AVAILABLE:
            self.window = ctk.CTkToplevel(parent) if parent else ctk.CTk()
        else:
            self.window = tk.Toplevel(parent) if parent else tk.Tk()
        
        self.window.title("Gideon AI - Model Manager")
        self.window.geometry("800x600")
        
        self._create_widgets()
        self._setup_layout()
    
    def _create_widgets(self):
        """Create UI widgets"""
        # Main container
        if CUSTOMTKINTER_AVAILABLE:
            self.main_frame = ctk.CTkFrame(self.window)
        else:
            self.main_frame = tk.Frame(self.window, bg="#2b2b2b")
        
        # Title
        if CUSTOMTKINTER_AVAILABLE:
            self.title_label = ctk.CTkLabel(
                self.main_frame,
                text="🤖 Gideon AI Model Manager",
                font=ctk.CTkFont(size=20, weight="bold")
            )
        else:
            self.title_label = tk.Label(
                self.main_frame,
                text="🤖 Gideon AI Model Manager",
                font=("Arial", 16, "bold"),
                bg="#2b2b2b",
                fg="#ffffff"
            )
        
        # Drag and drop area
        self.drag_drop = DragDropFrame(self.main_frame, self._on_model_dropped)
        
        # Model list
        self.model_list = ModelListWidget(self.main_frame, self.model_manager)
        
        # Buttons
        if CUSTOMTKINTER_AVAILABLE:
            self.button_frame = ctk.CTkFrame(self.main_frame)
            self.add_button = ctk.CTkButton(
                self.button_frame,
                text="📁 Add Model",
                command=self._add_model_dialog
            )
            self.refresh_button = ctk.CTkButton(
                self.button_frame,
                text="🔄 Refresh",
                command=self._refresh_models
            )
            self.stats_button = ctk.CTkButton(
                self.button_frame,
                text="📊 Statistics",
                command=self._show_stats
            )
        else:
            self.button_frame = tk.Frame(self.main_frame, bg="#2b2b2b")
            self.add_button = tk.Button(
                self.button_frame,
                text="📁 Add Model",
                command=self._add_model_dialog,
                bg="#4a9eff",
                fg="white"
            )
            self.refresh_button = tk.Button(
                self.button_frame,
                text="🔄 Refresh",
                command=self._refresh_models,
                bg="#4a9eff",
                fg="white"
            )
            self.stats_button = tk.Button(
                self.button_frame,
                text="📊 Statistics",
                command=self._show_stats,
                bg="#4a9eff",
                fg="white"
            )
    
    def _setup_layout(self):
        """Setup widget layout"""
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        self.title_label.pack(pady=(0, 10))
        
        # Drag and drop area
        self.drag_drop.frame.pack(fill="x", pady=(0, 10))
        
        # Model list
        self.model_list.frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # Buttons
        self.button_frame.pack(fill="x")
        self.add_button.pack(side="left", padx=(0, 5))
        self.refresh_button.pack(side="left", padx=(0, 5))
        self.stats_button.pack(side="left")
    
    def _on_model_dropped(self, file_path: str):
        """Handle model file drop"""
        self.logger.info(f"Model file dropped: {file_path}")
        
        # Show add model dialog with pre-filled path
        self._add_model_dialog(file_path)
    
    def _add_model_dialog(self, file_path: str = None):
        """Show add model dialog"""
        dialog = AddModelDialog(self.window, self.model_manager, file_path)
        if dialog.result:
            self._refresh_models()
    
    def _refresh_models(self):
        """Refresh model list"""
        self.model_list.refresh_models()
    
    def _show_stats(self):
        """Show model statistics"""
        stats = self.model_manager.get_model_stats()
        
        stats_text = f"""Model Statistics:

Total Models: {stats['total_models']}
Active Models: {stats['active_models']}
Total Size: {stats['total_size_mb']} MB

Model Types:"""
        
        for model_type, count in stats['types'].items():
            stats_text += f"\n  {model_type}: {count}"
        
        stats_text += f"\n\nSupported Formats:\n{', '.join(stats['supported_formats'])}"
        
        messagebox.showinfo("Model Statistics", stats_text)
    
    def show(self):
        """Show the window"""
        self.window.mainloop()


class AddModelDialog:
    """Dialog for adding a new model"""
    
    def __init__(self, parent, model_manager: ModelManager, file_path: str = None):
        self.parent = parent
        self.model_manager = model_manager
        self.result = None
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Add AI Model")
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Variables
        self.file_path_var = tk.StringVar(value=file_path or "")
        self.model_name_var = tk.StringVar()
        self.model_type_var = tk.StringVar(value="custom")
        self.description_var = tk.StringVar()
        
        self._create_widgets()
        self._setup_layout()
        
        # Auto-fill name if file path provided
        if file_path:
            self.model_name_var.set(Path(file_path).stem)
        
        # Center dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
        # Wait for dialog
        self.dialog.wait_window()
    
    def _create_widgets(self):
        """Create dialog widgets"""
        # File path
        tk.Label(self.dialog, text="Model File:").pack(anchor="w", padx=10, pady=(10, 0))
        file_frame = tk.Frame(self.dialog)
        file_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Entry(file_frame, textvariable=self.file_path_var, state="readonly").pack(side="left", fill="x", expand=True)
        tk.Button(file_frame, text="Browse", command=self._browse_file).pack(side="right", padx=(5, 0))
        
        # Model name
        tk.Label(self.dialog, text="Model Name:").pack(anchor="w", padx=10, pady=(10, 0))
        tk.Entry(self.dialog, textvariable=self.model_name_var).pack(fill="x", padx=10, pady=5)
        
        # Model type
        tk.Label(self.dialog, text="Model Type:").pack(anchor="w", padx=10, pady=(10, 0))
        type_combo = ttk.Combobox(
            self.dialog,
            textvariable=self.model_type_var,
            values=["chat", "code", "vision", "audio", "custom"],
            state="readonly"
        )
        type_combo.pack(fill="x", padx=10, pady=5)
        
        # Description
        tk.Label(self.dialog, text="Description:").pack(anchor="w", padx=10, pady=(10, 0))
        tk.Entry(self.dialog, textvariable=self.description_var).pack(fill="x", padx=10, pady=5)
        
        # Buttons
        button_frame = tk.Frame(self.dialog)
        button_frame.pack(fill="x", padx=10, pady=10)
        
        tk.Button(button_frame, text="Add Model", command=self._add_model).pack(side="right", padx=(5, 0))
        tk.Button(button_frame, text="Cancel", command=self._cancel).pack(side="right")
    
    def _setup_layout(self):
        """Setup layout"""
        pass  # Already done in _create_widgets
    
    def _browse_file(self):
        """Browse for model file"""
        file_path = filedialog.askopenfilename(
            title="Select AI Model File",
            filetypes=[
                ("All Supported", "*.gguf;*.bin;*.safetensors;*.pt;*.pth;*.onnx;*.tflite;*.h5"),
                ("All Files", "*.*")
            ]
        )
        
        if file_path:
            self.file_path_var.set(file_path)
            if not self.model_name_var.get():
                self.model_name_var.set(Path(file_path).stem)
    
    def _add_model(self):
        """Add the model"""
        file_path = self.file_path_var.get()
        model_name = self.model_name_var.get()
        model_type = self.model_type_var.get()
        description = self.description_var.get()
        
        if not file_path or not model_name:
            messagebox.showerror("Error", "Please provide file path and model name")
            return
        
        if not os.path.exists(file_path):
            messagebox.showerror("Error", "Model file not found")
            return
        
        # Add model
        model_id = self.model_manager.add_model_from_file(
            file_path, model_name, model_type, description
        )
        
        if model_id:
            self.result = model_id
            messagebox.showinfo("Success", f"Model '{model_name}' added successfully!")
            self.dialog.destroy()
        else:
            messagebox.showerror("Error", "Failed to add model")
    
    def _cancel(self):
        """Cancel dialog"""
        self.dialog.destroy()
