"""
Internationalization support for Gideon AI Assistant
Supports English and Arabic languages
"""

import json
import os
from pathlib import Path
from typing import Dict, Any

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False


class I18n:
    """Internationalization manager"""
    
    def __init__(self, default_language="ar"):
        self.current_language = default_language
        self.translations = {}
        self.load_translations()
    
    def load_translations(self):
        """Load translation files"""
        # English translations
        self.translations["en"] = {
            "app": {
                "title": "Gideon AI Assistant",
                "welcome": "Welcome to <PERSON>",
                "ready": "<PERSON> is ready",
                "listening": "Listening...",
                "processing": "Processing...",
                "speaking": "Speaking...",
                "error": "Error occurred"
            },
            "ui": {
                "chat": "Chat",
                "settings": "Settings",
                "voice": "Voice",
                "system": "System",
                "memory": "Memory",
                "help": "Help",
                "about": "About",
                "exit": "Exit",
                "minimize": "Minimize",
                "maximize": "Maximize",
                "close": "Close"
            },
            "speech": {
                "start_listening": "Start Listening",
                "stop_listening": "Stop Listening",
                "voice_input": "Voice Input",
                "text_input": "Text Input",
                "speak": "Speak",
                "mute": "Mute"
            },
            "settings": {
                "language": "Language",
                "theme": "Theme",
                "voice_settings": "Voice Settings",
                "ai_settings": "AI Settings",
                "system_settings": "System Settings",
                "save": "Save",
                "cancel": "Cancel",
                "reset": "Reset to Defaults"
            },
            "messages": {
                "hello": "Hello! I'm Gideon, your AI assistant. How can I help you today?",
                "goodbye": "Goodbye! Have a great day!",
                "error_speech": "Sorry, I couldn't understand what you said.",
                "error_processing": "I encountered an error while processing your request.",
                "thinking": "Let me think about that...",
                "working": "I'm working on it..."
            },
            "commands": {
                "screenshot": "Take Screenshot",
                "time": "What time is it?",
                "date": "What's the date?",
                "weather": "What's the weather?",
                "help": "Show help",
                "settings": "Open settings"
            }
        }
        
        # Arabic translations
        self.translations["ar"] = {
            "app": {
                "title": "مساعد جيديون الذكي",
                "welcome": "مرحباً بك في جيديون",
                "ready": "جيديون جاهز",
                "listening": "أستمع...",
                "processing": "معالجة...",
                "speaking": "أتحدث...",
                "error": "حدث خطأ"
            },
            "ui": {
                "chat": "محادثة",
                "settings": "إعدادات",
                "voice": "صوت",
                "system": "نظام",
                "memory": "ذاكرة",
                "help": "مساعدة",
                "about": "حول",
                "exit": "خروج",
                "minimize": "تصغير",
                "maximize": "تكبير",
                "close": "إغلاق"
            },
            "speech": {
                "start_listening": "بدء الاستماع",
                "stop_listening": "إيقاف الاستماع",
                "voice_input": "إدخال صوتي",
                "text_input": "إدخال نصي",
                "speak": "تحدث",
                "mute": "كتم الصوت"
            },
            "settings": {
                "language": "اللغة",
                "theme": "المظهر",
                "voice_settings": "إعدادات الصوت",
                "ai_settings": "إعدادات الذكاء الاصطناعي",
                "system_settings": "إعدادات النظام",
                "save": "حفظ",
                "cancel": "إلغاء",
                "reset": "إعادة تعيين الافتراضي"
            },
            "messages": {
                "hello": "مرحباً! أنا جيديون، مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟",
                "goodbye": "وداعاً! أتمنى لك يوماً رائعاً!",
                "error_speech": "عذراً، لم أستطع فهم ما قلته.",
                "error_processing": "واجهت خطأ أثناء معالجة طلبك.",
                "thinking": "دعني أفكر في ذلك...",
                "working": "أعمل على ذلك..."
            },
            "commands": {
                "screenshot": "التقاط لقطة شاشة",
                "time": "كم الساعة؟",
                "date": "ما هو التاريخ؟",
                "weather": "كيف الطقس؟",
                "help": "إظهار المساعدة",
                "settings": "فتح الإعدادات"
            }
        }
    
    def set_language(self, language_code: str):
        """Set current language"""
        if language_code in self.translations:
            self.current_language = language_code
            return True
        return False
    
    def get_text(self, key_path: str, default: str = None) -> str:
        """Get translated text using dot notation"""
        keys = key_path.split('.')
        translations = self.translations.get(self.current_language, {})
        
        try:
            for key in keys:
                translations = translations[key]
            
            text = translations
            
            # Apply Arabic text processing if needed
            if self.current_language == "ar" and ARABIC_SUPPORT:
                text = self.process_arabic_text(text)
            
            return text
        except (KeyError, TypeError):
            return default or key_path
    
    def process_arabic_text(self, text: str) -> str:
        """Process Arabic text for proper display"""
        if not ARABIC_SUPPORT:
            return text
        
        try:
            # Reshape Arabic text
            reshaped_text = arabic_reshaper.reshape(text)
            # Apply bidirectional algorithm
            display_text = get_display(reshaped_text)
            return display_text
        except Exception:
            return text
    
    def is_rtl(self, text: str = None) -> bool:
        """Check if current language or text is right-to-left"""
        if text:
            return self.detect_text_direction(text) == "rtl"
        return self.current_language == "ar"

    def detect_text_direction(self, text: str) -> str:
        """Detect text direction based on content"""
        if not text:
            return "ltr"

        # Count Arabic characters
        arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
        total_chars = len([char for char in text if char.isalpha()])

        if total_chars > 0 and arabic_chars / total_chars > 0.3:
            return "rtl"
        return "ltr"

    def format_text_with_direction(self, text: str) -> tuple:
        """Format text with proper direction and return (formatted_text, direction)"""
        direction = self.detect_text_direction(text)

        if direction == "rtl" and ARABIC_SUPPORT:
            # Process Arabic text for proper display
            formatted_text = self.process_arabic_text(text)
        else:
            formatted_text = text

        return formatted_text, direction
    
    def get_available_languages(self) -> Dict[str, str]:
        """Get available languages"""
        return {
            "en": "English",
            "ar": "العربية"
        }
    
    def get_current_language(self) -> str:
        """Get current language code"""
        return self.current_language
    
    # Convenience methods
    def t(self, key: str, default: str = None) -> str:
        """Short alias for get_text"""
        return self.get_text(key, default)


# Global instance
_i18n_instance = None

def get_i18n(language="ar") -> I18n:
    """Get global i18n instance"""
    global _i18n_instance
    if _i18n_instance is None:
        _i18n_instance = I18n(language)
    return _i18n_instance

def t(key: str, default: str = None) -> str:
    """Global translation function"""
    return get_i18n().get_text(key, default)
