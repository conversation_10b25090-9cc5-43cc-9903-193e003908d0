2025-06-08 00:01:24,696 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-08 00:01:24,696 - AIEngine - INFO - Initializing LLM backends...
2025-06-08 00:01:24,696 - AIEngine - INFO - ✅ Ollama backend available
2025-06-08 00:01:24,697 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-08 00:01:24,697 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-08 00:01:24,697 - AIEngine - INFO - ✅ Transformers backend available
2025-06-08 00:01:24,697 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-08 00:01:24,697 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-08 00:01:24,933 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-08 00:01:24,934 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-08 00:01:24,934 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-08 00:01:24,935 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-08 00:02:41,000 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-08 00:02:41,009 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-08 00:02:41,009 - AIEngine - INFO - AI Engine initialized successfully
2025-06-08 00:02:41,010 - AIEngine - INFO - Generating response for: 'Hello'
2025-06-08 00:02:41,012 - AIEngine - INFO - Attempting LLM response generation...
2025-06-08 00:02:41,013 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-08 00:02:41,014 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-08 00:02:41,015 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-08 00:02:41,017 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-08 00:02:52,152 - AIEngine - INFO - LLM response generated successfully: Hello! How can I help you today?...
2025-06-08 00:02:52,153 - AIEngine - INFO - LLM generated successful response: Hello! How can I help you today?...
2025-06-08 00:02:52,153 - AIEngine - INFO - Final response generated: Hello! How can I help you today?...
2025-06-08 00:27:44,625 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-08 00:27:44,626 - AIEngine - INFO - Initializing LLM backends...
2025-06-08 00:27:44,626 - AIEngine - INFO - ✅ Ollama backend available
2025-06-08 00:27:44,626 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-08 00:27:44,627 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-08 00:27:44,627 - AIEngine - INFO - ✅ Transformers backend available
2025-06-08 00:27:44,627 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-08 00:27:44,627 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-08 00:27:44,857 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-08 00:27:44,858 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-08 00:27:44,858 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-08 00:27:44,858 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-08 00:29:08,018 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-08 00:29:08,029 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-08 00:29:08,029 - AIEngine - INFO - AI Engine initialized successfully
2025-06-08 00:29:08,030 - AIEngine - INFO - Generating response for: 'Hello'
2025-06-08 00:29:08,032 - AIEngine - INFO - Attempting LLM response generation...
2025-06-08 00:29:08,032 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-08 00:29:08,033 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-08 00:29:08,033 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-08 00:29:08,035 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-08 00:29:20,876 - AIEngine - INFO - LLM response generated successfully: Hello! How can I help you today?...
2025-06-08 00:29:20,877 - AIEngine - INFO - LLM generated successful response: Hello! How can I help you today?...
2025-06-08 00:29:20,877 - AIEngine - INFO - Final response generated: Hello! How can I help you today?...
2025-06-08 00:52:29,135 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-08 00:52:29,137 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-08 00:52:29,141 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-08 00:52:29,142 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-08 00:52:29,142 - MemorySystem - INFO - Memory system initialized successfully
2025-06-08 00:52:29,144 - ModelManager - INFO - No existing models config found
2025-06-08 00:52:29,144 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-08 00:52:29,145 - AIEngine - INFO - Loaded 28 learned response patterns
2025-06-08 00:52:29,145 - AIEngine - INFO - Initializing LLM backends...
2025-06-08 00:52:29,146 - AIEngine - INFO - ✅ Ollama backend available
2025-06-08 00:52:29,146 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-08 00:52:29,146 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-08 00:52:29,147 - AIEngine - INFO - ✅ Transformers backend available
2025-06-08 00:52:29,147 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-08 00:52:29,147 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-08 00:52:29,377 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-08 00:52:29,378 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-08 00:52:29,379 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-08 00:52:29,379 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-08 00:54:01,263 - Gideon - INFO - 🤖 Initializing Gideon AI Assistant...
2025-06-08 00:54:01,265 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-08 00:54:01,271 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-08 00:54:01,271 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-08 00:54:01,272 - MemorySystem - INFO - Memory system initialized successfully
2025-06-08 00:54:01,273 - ModelManager - INFO - No existing models config found
2025-06-08 00:54:01,274 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-08 00:54:01,274 - AIEngine - INFO - Loaded 28 learned response patterns
2025-06-08 00:54:01,275 - AIEngine - INFO - Initializing LLM backends...
2025-06-08 00:54:01,275 - AIEngine - INFO - ✅ Ollama backend available
2025-06-08 00:54:01,275 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-08 00:54:01,275 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-08 00:54:01,276 - AIEngine - INFO - ✅ Transformers backend available
2025-06-08 00:54:01,276 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-08 00:54:01,276 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-08 00:54:01,523 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-08 00:54:01,525 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-08 00:54:01,525 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-08 00:54:01,525 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-08 00:54:08,171 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-08 00:54:08,172 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-08 00:54:08,172 - AIEngine - INFO - AI Engine initialized successfully
2025-06-08 00:54:08,343 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-08 00:54:08,401 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-08 00:54:08,579 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-08 00:54:08,579 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-08 00:54:11,590 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 7.165560873542597
2025-06-08 00:54:11,607 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-08 00:54:11,607 - STTEngine - INFO -    Energy threshold: 150
2025-06-08 00:54:11,607 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-08 00:54:11,607 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-08 00:54:11,607 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-08 00:54:11,607 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-08 00:54:11,608 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-08 00:54:11,608 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-08 00:54:11,608 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-08 00:54:11,987 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-08 00:54:11,987 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-08 00:54:11,987 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-08 00:54:15,240 - STTEngine - INFO - Testing microphone... Say something!
2025-06-08 00:54:18,415 - STTEngine - INFO - Microphone test successful. Heard: هلو
2025-06-08 00:54:20,538 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-08 00:54:20,538 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-08 00:54:20,538 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-08 00:54:20,539 - Gideon - INFO - ✅ Gideon AI Assistant initialized successfully!
2025-06-08 00:54:20,540 - Gideon - INFO - 🚀 Starting Gideon AI Assistant...
2025-06-08 00:54:21,289 - MainWindow - INFO - Main window created successfully
2025-06-08 00:54:33,388 - GideonCore - INFO - Processing text: علا
2025-06-08 00:54:33,389 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-08 00:54:33,389 - GideonCore - INFO - 🌍 Set AI engine response language directly to: ar
2025-06-08 00:54:33,389 - GideonCore - INFO - 🌍 Detected input language: ar
2025-06-08 00:54:33,390 - AIEngine - INFO - Generating response for: 'علا'
2025-06-08 00:54:33,390 - AIEngine - INFO - Attempting LLM response generation...
2025-06-08 00:54:33,390 - AIEngine - INFO - Generating response for: 'علا'
2025-06-08 00:54:33,391 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-08 00:54:33,391 - AIEngine - INFO - Attempting LLM response generation...
2025-06-08 00:54:33,391 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-08 00:54:33,391 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-08 00:54:33,391 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-08 00:54:33,391 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-08 00:54:33,391 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-08 00:54:33,391 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-08 00:54:33,392 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-08 00:54:33,509 - GideonCore - INFO - CONVERSATION - User: علا
2025-06-08 00:54:33,509 - GideonCore - INFO - CONVERSATION - AI: I'm processing that...
2025-06-08 00:54:52,781 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-08 00:54:53,676 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-08 00:54:53,704 - Gideon - INFO - 🔄 Shutting down Gideon AI Assistant...
2025-06-08 00:54:53,704 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-08 00:54:53,705 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-08 00:54:53,705 - Gideon - INFO - 👋 Gideon AI Assistant shutdown complete
2025-06-08 00:54:53,751 - STTEngine - INFO - Stopped continuous listening
2025-06-08 00:55:34,539 - AIEngine - INFO - LLM response generated successfully: أهلاً وسهلاً، ما الذي يمكنني مساعدتك في؟ نحن هنا ل...
2025-06-08 00:55:34,539 - AIEngine - INFO - LLM generated successful response: أهلاً وسهلاً، ما الذي يمكنني مساعدتك في؟ نحن هنا ل...
2025-06-08 00:55:34,544 - AIEngine - INFO - Final response generated: أهلاً وسهلاً، ما الذي يمكنني مساعدتك في؟ نحن هنا ل...
2025-06-08 00:56:04,041 - AIEngine - INFO - LLM response generated successfully: مرحباً، أنا جيديون، مساعد ذكيفودود. سعدني أنني قد ...
2025-06-08 00:56:04,042 - AIEngine - INFO - LLM generated successful response: مرحباً، أنا جيديون، مساعد ذكيفودود. سعدني أنني قد ...
2025-06-08 00:56:04,047 - AIEngine - INFO - Final response generated: مرحباً، أنا جيديون، مساعد ذكيفودود. سعدني أنني قد ...
2025-06-08 00:57:24,758 - Gideon - INFO - 🤖 Initializing Gideon AI Assistant...
2025-06-08 00:57:24,759 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-08 00:57:24,766 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-08 00:57:24,766 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-08 00:57:24,766 - MemorySystem - INFO - Memory system initialized successfully
2025-06-08 00:57:24,767 - ModelManager - INFO - No existing models config found
2025-06-08 00:57:24,768 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-08 00:57:24,769 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-08 00:57:24,769 - AIEngine - INFO - Initializing LLM backends...
2025-06-08 00:57:24,769 - AIEngine - INFO - ✅ Ollama backend available
2025-06-08 00:57:24,770 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-08 00:57:24,770 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-08 00:57:24,770 - AIEngine - INFO - ✅ Transformers backend available
2025-06-08 00:57:24,770 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-08 00:57:24,771 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-08 00:57:25,056 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-08 00:57:25,058 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-08 00:57:25,058 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-08 00:57:25,058 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-08 00:57:39,628 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-08 00:57:39,628 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-08 00:57:39,628 - AIEngine - INFO - AI Engine initialized successfully
2025-06-08 00:57:39,778 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-08 00:57:39,834 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-08 00:57:39,977 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-08 00:57:39,977 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-08 00:57:42,988 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-08 00:57:43,006 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-08 00:57:43,006 - STTEngine - INFO -    Energy threshold: 150
2025-06-08 00:57:43,006 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-08 00:57:43,006 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-08 00:57:43,007 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-08 00:57:43,007 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-08 00:57:43,007 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-08 00:57:43,007 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-08 00:57:43,007 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-08 00:57:43,188 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-08 00:57:43,188 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-08 00:57:43,188 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-08 00:57:46,441 - STTEngine - INFO - Testing microphone... Say something!
2025-06-08 00:57:48,939 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-08 00:57:53,119 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-08 00:57:53,120 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-08 00:57:53,120 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-08 00:57:53,121 - Gideon - INFO - ✅ Gideon AI Assistant initialized successfully!
2025-06-08 00:57:53,121 - Gideon - INFO - 🚀 Starting Gideon AI Assistant...
2025-06-08 00:57:53,546 - MainWindow - INFO - Main window created successfully
2025-06-08 00:58:00,091 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-08 00:58:00,188 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-08 00:58:00,209 - Gideon - INFO - 🔄 Shutting down Gideon AI Assistant...
2025-06-08 00:58:00,210 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-08 00:58:00,210 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-08 00:58:00,210 - Gideon - INFO - 👋 Gideon AI Assistant shutdown complete
2025-06-08 00:59:31,347 - Gideon - INFO - 🤖 Initializing Gideon AI Assistant...
2025-06-08 00:59:31,348 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-08 00:59:31,355 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-08 00:59:31,355 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-08 00:59:31,356 - MemorySystem - INFO - Memory system initialized successfully
2025-06-08 00:59:31,357 - ModelManager - INFO - No existing models config found
2025-06-08 00:59:31,358 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-08 00:59:31,358 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-08 00:59:31,359 - AIEngine - INFO - Initializing LLM backends...
2025-06-08 00:59:31,359 - AIEngine - INFO - ✅ Ollama backend available
2025-06-08 00:59:31,359 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-08 00:59:31,359 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-08 00:59:31,360 - AIEngine - INFO - ✅ Transformers backend available
2025-06-08 00:59:31,360 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-08 00:59:31,360 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-08 00:59:31,602 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-08 00:59:31,604 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-08 00:59:31,604 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-08 00:59:31,604 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-08 00:59:43,145 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-08 00:59:43,145 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-08 00:59:43,146 - AIEngine - INFO - AI Engine initialized successfully
2025-06-08 00:59:43,262 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-08 00:59:43,320 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-08 00:59:43,443 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-08 00:59:43,443 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-08 00:59:46,454 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 19.790306200433815
2025-06-08 00:59:46,470 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-08 00:59:46,471 - STTEngine - INFO -    Energy threshold: 150
2025-06-08 00:59:46,471 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-08 00:59:46,471 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-08 00:59:46,471 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-08 00:59:46,471 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-08 00:59:46,471 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-08 00:59:46,471 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-08 00:59:46,471 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-08 00:59:46,641 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-08 00:59:46,641 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-08 00:59:46,641 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-08 00:59:49,897 - STTEngine - INFO - Testing microphone... Say something!
2025-06-08 00:59:52,900 - STTEngine - INFO - Microphone test successful. Heard: ‏hello
2025-06-08 00:59:55,021 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-08 00:59:55,022 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-08 00:59:55,022 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-08 00:59:55,023 - Gideon - INFO - ✅ Gideon AI Assistant initialized successfully!
2025-06-08 00:59:55,023 - Gideon - INFO - 🚀 Starting Gideon AI Assistant...
2025-06-08 00:59:55,391 - MainWindow - INFO - Main window created successfully
2025-06-08 01:01:25,789 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-08 01:01:25,832 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-08 01:01:25,850 - Gideon - INFO - 🔄 Shutting down Gideon AI Assistant...
2025-06-08 01:01:25,850 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-08 01:01:25,850 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-08 01:01:25,851 - Gideon - INFO - 👋 Gideon AI Assistant shutdown complete
2025-06-08 01:03:11,332 - Gideon - INFO - 🤖 Initializing Gideon AI Assistant...
2025-06-08 01:03:11,333 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-08 01:03:11,339 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-08 01:03:11,340 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-08 01:03:11,340 - MemorySystem - INFO - Memory system initialized successfully
2025-06-08 01:03:11,341 - ModelManager - INFO - No existing models config found
2025-06-08 01:03:11,341 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-08 01:03:11,342 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-08 01:03:11,343 - AIEngine - INFO - Initializing LLM backends...
2025-06-08 01:03:11,343 - AIEngine - INFO - ✅ Ollama backend available
2025-06-08 01:03:11,343 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-08 01:03:11,344 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-08 01:03:11,344 - AIEngine - INFO - ✅ Transformers backend available
2025-06-08 01:03:11,344 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-08 01:03:11,344 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-08 01:03:11,589 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-08 01:03:11,591 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-08 01:03:11,591 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-08 01:03:11,591 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-08 01:03:48,577 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-08 01:03:48,583 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-08 01:03:48,583 - AIEngine - INFO - AI Engine initialized successfully
2025-06-08 01:03:48,782 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-08 01:03:48,839 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-08 01:03:48,966 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-08 01:03:48,967 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-08 01:03:51,977 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 3.00662074278988
2025-06-08 01:03:51,994 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-08 01:03:51,994 - STTEngine - INFO -    Energy threshold: 150
2025-06-08 01:03:51,994 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-08 01:03:51,994 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-08 01:03:51,994 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-08 01:03:51,994 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-08 01:03:51,995 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-08 01:03:51,995 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-08 01:03:51,995 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-08 01:03:52,244 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-08 01:03:52,244 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-08 01:03:52,244 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-08 01:03:55,498 - STTEngine - INFO - Testing microphone... Say something!
2025-06-08 01:03:58,525 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-08 01:04:02,715 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-08 01:04:02,716 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-08 01:04:02,716 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-08 01:04:02,719 - Gideon - INFO - ✅ Gideon AI Assistant initialized successfully!
2025-06-08 01:04:02,719 - Gideon - INFO - 🚀 Starting Gideon AI Assistant...
2025-06-08 01:04:03,098 - MainWindow - INFO - Main window created successfully
2025-06-08 01:04:11,797 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-08 01:04:11,802 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-08 01:04:11,822 - Gideon - INFO - 🔄 Shutting down Gideon AI Assistant...
2025-06-08 01:04:11,822 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-08 01:04:11,822 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-08 01:04:11,822 - Gideon - INFO - 👋 Gideon AI Assistant shutdown complete
2025-06-08 01:04:11,903 - STTEngine - INFO - Stopped continuous listening
2025-06-08 01:04:52,946 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-08 01:04:52,948 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-08 01:04:52,954 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-08 01:04:52,954 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-08 01:04:52,955 - MemorySystem - INFO - Memory system initialized successfully
2025-06-08 01:04:52,956 - ModelManager - INFO - No existing models config found
2025-06-08 01:04:52,957 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-08 01:04:52,958 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-08 01:04:52,958 - AIEngine - INFO - Initializing LLM backends...
2025-06-08 01:04:52,959 - AIEngine - INFO - ✅ Ollama backend available
2025-06-08 01:04:52,959 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-08 01:04:52,959 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-08 01:04:52,960 - AIEngine - INFO - ✅ Transformers backend available
2025-06-08 01:04:52,960 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-08 01:04:52,960 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-08 01:04:53,234 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-08 01:04:53,235 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-08 01:04:53,235 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-08 01:04:53,236 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-08 01:05:17,376 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-08 01:05:17,380 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-08 01:05:17,380 - AIEngine - INFO - AI Engine initialized successfully
2025-06-08 01:05:17,567 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-08 01:05:17,625 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-08 01:05:17,760 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-08 01:05:17,760 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-08 01:05:20,771 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-08 01:05:20,787 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-08 01:05:20,787 - STTEngine - INFO -    Energy threshold: 150
2025-06-08 01:05:20,788 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-08 01:05:20,788 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-08 01:05:20,788 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-08 01:05:20,788 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-08 01:05:20,788 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-08 01:05:20,789 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-08 01:05:20,789 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-08 01:05:21,065 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-08 01:05:21,066 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-08 01:05:21,066 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-08 01:05:24,316 - STTEngine - INFO - Testing microphone... Say something!
2025-06-08 01:05:27,342 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-08 01:05:31,522 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-08 01:05:31,522 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-08 01:05:31,522 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-08 01:05:32,280 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-08 01:05:32,280 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-08 01:05:32,320 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-08 01:05:32,326 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition started!...
2025-06-08 01:05:32,326 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:05:32] ⚙️ System: 🚀 Gideon...
2025-06-08 01:05:32,521 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:05:32,521 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:05:32,522 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Welcome to the ultimate professional AI assistan...
2025-06-08 01:05:32,522 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:05:32] ⚙️ System: 💼 Welcom...
2025-06-08 01:05:32,526 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:05:32,526 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:05:32,526 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 All enterprise features are active and ready...
2025-06-08 01:05:32,527 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:05:32] ⚙️ System: 🎯 All en...
2025-06-08 01:05:32,531 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:05:32,531 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:05:32,531 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model active: dolphin-llama3:70b...
2025-06-08 01:05:32,531 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:05:32] ⚙️ System: 🧠 Enterp...
2025-06-08 01:05:32,532 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:05:32,532 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:05:32,532 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex tasks...
2025-06-08 01:05:32,533 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:05:32] ⚙️ System: 💡 Advanc...
2025-06-08 01:05:32,536 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:05:32,536 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:05:32,536 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-08 01:05:32,537 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:05:32] ⚙️ System: 🚀 Gideon...
2025-06-08 01:05:32,537 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:05:32,537 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:05:32,537 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-08 01:05:32,538 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:05:32] ⚙️ System: 💼 Ultra-...
2025-06-08 01:05:32,538 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:05:32,538 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:05:32,539 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-08 01:05:32,539 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:05:32] ⚙️ System: 🎯 Real-t...
2025-06-08 01:05:32,539 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:05:32,540 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:05:32,540 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-08 01:05:32,540 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:05:32] ⚙️ System: 🧠 Enterp...
2025-06-08 01:05:32,541 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:05:32,541 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:05:32,541 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-08 01:05:32,541 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:05:32] ⚙️ System: 💡 Advanc...
2025-06-08 01:05:32,542 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:05:32,542 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:05:32,543 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-08 01:05:32,543 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:05:32] ⚙️ System: 💬 Say 'G...
2025-06-08 01:05:32,543 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:05:32,543 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:05:34,546 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-08 01:05:34,547 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:05:34] 🤖 Gideon: Hello! I'...
2025-06-08 01:05:34,547 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:05:34,548 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:05:46,930 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> اهلا...
2025-06-08 01:05:46,934 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫﺍ :You 👤 [01:05:46]
...
2025-06-08 01:05:46,949 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:05:46,949 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:05:46,957 - AIEngine - INFO - Generating response for: 'اهلا'
2025-06-08 01:05:46,958 - AIEngine - INFO - Attempting LLM response generation...
2025-06-08 01:05:46,958 - AIEngine - INFO - Generating response for: 'اهلا'
2025-06-08 01:05:46,958 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-08 01:05:46,958 - AIEngine - INFO - Attempting LLM response generation...
2025-06-08 01:05:46,959 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-08 01:05:46,959 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-08 01:05:46,959 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-08 01:05:46,959 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-08 01:05:46,959 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-08 01:05:46,960 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-08 01:05:46,966 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-08 01:05:47,062 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-08 01:05:47,062 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'اهلا' -> 'I'm processing that......'
2025-06-08 01:05:47,083 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-08 01:05:47,083 - UltraProfessionalInterface - INFO - 🚀 FORCING MESSAGE DISPLAY: 'I'm processing that......'
2025-06-08 01:05:47,084 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-08 01:05:47,084 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:05:47] 🤖 Gideon: I'm proce...
2025-06-08 01:05:47,109 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:05:47,109 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:06:16,709 - AIEngine - INFO - LLM response generated successfully: مرحباً، سعدني أنني هنا لمساعدتك. إذا كان لديك أي أ...
2025-06-08 01:06:16,709 - AIEngine - INFO - LLM generated successful response: مرحباً، سعدني أنني هنا لمساعدتك. إذا كان لديك أي أ...
2025-06-08 01:06:16,719 - AIEngine - INFO - Final response generated: مرحباً، سعدني أنني هنا لمساعدتك. إذا كان لديك أي أ...
2025-06-08 01:06:20,078 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-08 01:06:20,369 - STTEngine - INFO - Stopped continuous listening
2025-06-08 01:06:20,912 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-08 01:06:21,022 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-08 01:06:24,922 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-08 01:06:24,922 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-08 01:06:24,926 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-08 01:13:23,350 - EnterpriseMain - INFO - 🤖 Starting Gideon AI Assistant - Enterprise Edition
2025-06-08 01:13:23,352 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-08 01:13:23,358 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-08 01:13:23,359 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-08 01:13:23,359 - MemorySystem - INFO - Memory system initialized successfully
2025-06-08 01:13:23,360 - ModelManager - INFO - No existing models config found
2025-06-08 01:13:23,361 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-08 01:13:23,362 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-08 01:13:23,362 - AIEngine - INFO - Initializing LLM backends...
2025-06-08 01:13:23,363 - AIEngine - INFO - ✅ Ollama backend available
2025-06-08 01:13:23,363 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-08 01:13:23,363 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-08 01:13:23,363 - AIEngine - INFO - ✅ Transformers backend available
2025-06-08 01:13:23,363 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-08 01:13:23,364 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-08 01:13:23,613 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-08 01:13:23,615 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-08 01:13:23,615 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-08 01:13:23,615 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-08 01:14:41,001 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-08 01:14:41,011 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-08 01:14:41,011 - AIEngine - INFO - AI Engine initialized successfully
2025-06-08 01:14:41,274 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-08 01:14:41,330 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-08 01:14:41,470 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-08 01:14:41,470 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-08 01:14:44,481 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-08 01:14:44,497 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-08 01:14:44,497 - STTEngine - INFO -    Energy threshold: 150
2025-06-08 01:14:44,498 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-08 01:14:44,498 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-08 01:14:44,498 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-08 01:14:44,498 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-08 01:14:44,498 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-08 01:14:44,499 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-08 01:14:44,499 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-08 01:14:45,053 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-08 01:14:45,053 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-08 01:14:45,053 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-08 01:14:48,318 - STTEngine - INFO - Testing microphone... Say something!
2025-06-08 01:14:51,344 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-08 01:14:55,531 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-08 01:14:55,532 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-08 01:14:55,532 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-08 01:14:56,385 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-08 01:14:56,385 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-08 01:14:56,425 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-08 01:14:56,432 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition - READY!...
2025-06-08 01:14:56,432 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:56] ⚙️ System: 🚀 Gideon...
2025-06-08 01:14:56,678 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:56,679 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:14:56,679 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional AI assistant with Flash-inspi...
2025-06-08 01:14:56,679 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:56] ⚙️ System: 💼 Ultra-...
2025-06-08 01:14:56,684 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:56,684 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:14:56,685 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> � Bilingual support active: Arabic (primary) / Eng...
2025-06-08 01:14:56,685 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:56] ⚙️ System: � Biling...
2025-06-08 01:14:56,688 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:56,688 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:14:56,688 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎤 Voice interaction ready - Say 'Gideon' + your qu...
2025-06-08 01:14:56,688 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:56] ⚙️ System: 🎤 Voice ...
2025-06-08 01:14:56,692 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:56,692 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:14:56,692 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ⚡ Ultra-low latency mode active for instant respon...
2025-06-08 01:14:56,693 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:56] ⚙️ System: ⚡ Ultra-...
2025-06-08 01:14:56,693 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:56,693 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:14:56,694 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 AI Model loaded: dolphin-llama3:70b...
2025-06-08 01:14:56,694 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:56] ⚙️ System: 🧠 AI Mod...
2025-06-08 01:14:56,694 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:56,694 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:14:56,694 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready - Multi-backend s...
2025-06-08 01:14:56,694 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:56] ⚙️ System: 💡 Advanc...
2025-06-08 01:14:56,698 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:56,698 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:14:56,698 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🔄 Drag & drop new models to expand AI capabilities...
2025-06-08 01:14:56,698 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:56] ⚙️ System: 🔄 Drag &...
2025-06-08 01:14:56,699 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:56,699 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:14:56,699 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-08 01:14:56,700 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:56] ⚙️ System: 🚀 Gideon...
2025-06-08 01:14:56,700 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:56,700 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:14:56,701 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-08 01:14:56,701 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:56] ⚙️ System: 💼 Ultra-...
2025-06-08 01:14:56,701 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:56,702 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:14:56,702 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-08 01:14:56,702 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:56] ⚙️ System: 🎯 Real-t...
2025-06-08 01:14:56,702 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:56,703 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:14:56,703 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-08 01:14:56,703 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:56] ⚙️ System: 🧠 Enterp...
2025-06-08 01:14:56,704 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:56,704 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:14:56,704 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-08 01:14:56,704 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:56] ⚙️ System: 💡 Advanc...
2025-06-08 01:14:56,705 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:56,705 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:14:56,705 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-08 01:14:56,705 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:56] ⚙️ System: 💬 Say 'G...
2025-06-08 01:14:56,705 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:56,705 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:14:58,710 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-08 01:14:58,710 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:14:58] 🤖 Gideon: Hello! I'...
2025-06-08 01:14:58,710 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:14:58,711 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:15:11,129 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> hi...
2025-06-08 01:15:11,129 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:15:11] 👤 You: hi
...
2025-06-08 01:15:11,143 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:15:11,143 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:15:11,157 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:15:11,157 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'hi' -> 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:15:11,180 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:15:11,180 - UltraProfessionalInterface - INFO - 🚀 FORCING MESSAGE DISPLAY: 'Hi there! I'm Gideon, your AI ...'
2025-06-08 01:15:11,180 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hi there! I'm Gideon, your AI assistant. What can ...
2025-06-08 01:15:11,180 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:15:11] 🤖 Gideon: Hi there!...
2025-06-08 01:15:11,187 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:15:11,188 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:15:11,188 - UltraProfessionalInterface - ERROR - Error handling text response: 'UltraProfessionalInterface' object has no attribute '_stream_response'
2025-06-08 01:15:11,188 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ❌ Error displaying response: 'UltraProfessionalInt...
2025-06-08 01:15:11,188 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:15:11] ⚙️ System: ❌ Error ...
2025-06-08 01:15:11,194 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:15:11,194 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:18:10,598 - EnterpriseMain - INFO - 🤖 Starting Gideon AI Assistant - Enterprise Edition
2025-06-08 01:18:10,600 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-08 01:18:10,607 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-08 01:18:10,607 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-08 01:18:10,607 - MemorySystem - INFO - Memory system initialized successfully
2025-06-08 01:18:10,609 - ModelManager - INFO - No existing models config found
2025-06-08 01:18:10,610 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-08 01:18:10,610 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-08 01:18:10,611 - AIEngine - INFO - Initializing LLM backends...
2025-06-08 01:18:10,611 - AIEngine - INFO - ✅ Ollama backend available
2025-06-08 01:18:10,611 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-08 01:18:10,612 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-08 01:18:10,612 - AIEngine - INFO - ✅ Transformers backend available
2025-06-08 01:18:10,612 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-08 01:18:10,612 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-08 01:18:10,895 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-08 01:18:10,897 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-08 01:18:10,897 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-08 01:18:10,897 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-08 01:18:43,529 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-08 01:18:43,530 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-08 01:18:43,531 - AIEngine - INFO - AI Engine initialized successfully
2025-06-08 01:18:43,660 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-08 01:18:43,719 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-08 01:18:43,842 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-08 01:18:43,842 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-08 01:18:46,838 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-08 01:18:46,850 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-08 01:18:46,851 - STTEngine - INFO -    Energy threshold: 150
2025-06-08 01:18:46,851 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-08 01:18:46,851 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-08 01:18:46,851 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-08 01:18:46,851 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-08 01:18:46,851 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-08 01:18:46,852 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-08 01:18:46,852 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-08 01:18:47,094 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-08 01:18:47,094 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-08 01:18:47,094 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-08 01:18:50,337 - STTEngine - INFO - Testing microphone... Say something!
2025-06-08 01:18:53,346 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-08 01:18:57,528 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-08 01:18:57,529 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-08 01:18:57,529 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-08 01:18:58,229 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-08 01:18:58,229 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-08 01:18:58,267 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-08 01:18:58,271 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition - READY!...
2025-06-08 01:18:58,272 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:18:58] ⚙️ System: 🚀 Gideon...
2025-06-08 01:18:58,472 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:18:58,472 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:18:58,472 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional AI assistant with Flash-inspi...
2025-06-08 01:18:58,473 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:18:58] ⚙️ System: 💼 Ultra-...
2025-06-08 01:18:58,476 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:18:58,476 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:18:58,476 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> � Bilingual support active: Arabic (primary) / Eng...
2025-06-08 01:18:58,477 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:18:58] ⚙️ System: � Biling...
2025-06-08 01:18:58,479 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:18:58,479 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:18:58,480 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎤 Voice interaction ready - Say 'Gideon' + your qu...
2025-06-08 01:18:58,480 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:18:58] ⚙️ System: 🎤 Voice ...
2025-06-08 01:18:58,483 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:18:58,483 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:18:58,483 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ⚡ Ultra-low latency mode active for instant respon...
2025-06-08 01:18:58,483 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:18:58] ⚙️ System: ⚡ Ultra-...
2025-06-08 01:18:58,484 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:18:58,485 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:18:58,485 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 AI Model loaded: dolphin-llama3:70b...
2025-06-08 01:18:58,485 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:18:58] ⚙️ System: 🧠 AI Mod...
2025-06-08 01:18:58,485 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:18:58,485 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:18:58,485 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready - Multi-backend s...
2025-06-08 01:18:58,486 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:18:58] ⚙️ System: 💡 Advanc...
2025-06-08 01:18:58,489 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:18:58,490 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:18:58,490 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🔄 Drag & drop new models to expand AI capabilities...
2025-06-08 01:18:58,490 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:18:58] ⚙️ System: 🔄 Drag &...
2025-06-08 01:18:58,490 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:18:58,491 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:18:58,491 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-08 01:18:58,491 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:18:58] ⚙️ System: 🚀 Gideon...
2025-06-08 01:18:58,491 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:18:58,492 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:18:58,492 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-08 01:18:58,492 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:18:58] ⚙️ System: 💼 Ultra-...
2025-06-08 01:18:58,492 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:18:58,492 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:18:58,492 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-08 01:18:58,493 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:18:58] ⚙️ System: 🎯 Real-t...
2025-06-08 01:18:58,493 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:18:58,493 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:18:58,493 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-08 01:18:58,494 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:18:58] ⚙️ System: 🧠 Enterp...
2025-06-08 01:18:58,494 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:18:58,494 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:18:58,494 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-08 01:18:58,494 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:18:58] ⚙️ System: 💡 Advanc...
2025-06-08 01:18:58,495 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:18:58,495 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:18:58,495 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-08 01:18:58,495 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:18:58] ⚙️ System: 💬 Say 'G...
2025-06-08 01:18:58,496 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:18:58,496 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:19:00,496 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-08 01:19:00,496 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:00] 🤖 Gideon: Hello! I'...
2025-06-08 01:19:00,497 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:19:00,497 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:19:33,273 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-08 01:19:33,549 - STTEngine - INFO - Stopped continuous listening
2025-06-08 01:19:34,037 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-08 01:19:34,121 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-08 01:19:43,750 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> hi...
2025-06-08 01:19:43,750 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:43] 👤 You: hi
...
2025-06-08 01:19:43,764 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:19:43,765 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:19:43,783 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:19:43,783 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'hi' -> 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:19:43,810 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:19:43,810 - UltraProfessionalInterface - INFO - 🚀 FORCING MESSAGE DISPLAY: 'Hi there! I'm Gideon, your AI ...'
2025-06-08 01:19:43,810 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hi there! I'm Gideon, your AI assistant. What can ...
2025-06-08 01:19:43,810 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:43] 🤖 Gideon: Hi there!...
2025-06-08 01:19:43,835 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:19:43,835 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:19:43,836 - UltraProfessionalInterface - INFO - 🎬 Starting streaming effect for response: 'Hi there! I'm Gideon, your AI ...'
2025-06-08 01:19:44,048 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hi there! I'm Gideon, your AI assistant. What can ...
2025-06-08 01:19:44,048 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:44] 🤖 Gideon: Hi there!...
2025-06-08 01:19:44,054 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:19:44,055 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:19:44,055 - UltraProfessionalInterface - INFO - ✅ Streaming effect completed
2025-06-08 01:19:48,643 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> hi...
2025-06-08 01:19:48,644 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:48] 👤 You: hi
...
2025-06-08 01:19:48,651 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:19:48,651 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:19:48,653 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:19:48,653 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'hi' -> 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:19:48,675 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:19:48,675 - UltraProfessionalInterface - INFO - 🚀 FORCING MESSAGE DISPLAY: 'Hi there! I'm Gideon, your AI ...'
2025-06-08 01:19:48,675 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hi there! I'm Gideon, your AI assistant. What can ...
2025-06-08 01:19:48,675 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:48] 🤖 Gideon: Hi there!...
2025-06-08 01:19:48,697 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:19:48,697 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:19:48,698 - UltraProfessionalInterface - INFO - 🎬 Starting streaming effect for response: 'Hi there! I'm Gideon, your AI ...'
2025-06-08 01:19:48,698 - TTSEngine - ERROR - Error during speech: run loop already started
2025-06-08 01:19:48,910 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hi there! I'm Gideon, your AI assistant. What can ...
2025-06-08 01:19:48,910 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:19:48] 🤖 Gideon: Hi there!...
2025-06-08 01:19:48,914 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:19:48,914 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:19:48,915 - UltraProfessionalInterface - INFO - ✅ Streaming effect completed
2025-06-08 01:23:04,378 - EnterpriseMain - INFO - 🤖 Starting Gideon AI Assistant - Enterprise Edition
2025-06-08 01:23:04,380 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-08 01:23:04,385 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-08 01:23:04,385 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-08 01:23:04,385 - MemorySystem - INFO - Memory system initialized successfully
2025-06-08 01:23:04,386 - ModelManager - INFO - No existing models config found
2025-06-08 01:23:04,387 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-08 01:23:04,388 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-08 01:23:04,388 - AIEngine - INFO - Initializing LLM backends...
2025-06-08 01:23:04,389 - AIEngine - INFO - ✅ Ollama backend available
2025-06-08 01:23:04,389 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-08 01:23:04,389 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-08 01:23:04,389 - AIEngine - INFO - ✅ Transformers backend available
2025-06-08 01:23:04,389 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-08 01:23:04,389 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-08 01:23:04,632 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-08 01:23:04,634 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-08 01:23:04,634 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-08 01:23:04,634 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-08 01:23:15,297 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-08 01:23:16,024 - STTEngine - INFO - Stopped continuous listening
2025-06-08 01:23:16,076 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-08 01:23:16,167 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-08 01:23:30,620 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-08 01:23:30,621 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-08 01:23:30,621 - AIEngine - INFO - AI Engine initialized successfully
2025-06-08 01:23:30,751 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-08 01:23:30,806 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-08 01:23:30,931 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-08 01:23:30,931 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-08 01:23:33,941 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-08 01:23:33,957 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-08 01:23:33,957 - STTEngine - INFO -    Energy threshold: 150
2025-06-08 01:23:33,957 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-08 01:23:33,957 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-08 01:23:33,957 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-08 01:23:33,958 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-08 01:23:33,958 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-08 01:23:33,958 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-08 01:23:33,958 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-08 01:23:34,170 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-08 01:23:34,170 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-08 01:23:34,170 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-08 01:23:37,423 - STTEngine - INFO - Testing microphone... Say something!
2025-06-08 01:23:40,450 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-08 01:23:44,633 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-08 01:23:44,633 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-08 01:23:44,633 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-08 01:23:45,330 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-08 01:23:45,330 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-08 01:23:45,367 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-08 01:23:45,372 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition - READY!...
2025-06-08 01:23:45,372 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:45] ⚙️ System: 🚀 Gideon...
2025-06-08 01:23:45,549 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:45,550 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:23:45,550 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional AI assistant with Flash-inspi...
2025-06-08 01:23:45,550 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:45] ⚙️ System: 💼 Ultra-...
2025-06-08 01:23:45,553 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:45,553 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:23:45,554 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> � Bilingual support active: Arabic (primary) / Eng...
2025-06-08 01:23:45,554 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:45] ⚙️ System: � Biling...
2025-06-08 01:23:45,557 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:45,557 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:23:45,557 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎤 Voice interaction ready - Say 'Gideon' + your qu...
2025-06-08 01:23:45,558 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:45] ⚙️ System: 🎤 Voice ...
2025-06-08 01:23:45,561 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:45,561 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:23:45,561 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ⚡ Ultra-low latency mode active for instant respon...
2025-06-08 01:23:45,561 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:45] ⚙️ System: ⚡ Ultra-...
2025-06-08 01:23:45,561 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:45,562 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:23:45,562 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 AI Model loaded: dolphin-llama3:70b...
2025-06-08 01:23:45,562 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:45] ⚙️ System: 🧠 AI Mod...
2025-06-08 01:23:45,562 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:45,562 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:23:45,562 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready - Multi-backend s...
2025-06-08 01:23:45,563 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:45] ⚙️ System: 💡 Advanc...
2025-06-08 01:23:45,566 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:45,566 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:23:45,566 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🔄 Drag & drop new models to expand AI capabilities...
2025-06-08 01:23:45,566 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:45] ⚙️ System: 🔄 Drag &...
2025-06-08 01:23:45,567 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:45,567 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:23:45,567 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-08 01:23:45,567 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:45] ⚙️ System: 🚀 Gideon...
2025-06-08 01:23:45,568 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:45,568 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:23:45,568 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-08 01:23:45,568 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:45] ⚙️ System: 💼 Ultra-...
2025-06-08 01:23:45,568 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:45,569 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:23:45,569 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-08 01:23:45,569 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:45] ⚙️ System: 🎯 Real-t...
2025-06-08 01:23:45,569 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:45,569 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:23:45,570 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-08 01:23:45,570 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:45] ⚙️ System: 🧠 Enterp...
2025-06-08 01:23:45,570 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:45,570 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:23:45,570 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-08 01:23:45,570 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:45] ⚙️ System: 💡 Advanc...
2025-06-08 01:23:45,571 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:45,571 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:23:45,571 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-08 01:23:45,571 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:45] ⚙️ System: 💬 Say 'G...
2025-06-08 01:23:45,572 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:45,572 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:23:47,572 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-08 01:23:47,573 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:23:47] 🤖 Gideon: Hello! I'...
2025-06-08 01:23:47,573 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:23:47,573 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:25:49,844 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> hi...
2025-06-08 01:25:49,856 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:25:49] 👤 You: hi
...
2025-06-08 01:25:49,873 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:25:49,874 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:25:49,909 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:25:49,909 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'hi' -> 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:25:49,930 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:25:49,930 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'Hi there! I'm Gideon, your AI ...'
2025-06-08 01:25:49,930 - UltraProfessionalInterface - INFO - 📝 Using streaming effect for long response
2025-06-08 01:25:49,930 - UltraProfessionalInterface - INFO - 🎬 Starting streaming effect for response: 'Hi there! I'm Gideon, your AI ...'
2025-06-08 01:25:50,136 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hi there! I'm Gideon, your AI assistant. What can ...
2025-06-08 01:25:50,136 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:25:50] 🤖 Gideon: Hi there!...
2025-06-08 01:25:50,141 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:25:50,142 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:25:50,142 - UltraProfessionalInterface - INFO - ✅ Streaming effect completed
2025-06-08 01:25:54,850 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> اهلا...
2025-06-08 01:25:54,863 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫﺍ :You 👤 [01:25:54]
...
2025-06-08 01:25:54,872 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:25:54,872 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:25:54,875 - AIEngine - INFO - Generating response for: 'اهلا'
2025-06-08 01:25:54,882 - AIEngine - INFO - Generating response for: 'اهلا'
2025-06-08 01:25:54,884 - AIEngine - INFO - Attempting LLM response generation...
2025-06-08 01:25:54,885 - AIEngine - INFO - Attempting LLM response generation...
2025-06-08 01:25:54,886 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-08 01:25:54,886 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-08 01:25:54,886 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-08 01:25:54,886 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-08 01:25:54,887 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-08 01:25:54,887 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-08 01:25:54,888 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-08 01:25:54,888 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-08 01:25:54,985 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-08 01:25:54,985 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'اهلا' -> 'I'm processing that......'
2025-06-08 01:25:55,013 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-08 01:25:55,013 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'I'm processing that......'
2025-06-08 01:25:55,013 - UltraProfessionalInterface - INFO - 📝 Direct display for short response
2025-06-08 01:25:55,014 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-08 01:25:55,014 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:25:55] 🤖 Gideon: I'm proce...
2025-06-08 01:25:55,021 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:25:55,022 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:25:55,023 - TTSEngine - ERROR - Error during speech: run loop already started
2025-06-08 01:26:05,943 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-08 01:26:06,099 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-08 01:26:06,180 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-08 01:26:06,533 - STTEngine - INFO - Stopped continuous listening
2025-06-08 01:26:54,544 - AIEngine - INFO - LLM response generated successfully: مرحباً، سعدني رؤيتك! فيم يمكنني مساعدتك اليوم؟...
2025-06-08 01:26:54,547 - AIEngine - INFO - LLM generated successful response: مرحباً، سعدني رؤيتك! فيم يمكنني مساعدتك اليوم؟...
2025-06-08 01:26:54,564 - AIEngine - INFO - Final response generated: مرحباً، سعدني رؤيتك! فيم يمكنني مساعدتك اليوم؟ I'm...
2025-06-08 01:31:43,702 - EnterpriseMain - INFO - 🤖 Starting Gideon AI Assistant - Enterprise Edition
2025-06-08 01:31:43,704 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-08 01:31:43,710 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-08 01:31:43,710 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-08 01:31:43,710 - MemorySystem - INFO - Memory system initialized successfully
2025-06-08 01:31:43,712 - ModelManager - INFO - No existing models config found
2025-06-08 01:31:43,713 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-08 01:31:43,714 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-08 01:31:43,714 - AIEngine - INFO - Initializing LLM backends...
2025-06-08 01:31:43,715 - AIEngine - INFO - ✅ Ollama backend available
2025-06-08 01:31:43,715 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-08 01:31:43,715 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-08 01:31:43,716 - AIEngine - INFO - ✅ Transformers backend available
2025-06-08 01:31:43,716 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-08 01:31:43,716 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-08 01:31:43,993 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-08 01:31:43,995 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-08 01:31:43,995 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-08 01:31:43,995 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-08 01:32:07,942 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-08 01:32:07,944 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-08 01:32:07,944 - AIEngine - INFO - AI Engine initialized successfully
2025-06-08 01:32:08,086 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-08 01:32:08,143 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-08 01:32:08,278 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-08 01:32:08,278 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-08 01:32:11,289 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-08 01:32:11,305 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-08 01:32:11,305 - STTEngine - INFO -    Energy threshold: 150
2025-06-08 01:32:11,305 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-08 01:32:11,306 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-08 01:32:11,306 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-08 01:32:11,306 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-08 01:32:11,306 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-08 01:32:11,306 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-08 01:32:11,306 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-08 01:32:11,532 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-08 01:32:11,533 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-08 01:32:11,533 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-08 01:32:14,794 - STTEngine - INFO - Testing microphone... Say something!
2025-06-08 01:32:17,821 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-08 01:32:22,013 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-08 01:32:22,013 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-08 01:32:22,013 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-08 01:32:22,913 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-08 01:32:22,913 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-08 01:32:22,952 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-08 01:32:22,958 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition - READY!...
2025-06-08 01:32:22,959 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:22] ⚙️ System: 🚀 Gideon...
2025-06-08 01:32:23,165 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:23,165 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:23,165 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional AI assistant with Flash-inspi...
2025-06-08 01:32:23,165 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:23] ⚙️ System: 💼 Ultra-...
2025-06-08 01:32:23,169 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:23,169 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:23,169 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> � Bilingual support active: Arabic (primary) / Eng...
2025-06-08 01:32:23,170 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:23] ⚙️ System: � Biling...
2025-06-08 01:32:23,172 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:23,173 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:23,173 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎤 Voice interaction ready - Say 'Gideon' + your qu...
2025-06-08 01:32:23,173 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:23] ⚙️ System: 🎤 Voice ...
2025-06-08 01:32:23,176 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:23,176 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:23,176 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ⚡ Ultra-low latency mode active for instant respon...
2025-06-08 01:32:23,177 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:23] ⚙️ System: ⚡ Ultra-...
2025-06-08 01:32:23,177 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:23,177 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:23,178 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 AI Model loaded: dolphin-llama3:70b...
2025-06-08 01:32:23,178 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:23] ⚙️ System: 🧠 AI Mod...
2025-06-08 01:32:23,180 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:23,180 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:23,180 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready - Multi-backend s...
2025-06-08 01:32:23,180 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:23] ⚙️ System: 💡 Advanc...
2025-06-08 01:32:23,183 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:23,184 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:23,184 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🔄 Drag & drop new models to expand AI capabilities...
2025-06-08 01:32:23,184 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:23] ⚙️ System: 🔄 Drag &...
2025-06-08 01:32:23,185 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:23,185 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:23,185 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-08 01:32:23,185 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:23] ⚙️ System: 🚀 Gideon...
2025-06-08 01:32:23,186 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:23,186 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:23,186 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-08 01:32:23,186 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:23] ⚙️ System: 💼 Ultra-...
2025-06-08 01:32:23,187 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:23,187 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:23,187 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-08 01:32:23,187 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:23] ⚙️ System: 🎯 Real-t...
2025-06-08 01:32:23,188 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:23,188 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:23,188 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-08 01:32:23,188 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:23] ⚙️ System: 🧠 Enterp...
2025-06-08 01:32:23,188 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:23,188 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:23,189 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-08 01:32:23,189 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:23] ⚙️ System: 💡 Advanc...
2025-06-08 01:32:23,189 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:23,189 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:23,189 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-08 01:32:23,190 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:23] ⚙️ System: 💬 Say 'G...
2025-06-08 01:32:23,190 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:23,190 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:25,194 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-08 01:32:25,194 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:25] 🤖 Gideon: Hello! I'...
2025-06-08 01:32:25,195 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:25,195 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:33,202 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> hi...
2025-06-08 01:32:33,202 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:33] 👤 You: hi
...
2025-06-08 01:32:33,217 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:33,218 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:33,230 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:32:33,230 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'hi' -> 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:32:33,248 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'Hi there! I'm Gideon, your AI assistant. What can ...'
2025-06-08 01:32:33,249 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'Hi there! I'm Gideon, your AI ...'
2025-06-08 01:32:33,249 - UltraProfessionalInterface - INFO - 📝 Using streaming effect for long response
2025-06-08 01:32:33,249 - UltraProfessionalInterface - INFO - 🎬 Starting streaming effect for response: 'Hi there! I'm Gideon, your AI ...'
2025-06-08 01:32:33,472 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hi there! I'm Gideon, your AI assistant. What can ...
2025-06-08 01:32:33,473 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:33] 🤖 Gideon: Hi there!...
2025-06-08 01:32:33,479 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:33,479 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:33,479 - UltraProfessionalInterface - INFO - ✅ Streaming effect completed
2025-06-08 01:32:37,208 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> هلا...
2025-06-08 01:32:37,213 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫ :You 👤 [01:32:37]
...
2025-06-08 01:32:37,221 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:37,221 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:37,223 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-08 01:32:37,224 - AIEngine - INFO - Attempting LLM response generation...
2025-06-08 01:32:37,224 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-08 01:32:37,225 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-08 01:32:37,225 - AIEngine - INFO - Attempting LLM response generation...
2025-06-08 01:32:37,225 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-08 01:32:37,226 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-08 01:32:37,226 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-08 01:32:37,226 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-08 01:32:37,226 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-08 01:32:37,226 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-08 01:32:37,238 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-08 01:32:37,338 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-08 01:32:37,338 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'I'm processing that......'
2025-06-08 01:32:37,369 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-08 01:32:37,369 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'I'm processing that......'
2025-06-08 01:32:37,369 - UltraProfessionalInterface - INFO - 📝 Direct display for short response
2025-06-08 01:32:37,369 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-08 01:32:37,369 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [01:32:37] 🤖 Gideon: I'm proce...
2025-06-08 01:32:37,377 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-08 01:32:37,377 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-08 01:32:37,378 - TTSEngine - ERROR - Error during speech: run loop already started
2025-06-08 01:32:42,481 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-08 01:32:43,202 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-08 01:32:43,283 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-08 01:32:43,471 - STTEngine - INFO - Stopped continuous listening
2025-06-08 01:33:03,861 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-08 01:33:03,862 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-08 01:33:03,876 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
