#!/usr/bin/env python3
"""
Performance Optimization System for Gideon AI
Advanced caching, streaming, and pipeline optimization
"""

import time
import threading
import queue
import hashlib
import pickle
import json
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from pathlib import Path
import asyncio
import concurrent.futures

# Import ultra-low latency system
try:
    from src.optimization.ultra_low_latency import ultra_low_latency_manager
    ULTRA_LOW_LATENCY_AVAILABLE = True
except ImportError:
    ULTRA_LOW_LATENCY_AVAILABLE = False

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    response: str
    timestamp: float
    access_count: int
    response_time: float

class ResponseCache:
    """Intelligent response caching system"""
    
    def __init__(self, max_size: int = 1000, ttl: float = 3600):
        self.cache: Dict[str, CacheEntry] = {}
        self.max_size = max_size
        self.ttl = ttl  # Time to live in seconds
        self.lock = threading.RLock()
        
        # Cache statistics
        self.hits = 0
        self.misses = 0
        
        # Start cleanup thread
        self._start_cleanup_thread()
    
    def _generate_key(self, text: str, context: Dict = None) -> str:
        """Generate cache key from input"""
        # Normalize text
        normalized = text.lower().strip()
        
        # Include context if provided
        if context:
            context_str = json.dumps(context, sort_keys=True)
            normalized += f"|{context_str}"
        
        # Generate hash
        return hashlib.md5(normalized.encode()).hexdigest()
    
    def get(self, text: str, context: Dict = None) -> Optional[str]:
        """Get cached response"""
        key = self._generate_key(text, context)
        
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                
                # Check if expired
                if time.time() - entry.timestamp > self.ttl:
                    del self.cache[key]
                    self.misses += 1
                    return None
                
                # Update access count
                entry.access_count += 1
                self.hits += 1
                return entry.response
            
            self.misses += 1
            return None
    
    def put(self, text: str, response: str, response_time: float, context: Dict = None):
        """Cache response"""
        key = self._generate_key(text, context)
        
        with self.lock:
            # Remove oldest entries if cache is full
            if len(self.cache) >= self.max_size:
                self._evict_oldest()
            
            # Add new entry
            self.cache[key] = CacheEntry(
                response=response,
                timestamp=time.time(),
                access_count=1,
                response_time=response_time
            )
    
    def _evict_oldest(self):
        """Evict least recently used entries"""
        if not self.cache:
            return
        
        # Sort by access count and timestamp
        sorted_entries = sorted(
            self.cache.items(),
            key=lambda x: (x[1].access_count, x[1].timestamp)
        )
        
        # Remove bottom 10%
        remove_count = max(1, len(sorted_entries) // 10)
        for i in range(remove_count):
            key = sorted_entries[i][0]
            del self.cache[key]
    
    def _start_cleanup_thread(self):
        """Start background cleanup thread"""
        def cleanup_loop():
            while True:
                time.sleep(300)  # Clean every 5 minutes
                self._cleanup_expired()
        
        cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True)
        cleanup_thread.start()
    
    def _cleanup_expired(self):
        """Remove expired entries"""
        current_time = time.time()
        
        with self.lock:
            expired_keys = [
                key for key, entry in self.cache.items()
                if current_time - entry.timestamp > self.ttl
            ]
            
            for key in expired_keys:
                del self.cache[key]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self.hits + self.misses
        hit_rate = (self.hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'hits': self.hits,
            'misses': self.misses,
            'hit_rate': hit_rate,
            'cache_size': len(self.cache),
            'max_size': self.max_size
        }


class ResponseStreamer:
    """Stream responses for faster perceived response times"""
    
    def __init__(self):
        self.streaming_active = False
        self.stream_queue = queue.Queue()
        self.callbacks: List[Callable] = []
    
    def add_callback(self, callback: Callable):
        """Add callback for streamed responses"""
        self.callbacks.append(callback)
    
    def start_stream(self, response: str, chunk_size: int = 10):
        """Start streaming response in chunks"""
        self.streaming_active = True
        
        def stream_worker():
            words = response.split()
            current_chunk = []
            
            for word in words:
                if not self.streaming_active:
                    break
                
                current_chunk.append(word)
                
                if len(current_chunk) >= chunk_size:
                    chunk_text = ' '.join(current_chunk)
                    self._notify_callbacks(chunk_text, False)
                    current_chunk = []
                    time.sleep(0.1)  # Small delay for natural flow
            
            # Send remaining words
            if current_chunk and self.streaming_active:
                chunk_text = ' '.join(current_chunk)
                self._notify_callbacks(chunk_text, True)
        
        threading.Thread(target=stream_worker, daemon=True).start()
    
    def stop_stream(self):
        """Stop streaming"""
        self.streaming_active = False
    
    def _notify_callbacks(self, chunk: str, is_final: bool):
        """Notify all callbacks with chunk"""
        for callback in self.callbacks:
            try:
                callback(chunk, is_final)
            except Exception as e:
                print(f"Stream callback error: {e}")


class PipelineOptimizer:
    """Optimize the voice-to-AI-to-voice pipeline"""
    
    def __init__(self):
        self.pipeline_stats = {
            'voice_to_text': [],
            'ai_processing': [],
            'text_to_speech': [],
            'total_pipeline': []
        }
        
        # Thread pools for parallel processing
        self.ai_executor = concurrent.futures.ThreadPoolExecutor(max_workers=2)
        self.tts_executor = concurrent.futures.ThreadPoolExecutor(max_workers=2)
        
        # Pre-warm components
        self._preload_components()
    
    def _preload_components(self):
        """Pre-load and warm up components"""
        # This would pre-load AI models, TTS engines, etc.
        pass
    
    def time_operation(self, operation_name: str):
        """Context manager for timing operations"""
        return OperationTimer(self, operation_name)
    
    def record_timing(self, operation: str, duration: float):
        """Record operation timing"""
        if operation in self.pipeline_stats:
            self.pipeline_stats[operation].append(duration)
            
            # Keep only last 100 measurements
            if len(self.pipeline_stats[operation]) > 100:
                self.pipeline_stats[operation] = self.pipeline_stats[operation][-100:]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        stats = {}
        
        for operation, timings in self.pipeline_stats.items():
            if timings:
                stats[operation] = {
                    'avg_time': sum(timings) / len(timings),
                    'min_time': min(timings),
                    'max_time': max(timings),
                    'count': len(timings)
                }
            else:
                stats[operation] = {
                    'avg_time': 0,
                    'min_time': 0,
                    'max_time': 0,
                    'count': 0
                }
        
        return stats
    
    def optimize_ai_processing(self, text: str, ai_engine) -> str:
        """Optimize AI processing with parallel execution and robust error handling"""
        # Try direct processing first (faster for most cases)
        try:
            # For simple queries, try direct processing without threading overhead
            if len(text.strip()) < 100:  # Short queries
                response = ai_engine.generate_response(text)
                if response and response.strip():
                    return response
        except Exception as e:
            # If direct processing fails, continue to threaded approach
            pass

        # Submit to thread pool for complex processing
        future = self.ai_executor.submit(ai_engine.generate_response, text)

        # Increased timeout and better error handling
        try:
            return future.result(timeout=120)  # 2 minute timeout (much more generous)
        except concurrent.futures.TimeoutError:
            # Cancel the future to free resources
            future.cancel()
            # Return a more helpful message
            return "I need a moment to think about that. Let me try a different approach."
        except Exception as e:
            # Handle other errors gracefully
            return f"I encountered an issue processing that. Could you rephrase your question?"
    
    def optimize_tts_processing(self, text: str, tts_engine) -> bool:
        """Optimize TTS processing"""
        # Submit to thread pool
        future = self.tts_executor.submit(tts_engine.speak, text)
        
        try:
            return future.result(timeout=15)  # 15 second timeout
        except concurrent.futures.TimeoutError:
            return False


class OperationTimer:
    """Context manager for timing operations"""
    
    def __init__(self, optimizer: PipelineOptimizer, operation_name: str):
        self.optimizer = optimizer
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            self.optimizer.record_timing(self.operation_name, duration)


class PerformanceManager:
    """Main performance management system"""
    
    def __init__(self):
        self.cache = ResponseCache()
        self.streamer = ResponseStreamer()
        self.optimizer = PipelineOptimizer()
        
        # Performance monitoring
        self.monitoring_active = True
        self._start_monitoring()
    
    def process_request_optimized(self, text: str, ai_engine, context: Dict = None) -> str:
        """Process request with ultra-low latency optimization"""
        start_time = time.time()

        # PRIORITY 1: Ultra-low latency processing (0-100ms)
        if ULTRA_LOW_LATENCY_AVAILABLE:
            try:
                ultra_response = ultra_low_latency_manager.process_ultra_fast(text, ai_engine)
                if ultra_response:
                    response_time = (time.time() - start_time) * 1000
                    print(f"🚀 ULTRA-LOW LATENCY ({response_time:.2f}ms): {ultra_response[:50]}...")
                    return ultra_response
            except Exception as e:
                print(f"Ultra-low latency processing failed: {e}")

        # PRIORITY 2: Standard cache check
        cached_response = self.cache.get(text, context)
        if cached_response:
            response_time = (time.time() - start_time) * 1000
            print(f"💾 CACHED RESPONSE ({response_time:.2f}ms): {cached_response[:50]}...")
            return cached_response

        # PRIORITY 3: Direct AI processing (fastest for uncached)
        try:
            with self.optimizer.time_operation('ai_processing'):
                response = ai_engine.generate_response(text)

            if response and response.strip() and len(response.strip()) > 5:
                response_time = time.time() - start_time
                self.cache.put(text, response, response_time, context)
                print(f"🧠 AI RESPONSE ({response_time:.2f}s): {response[:50]}...")
                return response
        except Exception as e:
            print(f"Direct AI processing failed: {e}")

        # PRIORITY 4: Optimized processing with threading
        try:
            with self.optimizer.time_operation('ai_processing_threaded'):
                response = self.optimizer.optimize_ai_processing(text, ai_engine)

            if response and response.strip() and len(response.strip()) > 5:
                response_time = time.time() - start_time
                self.cache.put(text, response, response_time, context)
                print(f"⚡ THREADED RESPONSE ({response_time:.2f}s): {response[:50]}...")
                return response
        except Exception as e:
            print(f"Threaded AI processing failed: {e}")

        # PRIORITY 5: Instant fallback responses
        instant_fallbacks = {
            "hello": "Hello! I'm Gideon, your AI assistant. How can I help you today?",
            "hi": "Hi there! I'm Gideon. What can I do for you?",
            "hey": "Hey! I'm Gideon. How can I assist you?",
            "how are you": "I'm doing well, thank you for asking! I'm Gideon. How are you?",
            "what is your name": "My name is Gideon. I'm your AI assistant.",
            "who are you": "I'm Gideon, your AI assistant. I'm here to help you with whatever you need.",
            "what are you": "I'm Gideon, an advanced AI assistant designed to help you.",
            "help": "I'm Gideon, your AI assistant. I can help you with questions, conversations, and various tasks. What would you like to know?",
            "thank you": "You're very welcome! I'm Gideon, always happy to help.",
            "thanks": "You're welcome! I'm Gideon, glad I could assist you."
        }

        text_lower = text.lower().strip()
        for key, fallback in instant_fallbacks.items():
            if key in text_lower:
                response_time = (time.time() - start_time) * 1000
                print(f"⚡ INSTANT FALLBACK ({response_time:.2f}ms): {fallback[:50]}...")
                return fallback

        # Final ultra-fast fallback
        final_response = "I'm Gideon, your AI assistant. I'm here to help you. Could you please rephrase your question?"
        response_time = (time.time() - start_time) * 1000
        print(f"⚡ FINAL FALLBACK ({response_time:.2f}ms): {final_response[:50]}...")
        return final_response
    
    def stream_response(self, response: str, callback: Callable):
        """Stream response for faster perceived response"""
        self.streamer.add_callback(callback)
        self.streamer.start_stream(response)
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics"""
        return {
            'cache_stats': self.cache.get_stats(),
            'pipeline_stats': self.optimizer.get_performance_stats(),
            'timestamp': time.time()
        }
    
    def _start_monitoring(self):
        """Start performance monitoring"""
        def monitor_loop():
            while self.monitoring_active:
                time.sleep(60)  # Monitor every minute
                stats = self.get_comprehensive_stats()
                # Could log stats, trigger alerts, etc.
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
    
    def shutdown(self):
        """Shutdown performance manager"""
        self.monitoring_active = False
        self.optimizer.ai_executor.shutdown(wait=False)
        self.optimizer.tts_executor.shutdown(wait=False)
