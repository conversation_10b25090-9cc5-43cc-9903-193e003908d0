#!/usr/bin/env python3
"""
Enterprise Error Handling and Recovery System for Gideon AI Assistant
Comprehensive error handling, graceful degradation, and automatic recovery
"""

import sys
import traceback
import threading
import time
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from src.utils.logger import GideonLogger

class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    FATAL = "fatal"

class ErrorCategory(Enum):
    """Error categories for better classification"""
    SYSTEM = "system"
    AI_ENGINE = "ai_engine"
    SPEECH = "speech"
    UI = "ui"
    NETWORK = "network"
    MEMORY = "memory"
    CONFIGURATION = "configuration"
    AUTHENTICATION = "authentication"
    DATA = "data"

class EnterpriseErrorHandler:
    """Enterprise-grade error handling and recovery system"""
    
    def __init__(self):
        self.logger = GideonLogger("EnterpriseErrorHandler")
        
        # Error tracking
        self.error_history = []
        self.error_counts = {}
        self.recovery_attempts = {}
        
        # Recovery strategies
        self.recovery_strategies = {}
        self.fallback_handlers = {}
        
        # Error reporting
        self.error_reports_dir = Path("data/error_reports")
        self.error_reports_dir.mkdir(parents=True, exist_ok=True)
        
        # System health monitoring
        self.health_checks = {}
        self.health_status = {}
        
        # Recovery settings
        self.max_recovery_attempts = 3
        self.recovery_cooldown = 30  # seconds
        self.auto_recovery_enabled = True
        
        # Error callbacks
        self.error_callbacks = []
        self.recovery_callbacks = []
        
        # Initialize default recovery strategies
        self._initialize_recovery_strategies()
        
        # Start health monitoring
        self._start_health_monitoring()
    
    def _initialize_recovery_strategies(self):
        """Initialize default recovery strategies for different error types"""
        
        # AI Engine recovery
        self.recovery_strategies[ErrorCategory.AI_ENGINE] = [
            self._restart_ai_engine,
            self._switch_to_fallback_model,
            self._use_rule_based_responses
        ]
        
        # Speech engine recovery
        self.recovery_strategies[ErrorCategory.SPEECH] = [
            self._restart_speech_engines,
            self._switch_to_text_only_mode,
            self._reinitialize_audio_devices
        ]
        
        # UI recovery
        self.recovery_strategies[ErrorCategory.UI] = [
            self._refresh_ui_components,
            self._restart_interface,
            self._switch_to_minimal_ui
        ]
        
        # Network recovery
        self.recovery_strategies[ErrorCategory.NETWORK] = [
            self._retry_network_connection,
            self._switch_to_offline_mode,
            self._use_cached_responses
        ]
        
        # Memory recovery
        self.recovery_strategies[ErrorCategory.MEMORY] = [
            self._clear_memory_cache,
            self._restart_memory_system,
            self._reduce_memory_usage
        ]
    
    def handle_error(self, error: Exception, category: ErrorCategory, 
                    severity: ErrorSeverity, context: Dict[str, Any] = None,
                    auto_recover: bool = True) -> bool:
        """Handle an error with comprehensive logging and recovery"""
        
        error_id = self._generate_error_id()
        timestamp = datetime.now()
        
        # Create error record
        error_record = {
            'id': error_id,
            'timestamp': timestamp.isoformat(),
            'category': category.value,
            'severity': severity.value,
            'error_type': type(error).__name__,
            'error_message': str(error),
            'traceback': traceback.format_exc(),
            'context': context or {},
            'recovery_attempted': False,
            'recovery_successful': False
        }
        
        # Log the error
        self._log_error(error_record)
        
        # Add to error history
        self.error_history.append(error_record)
        
        # Update error counts
        error_key = f"{category.value}:{type(error).__name__}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # Trigger error callbacks
        self._trigger_error_callbacks(error_record)
        
        # Attempt recovery if enabled and appropriate
        recovery_successful = False
        if auto_recover and self.auto_recovery_enabled and severity != ErrorSeverity.FATAL:
            recovery_successful = self._attempt_recovery(error_record)
        
        # Generate error report for critical/fatal errors
        if severity in [ErrorSeverity.CRITICAL, ErrorSeverity.FATAL]:
            self._generate_error_report(error_record)
        
        return recovery_successful
    
    def _log_error(self, error_record: Dict[str, Any]):
        """Log error with appropriate severity level"""
        severity = error_record['severity']
        message = f"[{error_record['category'].upper()}] {error_record['error_message']}"
        
        if severity == ErrorSeverity.LOW.value:
            self.logger.debug(message)
        elif severity == ErrorSeverity.MEDIUM.value:
            self.logger.info(message)
        elif severity == ErrorSeverity.HIGH.value:
            self.logger.warning(message)
        elif severity == ErrorSeverity.CRITICAL.value:
            self.logger.error(message)
        elif severity == ErrorSeverity.FATAL.value:
            self.logger.critical(message)
    
    def _attempt_recovery(self, error_record: Dict[str, Any]) -> bool:
        """Attempt to recover from an error using registered strategies"""
        category = ErrorCategory(error_record['category'])
        error_key = f"{category.value}:{error_record['error_type']}"
        
        # Check if we've exceeded recovery attempts
        if error_key in self.recovery_attempts:
            attempts, last_attempt = self.recovery_attempts[error_key]
            if attempts >= self.max_recovery_attempts:
                if time.time() - last_attempt < self.recovery_cooldown:
                    self.logger.warning(f"Recovery cooldown active for {error_key}")
                    return False
                else:
                    # Reset attempts after cooldown
                    self.recovery_attempts[error_key] = (0, time.time())
        
        # Get recovery strategies for this category
        strategies = self.recovery_strategies.get(category, [])
        
        for i, strategy in enumerate(strategies):
            try:
                self.logger.info(f"Attempting recovery strategy {i+1}/{len(strategies)} for {error_key}")
                
                # Update recovery attempt count
                attempts, _ = self.recovery_attempts.get(error_key, (0, 0))
                self.recovery_attempts[error_key] = (attempts + 1, time.time())
                
                # Execute recovery strategy
                success = strategy(error_record)
                
                if success:
                    self.logger.info(f"Recovery successful for {error_key}")
                    error_record['recovery_attempted'] = True
                    error_record['recovery_successful'] = True
                    
                    # Trigger recovery callbacks
                    self._trigger_recovery_callbacks(error_record)
                    
                    # Reset recovery attempts on success
                    if error_key in self.recovery_attempts:
                        del self.recovery_attempts[error_key]
                    
                    return True
                
            except Exception as recovery_error:
                self.logger.error(f"Recovery strategy failed: {recovery_error}")
        
        error_record['recovery_attempted'] = True
        error_record['recovery_successful'] = False
        self.logger.error(f"All recovery strategies failed for {error_key}")
        return False
    
    def _generate_error_id(self) -> str:
        """Generate unique error ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"ERR_{timestamp}_{len(self.error_history):04d}"
    
    def _generate_error_report(self, error_record: Dict[str, Any]):
        """Generate comprehensive error report"""
        try:
            report_file = self.error_reports_dir / f"{error_record['id']}_report.json"
            
            # Collect system information
            system_info = {
                'python_version': sys.version,
                'platform': sys.platform,
                'error_history_count': len(self.error_history),
                'recent_errors': self.error_history[-10:] if len(self.error_history) > 10 else self.error_history,
                'error_counts': self.error_counts,
                'health_status': self.health_status
            }
            
            # Create comprehensive report
            report = {
                'error_record': error_record,
                'system_info': system_info,
                'generated_at': datetime.now().isoformat()
            }
            
            # Save report
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, default=str)
            
            self.logger.info(f"Error report generated: {report_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to generate error report: {e}")
    
    def _trigger_error_callbacks(self, error_record: Dict[str, Any]):
        """Trigger registered error callbacks"""
        for callback in self.error_callbacks:
            try:
                callback(error_record)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")
    
    def _trigger_recovery_callbacks(self, error_record: Dict[str, Any]):
        """Trigger registered recovery callbacks"""
        for callback in self.recovery_callbacks:
            try:
                callback(error_record)
            except Exception as e:
                self.logger.error(f"Error in recovery callback: {e}")
    
    def _start_health_monitoring(self):
        """Start background health monitoring"""
        def health_monitor():
            while True:
                try:
                    self._perform_health_checks()
                    time.sleep(60)  # Check every minute
                except Exception as e:
                    self.logger.error(f"Error in health monitoring: {e}")
                    time.sleep(60)
        
        health_thread = threading.Thread(target=health_monitor, daemon=True)
        health_thread.start()
    
    def _perform_health_checks(self):
        """Perform system health checks"""
        for component, check_func in self.health_checks.items():
            try:
                status = check_func()
                self.health_status[component] = {
                    'status': 'healthy' if status else 'unhealthy',
                    'last_check': datetime.now().isoformat()
                }
            except Exception as e:
                self.health_status[component] = {
                    'status': 'error',
                    'error': str(e),
                    'last_check': datetime.now().isoformat()
                }
    
    # Recovery strategy implementations
    def _restart_ai_engine(self, error_record: Dict[str, Any]) -> bool:
        """Restart AI engine"""
        try:
            # Implementation would restart the AI engine
            self.logger.info("Restarting AI engine...")
            return True
        except Exception:
            return False
    
    def _switch_to_fallback_model(self, error_record: Dict[str, Any]) -> bool:
        """Switch to fallback AI model"""
        try:
            self.logger.info("Switching to fallback AI model...")
            return True
        except Exception:
            return False
    
    def _use_rule_based_responses(self, error_record: Dict[str, Any]) -> bool:
        """Switch to rule-based responses"""
        try:
            self.logger.info("Switching to rule-based responses...")
            return True
        except Exception:
            return False
    
    def _restart_speech_engines(self, error_record: Dict[str, Any]) -> bool:
        """Restart speech engines"""
        try:
            self.logger.info("Restarting speech engines...")
            return True
        except Exception:
            return False
    
    def _switch_to_text_only_mode(self, error_record: Dict[str, Any]) -> bool:
        """Switch to text-only mode"""
        try:
            self.logger.info("Switching to text-only mode...")
            return True
        except Exception:
            return False
    
    def _reinitialize_audio_devices(self, error_record: Dict[str, Any]) -> bool:
        """Reinitialize audio devices"""
        try:
            self.logger.info("Reinitializing audio devices...")
            return True
        except Exception:
            return False
    
    def _refresh_ui_components(self, error_record: Dict[str, Any]) -> bool:
        """Refresh UI components"""
        try:
            self.logger.info("Refreshing UI components...")
            return True
        except Exception:
            return False
    
    def _restart_interface(self, error_record: Dict[str, Any]) -> bool:
        """Restart interface"""
        try:
            self.logger.info("Restarting interface...")
            return True
        except Exception:
            return False
    
    def _switch_to_minimal_ui(self, error_record: Dict[str, Any]) -> bool:
        """Switch to minimal UI"""
        try:
            self.logger.info("Switching to minimal UI...")
            return True
        except Exception:
            return False
    
    def _retry_network_connection(self, error_record: Dict[str, Any]) -> bool:
        """Retry network connection"""
        try:
            self.logger.info("Retrying network connection...")
            return True
        except Exception:
            return False
    
    def _switch_to_offline_mode(self, error_record: Dict[str, Any]) -> bool:
        """Switch to offline mode"""
        try:
            self.logger.info("Switching to offline mode...")
            return True
        except Exception:
            return False
    
    def _use_cached_responses(self, error_record: Dict[str, Any]) -> bool:
        """Use cached responses"""
        try:
            self.logger.info("Using cached responses...")
            return True
        except Exception:
            return False
    
    def _clear_memory_cache(self, error_record: Dict[str, Any]) -> bool:
        """Clear memory cache"""
        try:
            self.logger.info("Clearing memory cache...")
            return True
        except Exception:
            return False
    
    def _restart_memory_system(self, error_record: Dict[str, Any]) -> bool:
        """Restart memory system"""
        try:
            self.logger.info("Restarting memory system...")
            return True
        except Exception:
            return False
    
    def _reduce_memory_usage(self, error_record: Dict[str, Any]) -> bool:
        """Reduce memory usage"""
        try:
            self.logger.info("Reducing memory usage...")
            return True
        except Exception:
            return False
    
    def register_error_callback(self, callback: Callable):
        """Register error callback"""
        self.error_callbacks.append(callback)
    
    def register_recovery_callback(self, callback: Callable):
        """Register recovery callback"""
        self.recovery_callbacks.append(callback)
    
    def register_health_check(self, component: str, check_func: Callable):
        """Register health check for a component"""
        self.health_checks[component] = check_func
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics"""
        return {
            'total_errors': len(self.error_history),
            'error_counts': self.error_counts,
            'recovery_attempts': self.recovery_attempts,
            'health_status': self.health_status
        }

# Global instance
enterprise_error_handler = EnterpriseErrorHandler()
