#!/usr/bin/env python3
"""
Gideon AI Assistant - Main Entry Point

A bilingual AI assistant with voice interaction capabilities.
Supports Arabic (primary) and English (secondary) languages.

Features:
- Bilingual speech recognition and text-to-speech
- Modern GUI interface
- AI model integration (Ollama, local models)
- Voice wake word detection
- Real-time language switching
"""

import sys
import traceback
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

# Initialize application
print("Initializing Gideon AI Assistant...")
print("Bilingual support: Arabic (primary) / English (secondary)")

def check_dependencies():
    """Check for required dependencies"""
    print("Checking Dependencies...")
    print("=" * 40)
    
    missing_deps = []
    optional_deps = []
    
    # Check for CustomTkinter (required for modern UI)
    try:
        import customtkinter
        print("OK CustomTkinter available - Modern UI ready")
        modern_ui = True
    except ImportError:
        print("ERROR CustomTkinter REQUIRED for modern interface")
        print("Install with: pip install customtkinter")
        missing_deps.append('customtkinter')
        modern_ui = False
    
    # Check for core dependencies
    core_deps = [
        ('speech_recognition', 'speech_recognition'),
        ('pyttsx3', 'pyttsx3'),
        ('pyaudio', 'pyaudio'),
        ('Pillow', 'PIL'),
    ]
    
    for dep_name, import_name in core_deps:
        try:
            __import__(import_name)
            print(f"✅ {dep_name} available")
        except ImportError:
            missing_deps.append(dep_name)
            print(f"❌ {dep_name} missing")
    
    # Check for AI dependencies (optional but recommended)
    ai_deps = [
        ('ollama', 'ollama'),
        ('llama-cpp-python', 'llama_cpp'),
        ('transformers', 'transformers'),
        ('torch', 'torch'),
    ]
    
    ai_available = 0
    for dep_name, import_name in ai_deps:
        try:
            __import__(import_name)
            print(f"✅ {dep_name} available")
            ai_available += 1
        except ImportError:
            optional_deps.append(dep_name)
            print(f"⚠️ {dep_name} not available (optional)")
    
    # Check for performance monitoring dependencies
    perf_deps = [
        ('psutil', 'psutil'),
        ('numpy', 'numpy'),
    ]
    
    perf_available = 0
    for dep_name, import_name in perf_deps:
        try:
            __import__(import_name)
            print(f"OK {dep_name} available")
            perf_available += 1
        except ImportError:
            optional_deps.append(dep_name)
            print(f"WARNING {dep_name} not available (performance monitoring)")
    
    print(f"\nDependency Summary:")
    print(f"   AI Frameworks: {ai_available}/{len(ai_deps)} available")
    print(f"   Performance Tools: {perf_available}/{len(perf_deps)} available")
    
    if missing_deps:
        print(f"\nMissing REQUIRED dependencies: {', '.join(missing_deps)}")
        print("Install with: pip install " + " ".join(missing_deps))
        return False
    
    if optional_deps:
        print(f"\nOptional dependencies missing: {', '.join(optional_deps)}")
        print("For full features install: pip install " + " ".join(optional_deps))
    
    return True

def display_banner():
    """Display application banner"""
    banner = """
================================================================================
                                                                              
    GIDEON AI ASSISTANT                                                       
                                                                              
    Bilingual AI Assistant with Voice Interaction                            
                                                                              
    FEATURES:                                                                 
    • Bilingual Speech Recognition (Arabic/English)                          
    • Text-to-Speech with Female Voice                                       
    • Wake Word Detection ("Gideon")                                         
    • AI Model Integration (Ollama, Local Models)                            
    • Modern GUI Interface                                                   
    • Real-time Language Switching                                           
    • Chat History and Memory                                                
                                                                              
================================================================================
"""
    print(banner)

def main():
    """Main entry point for Gideon AI Assistant"""
    display_banner()

    # Check dependencies
    if not check_dependencies():
        print("\n❌ Cannot start due to missing required dependencies")
        print("🔧 Please install the required packages and try again")
        input("Press Enter to exit...")
        return 1

    print("\n🚀 Starting Gideon AI Assistant Enterprise Edition...")
    print("🎨 Initializing Full HD interface with enterprise features...")

    try:
        # Import core components
        from src.core.gideon_core import GideonCore
        from src.ui.ultra_professional_interface import UltraProfessionalInterface
        from src.ui.enterprise_splash import EnterpriseSplashScreen
        from src.utils.logger import GideonLogger

        # Setup logging
        logger = GideonLogger("GideonMain")
        logger.info("🤖 Starting Gideon AI Assistant Enterprise Edition")

        # Create data directories including enterprise assets
        print("📁 Creating enterprise directories...")
        directories = [
            "data/models", "data/memory", "data/logs", "data/cache",
            "data/recordings", "data/screenshots", "assets", "assets/icons",
            "assets/images", "assets/fonts"
        ]
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

        # Global variables for initialization
        gideon_core = None
        interface = None
        root = None

        def _setup_enterprise_window_properties(root, logger):
            """Setup enterprise window properties for Full HD"""
            try:
                # Set high-resolution icon if available
                icon_paths = [
                    current_dir / "assets" / "gideon_enterprise_icon_hd.ico",
                    current_dir / "assets" / "gideon_icon.ico"
                ]

                for icon_path in icon_paths:
                    if icon_path.exists():
                        root.iconbitmap(str(icon_path))
                        break

                # Set enterprise window attributes
                root.attributes('-topmost', False)  # Don't stay on top
                root.state('normal')  # Normal window state

                # Enable high-DPI awareness
                try:
                    import ctypes
                    ctypes.windll.shcore.SetProcessDpiAwareness(1)
                except:
                    pass

            except Exception as e:
                logger.warning(f"Could not set enterprise window properties: {e}")

        def _add_enterprise_welcome_messages(interface, gideon_core):
            """Add enterprise welcome messages to the interface"""
            print("Gideon AI Assistant Enterprise Edition ready!")
            print("\nENTERPRISE FEATURES:")
            print("  • Full HD (1920x1080) optimized interface")
            print("  • High-DPI scaling for 4K displays")
            print("  • Professional dark theme with glass morphism")
            print("  • Vector-based scalable icons")
            print("  • Enterprise-grade error handling")
            print("  • Comprehensive logging and monitoring")
            print("  • Professional splash screen and animations")

            print("\nACTIVE FEATURES:")
            print("  • Modern enterprise interface")
            print("  • AI model integration")
            print("  • Bilingual support (Arabic primary/English secondary)")
            print("  • Voice interaction with wake word detection")
            print("  • Model management")
            print("  • Chat history and memory")

            print("\nINTERACTION METHODS:")
            print("  • Voice: Say 'Gideon' + your question")
            print("  • Chat: Type in the interface")
            print("  • Languages: Arabic (primary) / English (secondary)")
            print("  • Models: Manage AI models")

            print("\nKEYBOARD SHORTCUTS:")
            print("  • Enter: Send message")
            print("  • Ctrl+N: Clear chat history")
            print("  • Ctrl+L: Switch language")
            print("  • F1: Show help")

            # Add comprehensive welcome messages to interface
            interface._add_message("System", "🚀 Gideon AI Assistant Enterprise Edition - READY!", interface.design_system['colors']['accent_success'])
            interface._add_message("System", "💎 Ultra-professional AI assistant with Full HD interface", interface.design_system['colors']['text_secondary'])
            interface._add_message("System", "🌐 Bilingual support active: Arabic (primary) / English (secondary)", interface.design_system['colors']['accent_primary'])
            interface._add_message("System", "🎤 Voice interaction ready - Say 'Gideon' + your question", interface.design_system['colors']['accent_cyan'])
            interface._add_message("System", "⚡ Ultra-low latency mode active for instant responses", interface.design_system['colors']['accent_purple'])
            interface._add_message("System", "🎯 Full HD (1920x1080) optimized with high-DPI scaling", interface.design_system['colors']['accent_secondary'])

            # Check AI status and display comprehensive information
            if gideon_core and hasattr(gideon_core, 'ai_engine'):
                if gideon_core.ai_engine.llm_enabled:
                    model_name = gideon_core.ai_engine.active_backend.current_model if gideon_core.ai_engine.active_backend else "Unknown"
                    interface._add_message("System", f"🤖 AI Model loaded: {model_name}", interface.design_system['colors']['accent_success'])
                    interface._add_message("System", "🧠 Advanced AI capabilities ready - Multi-backend support active", interface.design_system['colors']['text_secondary'])
                    interface._add_message("System", "📂 Drag & drop new models to expand AI capabilities", interface.design_system['colors']['accent_warning'])
                else:
                    interface._add_message("System", "⚠️ Using rule-based responses. Configure AI models for advanced capabilities.", interface.design_system['colors']['accent_warning'])
                    interface._add_message("System", "💡 Tip: Install Ollama or add model files for enhanced AI features", interface.design_system['colors']['text_secondary'])

            logger.info("Gideon AI Assistant Enterprise Edition ready")

        def initialize_application():
            """Initialize the application after splash screen"""
            nonlocal gideon_core, interface, root

            try:
                # Initialize Gideon core
                print("Initializing core systems...")
                gideon_core = GideonCore()

                # Initialize all systems
                print("Loading AI engine...")
                print("Configuring bilingual support...")
                gideon_core.initialize()

                # Create interface
                print("Creating enterprise interface...")
                interface = UltraProfessionalInterface(gideon_core)
                root = interface.create_window()

                # Set enterprise window properties for Full HD
                _setup_enterprise_window_properties(root, logger)

                print("✅ Enterprise Edition Ready!")
                print("🎯 Full HD (1920x1080) optimized interface")
                print("🚀 High-DPI scaling enabled")
                print("💎 Professional enterprise features active")

                # Add enterprise welcome messages
                _add_enterprise_welcome_messages(interface, gideon_core)

                # Start the main interface
                interface.run()

            except Exception as e:
                logger.error(f"Error during initialization: {e}")
                print(f"❌ Error during initialization: {e}")
                import traceback
                traceback.print_exc()

        # Show enterprise splash screen
        print("🎬 Launching enterprise splash screen...")
        splash = EnterpriseSplashScreen(on_complete=initialize_application)
        splash.show(total_duration=5.0)  # 5 second professional loading

        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
        return 0
        
    except Exception as e:
        # Log fatal error and traceback for diagnostics
        try:
            from src.utils.logger import GideonLogger
            logger = GideonLogger("GideonMain")
            logger.error(f"Fatal error: {e}")
            logger.error(traceback.format_exc())
        except Exception:
            pass  # If logger import fails, fallback to print
        print(f"\n❌ Fatal error: {e}")
        print("\n🔍 Full error details:")
        traceback.print_exc()
        
        # Try to show error in GUI
        try:
            import tkinter as tk
            from tkinter import messagebox
            
            root = tk.Tk()
            root.withdraw()  # Hide main window
            
            messagebox.showerror(
                "Gideon AI Enterprise - Fatal Error",
                f"Failed to start Gideon AI Assistant Enterprise Edition:\n\n{e}\n\nCheck console for details."
            )
            
        except Exception:
            pass
        
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
