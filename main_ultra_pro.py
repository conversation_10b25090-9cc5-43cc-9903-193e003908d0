#!/usr/bin/env python3
"""
Gideon AI Assistant - Optimized Main Entry Point

A bilingual AI assistant with voice interaction capabilities.
Optimized for minimal resource usage and fast startup.

Features:
- Bilingual speech recognition and text-to-speech (lazy loaded)
- Modern GUI interface (lightweight startup)
- AI model integration (on-demand loading)
- Voice wake word detection (optional)
- Real-time language switching
"""

import sys
import traceback
import gc
import threading
import time
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

# Resource monitoring
import psutil

# Initialize application with resource monitoring
print("🚀 Starting Gideon AI Assistant (Optimized)...")
print("💡 Lightweight startup mode enabled")

def check_system_resources():
    """Check system resources and adjust startup accordingly"""
    try:
        # Get system info
        cpu_percent = psutil.cpu_percent(interval=0.1)  # Quick check
        memory = psutil.virtual_memory()
        available_memory_gb = memory.available / (1024**3)
        
        print(f"📊 System Status:")
        print(f"   CPU Usage: {cpu_percent:.1f}%")
        print(f"   Available Memory: {available_memory_gb:.1f} GB")
        print(f"   Memory Usage: {memory.percent:.1f}%")
        
        # Determine startup mode based on resources
        if cpu_percent > 80 or memory.percent > 85:
            print("⚠️ High system load detected - Using minimal startup mode")
            return "minimal"
        elif available_memory_gb < 2:
            print("⚠️ Low memory detected - Using lightweight mode")
            return "lightweight"
        else:
            print("✅ System resources adequate - Using standard mode")
            return "standard"
            
    except Exception as e:
        print(f"⚠️ Resource check failed: {e} - Using safe mode")
        return "minimal"

def check_dependencies(startup_mode="standard"):
    """Check for required dependencies with optimized loading"""
    print("🔍 Checking Dependencies (Optimized)...")
    print("=" * 50)
    
    missing_deps = []
    
    # Check for CustomTkinter (required for modern UI)
    try:
        import customtkinter
        print("✅ CustomTkinter available - Modern UI ready")
    except ImportError:
        print("❌ CustomTkinter REQUIRED for modern interface")
        print("Install with: pip install customtkinter")
        missing_deps.append('customtkinter')
    
    # Core dependencies (always check)
    core_deps = [('Pillow', 'PIL')]
    
    # Speech dependencies (check based on startup mode)
    if startup_mode != "minimal":
        core_deps.extend([
            ('speech_recognition', 'speech_recognition'),
            ('pyttsx3', 'pyttsx3'),
        ])
    
    for dep_name, import_name in core_deps:
        try:
            __import__(import_name)
            print(f"✅ {dep_name} available")
        except ImportError:
            if startup_mode == "minimal" and dep_name in ['speech_recognition', 'pyttsx3']:
                print(f"⚠️ {dep_name} not available (will load on demand)")
            else:
                missing_deps.append(dep_name)
                print(f"❌ {dep_name} missing")
    
    if missing_deps:
        print(f"\n❌ Missing REQUIRED dependencies: {', '.join(missing_deps)}")
        print("Install with: pip install " + " ".join(missing_deps))
        return False
    
    print(f"\n📊 Dependency Summary: Mode: {startup_mode}")
    return True

def display_banner():
    """Display application banner"""
    banner = """
================================================================================
                                                                              
    GIDEON AI ASSISTANT (OPTIMIZED)                                          
                                                                              
    Bilingual AI Assistant with Voice Interaction                            
    Optimized for Fast Startup and Low Resource Usage                        
                                                                              
    FEATURES:                                                                 
    • Lazy Loading - Components load on demand                               
    • Resource Monitoring - Adapts to system load                            
    • Bilingual Speech Recognition (Arabic/English)                          
    • Modern GUI Interface                                                   
    • AI Model Integration (On-demand)                                       
                                                                              
================================================================================
"""
    print(banner)

class LazyLoader:
    """Lazy loader for resource-intensive components"""
    
    def __init__(self):
        self._gideon_core = None
        self._interface = None
        self._logger = None
        self._startup_mode = "standard"
        
    def set_startup_mode(self, mode):
        """Set startup mode for resource optimization"""
        self._startup_mode = mode
        
    def get_logger(self):
        """Get logger instance (lazy loaded)"""
        if self._logger is None:
            from src.utils.logger import GideonLogger
            self._logger = GideonLogger("GideonMain")
        return self._logger
        
    def get_gideon_core(self):
        """Get Gideon core instance (lazy loaded)"""
        if self._gideon_core is None:
            print("🧠 Loading AI core systems...")
            from src.core.gideon_core import GideonCore
            self._gideon_core = GideonCore()
            
            # Initialize based on startup mode
            if self._startup_mode == "minimal":
                print("⚡ Minimal initialization mode")
                # Skip heavy initialization
                self._gideon_core.initialize_minimal()
            elif self._startup_mode == "lightweight":
                print("🪶 Lightweight initialization mode")
                self._gideon_core.initialize_lightweight()
            else:
                print("🚀 Standard initialization mode")
                self._gideon_core.initialize()
                
        return self._gideon_core
        
    def get_interface(self):
        """Get interface instance (lazy loaded)"""
        if self._interface is None:
            print("🎨 Creating optimized interface...")
            from src.ui.ultra_professional_interface import UltraProfessionalInterface
            
            # Get core first
            core = self.get_gideon_core()
            
            # Create interface with optimization flags
            self._interface = UltraProfessionalInterface(
                gideon_core=core,
                startup_mode=self._startup_mode
            )
            
        return self._interface

def cleanup_memory():
    """Perform memory cleanup"""
    gc.collect()
    print("🧹 Memory cleanup completed")

def main():
    """Optimized main entry point for Gideon AI Assistant"""
    display_banner()
    
    # Check system resources first
    startup_mode = check_system_resources()
    
    # Check dependencies based on startup mode
    if not check_dependencies(startup_mode):
        print("\n❌ Cannot start due to missing required dependencies")
        print("🔧 Please install the required packages and try again")
        input("Press Enter to exit...")
        return 1

    print(f"\n🚀 Starting Gideon AI Assistant (Mode: {startup_mode})...")
    print("⚡ Optimized for fast startup and low resource usage")

    try:
        # Initialize lazy loader
        lazy_loader = LazyLoader()
        lazy_loader.set_startup_mode(startup_mode)
        
        # Setup logging (lightweight)
        logger = lazy_loader.get_logger()
        logger.info(f"🤖 Starting Gideon AI Assistant - Mode: {startup_mode}")

        # Create essential directories only
        print("📁 Creating essential directories...")
        essential_dirs = ["data/logs", "data/cache"]
        for directory in essential_dirs:
            Path(directory).mkdir(parents=True, exist_ok=True)
            
        # Memory cleanup
        cleanup_memory()

        def initialize_application_optimized():
            """Optimized application initialization with lazy loading"""
            try:
                # Initialize core with lazy loading
                print("🧠 Initializing core systems (optimized)...")
                gideon_core = lazy_loader.get_gideon_core()

                # Create interface with optimization
                print("🎨 Creating optimized interface...")
                interface = lazy_loader.get_interface()
                root = interface.create_window()

                print(f"✅ Gideon AI Assistant Ready! (Mode: {startup_mode})")
                if startup_mode == "standard":
                    print("🎯 Full HD interface with all features")
                elif startup_mode == "lightweight":
                    print("🪶 Lightweight mode - reduced resource usage")
                else:
                    print("⚡ Minimal mode - maximum performance")

                # Add optimized welcome messages
                interface._add_message("System", f"🚀 Gideon AI Assistant - {startup_mode.title()} Mode", 
                                     interface.design_system['colors']['accent_success'])
                interface._add_message("System", "💬 Chat ready - Type your message below", 
                                     interface.design_system['colors']['text_secondary'])

                # Start the main interface
                interface.run()

            except Exception as e:
                logger.error(f"Error during initialization: {e}")
                print(f"❌ Error during initialization: {e}")
                traceback.print_exc()

        # Optimized startup based on mode
        if startup_mode == "minimal":
            # Skip splash screen for minimal mode
            print("⚡ Fast startup mode - launching directly...")
            initialize_application_optimized()
        else:
            # Show splash screen for standard/lightweight modes
            try:
                from src.ui.enterprise_splash import EnterpriseSplashScreen
                print("🎬 Launching optimized splash screen...")
                splash_duration = 1.5 if startup_mode == "lightweight" else 2.0
                splash = EnterpriseSplashScreen(on_complete=initialize_application_optimized)
                splash.show(total_duration=splash_duration)
            except ImportError:
                # Fallback if splash screen not available
                print("⚡ Splash screen unavailable - launching directly...")
                initialize_application_optimized()

        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
        cleanup_memory()
        return 0
        
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        traceback.print_exc()
        cleanup_memory()
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
