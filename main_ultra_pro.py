#!/usr/bin/env python3
"""
Gideon AI Assistant - Optimized Main Entry Point

A bilingual AI assistant with voice interaction capabilities.
Optimized for minimal resource usage and fast startup.

Features:
- Bilingual speech recognition and text-to-speech (lazy loaded)
- Modern GUI interface (lightweight startup)
- AI model integration (on-demand loading)
- Voice wake word detection (optional)
- Real-time language switching
"""

import sys
import traceback
import gc
import threading
import time
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

# Resource monitoring
import psutil
import os

# Initialize application with resource monitoring
print("🚀 Starting Gideon AI Assistant (Optimized)...")
print("💡 Lightweight startup mode enabled")

def check_system_resources():
    """Check system resources and adjust startup accordingly"""
    try:
        # Get system info
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        available_memory_gb = memory.available / (1024**3)

        print(f"📊 System Status:")
        print(f"   CPU Usage: {cpu_percent:.1f}%")
        print(f"   Available Memory: {available_memory_gb:.1f} GB")
        print(f"   Memory Usage: {memory.percent:.1f}%")

        # Determine startup mode based on resources
        if cpu_percent > 80 or memory.percent > 85:
            print("⚠️ High system load detected - Using minimal startup mode")
            return "minimal"
        elif available_memory_gb < 2:
            print("⚠️ Low memory detected - Using lightweight mode")
            return "lightweight"
        else:
            print("✅ System resources adequate - Using standard mode")
            return "standard"

    except Exception as e:
        print(f"⚠️ Resource check failed: {e} - Using safe mode")
        return "minimal"

def check_dependencies(startup_mode="standard"):
    """Check for required dependencies with optimized loading"""
    print("🔍 Checking Dependencies (Optimized)...")
    print("=" * 50)

    missing_deps = []
    optional_deps = []

    # Check for CustomTkinter (required for modern UI)
    try:
        import customtkinter
        print("✅ CustomTkinter available - Modern UI ready")
    except ImportError:
        print("❌ CustomTkinter REQUIRED for modern interface")
        print("Install with: pip install customtkinter")
        missing_deps.append('customtkinter')

    # Core dependencies (always check)
    core_deps = [
        ('Pillow', 'PIL'),
    ]

    # Speech dependencies (check based on startup mode)
    if startup_mode != "minimal":
        core_deps.extend([
            ('speech_recognition', 'speech_recognition'),
            ('pyttsx3', 'pyttsx3'),
        ])

        # Only check pyaudio if not in minimal mode (resource intensive)
        if startup_mode == "standard":
            core_deps.append(('pyaudio', 'pyaudio'))

    for dep_name, import_name in core_deps:
        try:
            __import__(import_name)
            print(f"✅ {dep_name} available")
        except ImportError:
            if startup_mode == "minimal" and dep_name in ['speech_recognition', 'pyttsx3', 'pyaudio']:
                optional_deps.append(dep_name)
                print(f"⚠️ {dep_name} not available (will load on demand)")
            else:
                missing_deps.append(dep_name)
                print(f"❌ {dep_name} missing")

    # AI dependencies (optional, check quickly)
    if startup_mode == "standard":
        ai_deps = [
            ('ollama', 'ollama'),
            ('transformers', 'transformers'),
        ]

        ai_available = 0
        for dep_name, import_name in ai_deps:
            try:
                __import__(import_name)
                print(f"✅ {dep_name} available")
                ai_available += 1
            except ImportError:
                optional_deps.append(dep_name)
                print(f"⚠️ {dep_name} not available (will load on demand)")

        print(f"\n📊 Dependency Summary:")
        print(f"   AI Frameworks: {ai_available}/{len(ai_deps)} available")
    else:
        print(f"\n📊 Dependency Summary:")
        print(f"   Startup Mode: {startup_mode} (AI frameworks will load on demand)")

    if missing_deps:
        print(f"\n❌ Missing REQUIRED dependencies: {', '.join(missing_deps)}")
        print("Install with: pip install " + " ".join(missing_deps))
        return False

    if optional_deps and startup_mode == "standard":
        print(f"\n💡 Optional dependencies: {', '.join(optional_deps)}")
        print("These will be loaded on demand when needed")

    return True

def display_banner():
    """Display application banner"""
    banner = """
================================================================================
                                                                              
    GIDEON AI ASSISTANT                                                       
                                                                              
    Bilingual AI Assistant with Voice Interaction                            
                                                                              
    FEATURES:                                                                 
    • Bilingual Speech Recognition (Arabic/English)                          
    • Text-to-Speech with Female Voice                                       
    • Wake Word Detection ("Gideon")                                         
    • AI Model Integration (Ollama, Local Models)                            
    • Modern GUI Interface                                                   
    • Real-time Language Switching                                           
    • Chat History and Memory                                                
                                                                              
================================================================================
"""
    print(banner)

class LazyLoader:
    """Lazy loader for resource-intensive components"""

    def __init__(self):
        self._gideon_core = None
        self._interface = None
        self._logger = None
        self._startup_mode = "standard"

    def set_startup_mode(self, mode):
        """Set startup mode for resource optimization"""
        self._startup_mode = mode

    def get_logger(self):
        """Get logger instance (lazy loaded)"""
        if self._logger is None:
            from src.utils.logger import GideonLogger
            self._logger = GideonLogger("GideonMain")
        return self._logger

    def get_gideon_core(self):
        """Get Gideon core instance (lazy loaded)"""
        if self._gideon_core is None:
            print("🧠 Loading AI core systems...")
            from src.core.gideon_core import GideonCore
            self._gideon_core = GideonCore()

            # Initialize based on startup mode
            if self._startup_mode == "minimal":
                print("⚡ Minimal initialization mode")
                self._gideon_core.initialize_minimal()
            elif self._startup_mode == "lightweight":
                print("🪶 Lightweight initialization mode")
                self._gideon_core.initialize_lightweight()
            else:
                print("🚀 Standard initialization mode")
                self._gideon_core.initialize()

        return self._gideon_core

    def get_interface(self):
        """Get interface instance (lazy loaded)"""
        if self._interface is None:
            print("🎨 Creating optimized interface...")
            from src.ui.ultra_professional_interface import UltraProfessionalInterface

            # Get core first
            core = self.get_gideon_core()

            # Create interface with optimization flags
            self._interface = UltraProfessionalInterface(
                gideon_core=core,
                startup_mode=self._startup_mode
            )

        return self._interface

def main():
    """Optimized main entry point for Gideon AI Assistant"""
    display_banner()

    # Check system resources first
    startup_mode = check_system_resources()

    # Check dependencies based on startup mode
    if not check_dependencies(startup_mode):
        print("\n❌ Cannot start due to missing required dependencies")
        print("🔧 Please install the required packages and try again")
        input("Press Enter to exit...")
        return 1

    print(f"\n🚀 Starting Gideon AI Assistant (Mode: {startup_mode})...")
    print("⚡ Optimized for fast startup and low resource usage")

    try:
        # Initialize lazy loader
        lazy_loader = LazyLoader()
        lazy_loader.set_startup_mode(startup_mode)

        # Setup logging (lightweight)
        logger = lazy_loader.get_logger()
        logger.info(f"🤖 Starting Gideon AI Assistant - Mode: {startup_mode}")

        # Create essential directories only
        print("📁 Creating essential directories...")
        essential_dirs = ["data/logs", "data/cache"]
        for directory in essential_dirs:
            Path(directory).mkdir(parents=True, exist_ok=True)

        # Create additional directories on demand
        def create_additional_dirs():
            """Create additional directories when needed"""
            additional_dirs = [
                "data/models", "data/memory", "data/recordings",
                "data/screenshots", "assets", "assets/icons",
                "assets/images", "assets/fonts"
            ]
            for directory in additional_dirs:
                Path(directory).mkdir(parents=True, exist_ok=True)

        # Global variables for initialization
        gideon_core = None
        interface = None
        root = None

        def _setup_optimized_window_properties(root, logger, startup_mode):
            """Setup optimized window properties based on startup mode"""
            try:
                # Minimal window setup for better performance
                if startup_mode == "minimal":
                    # Skip icon loading and DPI awareness for minimal mode
                    root.attributes('-topmost', False)
                    root.state('normal')
                    return

                # Set icon only if available (don't block startup)
                icon_paths = [
                    current_dir / "assets" / "gideon_icon.ico",
                    current_dir / "assets" / "gideon_enterprise_icon_hd.ico"
                ]

                for icon_path in icon_paths:
                    if icon_path.exists():
                        try:
                            root.iconbitmap(str(icon_path))
                            break
                        except Exception:
                            continue  # Skip if icon loading fails

                # Set window attributes
                root.attributes('-topmost', False)
                root.state('normal')

                # Enable high-DPI awareness only in standard mode
                if startup_mode == "standard":
                    try:
                        import ctypes
                        ctypes.windll.shcore.SetProcessDpiAwareness(1)
                    except Exception:
                        pass  # Ignore DPI awareness errors

            except Exception as e:
                logger.warning(f"Window properties setup error: {e}")

        def _add_optimized_welcome_messages(interface, gideon_core, startup_mode):
            """Add optimized welcome messages based on startup mode"""

            # Print console messages based on mode
            if startup_mode == "minimal":
                print("⚡ Gideon AI Assistant ready! (Minimal Mode)")
                print("💬 Chat interface active - AI features load on demand")
            elif startup_mode == "lightweight":
                print("🪶 Gideon AI Assistant ready! (Lightweight Mode)")
                print("🎯 Optimized for performance with essential features")
            else:
                print("🚀 Gideon AI Assistant ready! (Standard Mode)")
                print("💎 Full feature set with enterprise capabilities")

            print("\n📱 INTERACTION METHODS:")
            print("  • Chat: Type in the interface")
            print("  • Languages: Arabic (primary) / English (secondary)")
            if startup_mode != "minimal":
                print("  • Voice: Say 'Gideon' + your question (loads on demand)")
                print("  • Models: AI models load automatically when needed")

            print("\n⌨️ KEYBOARD SHORTCUTS:")
            print("  • Enter: Send message")
            print("  • Ctrl+N: Clear chat history")
            if startup_mode == "standard":
                print("  • Ctrl+L: Switch language")
                print("  • F1: Show help")

            # Add interface messages (fewer for better performance)
            try:
                if startup_mode == "minimal":
                    interface._add_message("System", "⚡ Gideon AI Assistant - Minimal Mode", interface.design_system['colors']['accent_success'])
                    interface._add_message("System", "� Chat ready - AI features load on demand", interface.design_system['colors']['text_secondary'])
                elif startup_mode == "lightweight":
                    interface._add_message("System", "🪶 Gideon AI Assistant - Lightweight Mode", interface.design_system['colors']['accent_success'])
                    interface._add_message("System", "� Optimized performance with essential features", interface.design_system['colors']['text_secondary'])
                    interface._add_message("System", "🌐 Bilingual support: Arabic (primary) / English (secondary)", interface.design_system['colors']['accent_primary'])
                else:
                    interface._add_message("System", "🚀 Gideon AI Assistant - Standard Mode", interface.design_system['colors']['accent_success'])
                    interface._add_message("System", "💎 Full feature AI assistant ready", interface.design_system['colors']['text_secondary'])
                    interface._add_message("System", "🌐 Bilingual support: Arabic (primary) / English (secondary)", interface.design_system['colors']['accent_primary'])
                    interface._add_message("System", "🎤 Voice interaction available - Say 'Gideon' + your question", interface.design_system['colors']['accent_cyan'])

                # Check AI status (lightweight check)
                if gideon_core and hasattr(gideon_core, 'ai_engine'):
                    if hasattr(gideon_core.ai_engine, 'llm_enabled') and gideon_core.ai_engine.llm_enabled:
                        interface._add_message("System", "🤖 AI models ready for advanced responses", interface.design_system['colors']['accent_success'])
                    else:
                        interface._add_message("System", "💡 AI models will load automatically when needed", interface.design_system['colors']['accent_warning'])

            except Exception as e:
                logger.warning(f"Welcome message error: {e}")

            logger.info(f"Gideon AI Assistant ready - Mode: {startup_mode}")

        def initialize_application_optimized():
            """Optimized application initialization with lazy loading"""
            nonlocal gideon_core, interface, root

            try:
                # Create additional directories when needed
                threading.Thread(target=create_additional_dirs, daemon=True).start()

                # Initialize core with lazy loading
                print("🧠 Initializing core systems (optimized)...")
                gideon_core = lazy_loader.get_gideon_core()

                # Create interface with optimization
                print("🎨 Creating optimized interface...")
                interface = lazy_loader.get_interface()
                root = interface.create_window()

                # Setup window properties based on startup mode
                _setup_optimized_window_properties(root, logger, startup_mode)

                print(f"✅ Gideon AI Assistant Ready! (Mode: {startup_mode})")
                if startup_mode == "standard":
                    print("🎯 Full HD interface with all features")
                elif startup_mode == "lightweight":
                    print("🪶 Lightweight mode - reduced resource usage")
                else:
                    print("⚡ Minimal mode - maximum performance")

                # Add optimized welcome messages
                _add_optimized_welcome_messages(interface, gideon_core, startup_mode)

                # Start the main interface
                interface.run()

            except Exception as e:
                logger.error(f"Error during initialization: {e}")
                print(f"❌ Error during initialization: {e}")
                import traceback
                traceback.print_exc()

        # Optimized startup based on mode
        if startup_mode == "minimal":
            # Skip splash screen for minimal mode
            print("⚡ Fast startup mode - launching directly...")
            initialize_application_optimized()
        else:
            # Show splash screen for standard/lightweight modes
            try:
                from src.ui.enterprise_splash import EnterpriseSplashScreen
                print("🎬 Launching optimized splash screen...")
                splash_duration = 2.0 if startup_mode == "lightweight" else 3.0
                splash = EnterpriseSplashScreen(on_complete=initialize_application_optimized)
                splash.show(total_duration=splash_duration)
            except ImportError:
                # Fallback if splash screen not available
                print("⚡ Splash screen unavailable - launching directly...")
                initialize_application_optimized()

        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
        return 0
        
    except Exception as e:
        # Log fatal error and traceback for diagnostics
        try:
            from src.utils.logger import GideonLogger
            logger = GideonLogger("GideonMain")
            logger.error(f"Fatal error: {e}")
            logger.error(traceback.format_exc())
        except Exception:
            pass  # If logger import fails, fallback to print
        print(f"\n❌ Fatal error: {e}")
        print("\n🔍 Full error details:")
        traceback.print_exc()
        
        # Try to show error in GUI
        try:
            import tkinter as tk
            from tkinter import messagebox
            
            root = tk.Tk()
            root.withdraw()  # Hide main window
            
            messagebox.showerror(
                "Gideon AI Enterprise - Fatal Error",
                f"Failed to start Gideon AI Assistant Enterprise Edition:\n\n{e}\n\nCheck console for details."
            )
            
        except Exception:
            pass
        
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
