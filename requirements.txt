# Gideon AI Assistant Enterprise Edition Dependencies

# GUI Framework - Enterprise Grade
customtkinter>=5.2.0
kivy>=2.1.0
kivymd>=1.1.1
Pillow>=10.0.0

# Speech Processing (offline)
SpeechRecognition>=3.10.0
pyttsx3>=2.90

# System Integration & Monitoring
pyautogui>=0.9.54
psutil>=5.9.0

# Screen Capture
mss>=9.0.1

# Enterprise Utilities
python-dotenv>=1.0.0
colorlog>=6.7.0
cryptography>=41.0.0

# Arabic Language Support
arabic-reshaper>=3.0.0
python-bidi>=0.4.2

# Local LLM (lightweight)
transformers>=4.35.0
torch>=2.0.0
numpy>=1.24.0

# Audio Processing
sounddevice>=0.4.6

# Enterprise Data Management
sqlite3  # Built-in with Python
pandas>=2.0.0
openpyxl>=3.1.0

# Enterprise Security
bcrypt>=4.0.0
keyring>=24.0.0

# Enterprise Networking
requests>=2.31.0
urllib3>=2.0.0

# Enterprise Performance
cachetools>=5.3.0
memory-profiler>=0.61.0

# Enterprise Logging & Monitoring
structlog>=23.1.0
prometheus-client>=0.17.0

# Enterprise Configuration
pydantic>=2.0.0
toml>=0.10.2

# Enterprise Testing (Development)
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# Enterprise Documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
