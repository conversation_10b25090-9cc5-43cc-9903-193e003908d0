# Gideon AI Assistant - Core Dependencies
# Optimized for production use with only essential packages

# GUI Framework - Primary Interface
customtkinter>=5.2.0
Pillow>=10.0.0

# Speech Processing - Core Features
SpeechRecognition>=3.10.0
pyttsx3>=2.90
pyaudio>=0.2.11

# System Integration & Monitoring
psutil>=5.9.0
pyautogui>=0.9.54

# Screen Capture
mss>=9.0.1

# Arabic Language Support - Bilingual Features
arabic-reshaper>=3.0.0
python-bidi>=0.4.2

# AI/LLM Support - Optional but Recommended
# Uncomment the packages you want to use:

# For Ollama integration:
# ollama>=0.1.7

# For local GGUF models:
# llama-cpp-python>=0.2.0

# For Hugging Face transformers:
# transformers>=4.35.0
# torch>=2.0.0

# For CTransformers:
# ctransformers>=0.2.0

# Core Python Libraries
numpy>=1.24.0
requests>=2.31.0

# Configuration & Logging
python-dotenv>=1.0.0
colorlog>=6.7.0

# Development Dependencies (Optional)
# Uncomment for development:
# pytest>=7.4.0
# pytest-cov>=4.1.0
