"""
Terminal Interface for Gideon AI Assistant
Provides terminal access UI integrated into the main interface
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from datetime import datetime
from typing import Optional, Callable, Dict, Any, List
import threading
import time

try:
    import customtkinter as ctk
    CUSTOMTKINTER_AVAILABLE = True
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False

from src.utils.logger import Gideon<PERSON>og<PERSON>
from src.utils.config import Config
from src.utils.i18n import get_i18n
from src.system.terminal_manager import TerminalManager


class TerminalInterface:
    """Terminal interface panel for Gideon AI Assistant"""
    
    def __init__(self, parent_frame, gideon_core=None):
        self.logger = GideonLogger("TerminalInterface")
        self.config = Config()
        self.i18n = get_i18n()
        self.parent = parent_frame
        self.gideon_core = gideon_core
        
        # Terminal manager
        self.terminal_manager = TerminalManager()
        
        # UI components
        self.terminal_frame = None
        self.output_display = None
        self.command_entry = None
        self.send_button = None
        self.clear_button = None
        self.session_selector = None
        self.status_label = None
        
        # State
        self.is_visible = False
        self.command_history = []
        self.history_index = -1
        self.max_output_lines = 1000
        
        # Callbacks
        self.on_command_executed = None
        self.on_output_received = None
        
        # Setup terminal manager callbacks
        self.terminal_manager.on_output_received = self._on_terminal_output
        self.terminal_manager.on_command_executed = self._on_command_executed
        self.terminal_manager.on_error_occurred = self._on_terminal_error
        self.terminal_manager.on_confirmation_required = self._on_confirmation_required
        
        # Create interface
        self._create_terminal_interface()
    
    def _create_terminal_interface(self):
        """Create the terminal interface"""
        if CUSTOMTKINTER_AVAILABLE:
            self._create_modern_terminal_interface()
        else:
            self._create_fallback_terminal_interface()
    
    def _create_modern_terminal_interface(self):
        """Create modern terminal interface using CustomTkinter"""
        # Main terminal frame
        self.terminal_frame = ctk.CTkFrame(
            self.parent,
            fg_color="#0a0e13",  # Dark terminal background
            corner_radius=8
        )
        self.terminal_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Terminal header
        header_frame = ctk.CTkFrame(
            self.terminal_frame,
            height=40,
            fg_color="#161b22",
            corner_radius=6
        )
        header_frame.pack(fill="x", padx=5, pady=(5, 0))
        header_frame.pack_propagate(False)
        
        # Terminal title
        title_label = ctk.CTkLabel(
            header_frame,
            text="💻 TERMINAL ACCESS",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color="#58a6ff"
        )
        title_label.pack(side="left", padx=10, pady=8)
        
        # Session selector
        self.session_selector = ctk.CTkOptionMenu(
            header_frame,
            values=["Session 1"],
            command=self._on_session_changed,
            width=120,
            height=25,
            font=ctk.CTkFont(size=10),
            fg_color="#21262d",
            button_color="#30363d",
            button_hover_color="#484f58"
        )
        self.session_selector.pack(side="right", padx=10, pady=8)
        
        # Terminal output display
        output_frame = ctk.CTkFrame(
            self.terminal_frame,
            fg_color="#0d1117",
            corner_radius=6
        )
        output_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        self.output_display = ctk.CTkTextbox(
            output_frame,
            font=ctk.CTkFont(family="Consolas", size=11),
            fg_color="#0d1117",
            text_color="#f0f6fc",
            wrap="word",
            corner_radius=0,
            border_width=0,
            state="normal"
        )
        self.output_display.pack(fill="both", expand=True, padx=2, pady=2)
        
        # Command input area
        input_frame = ctk.CTkFrame(
            self.terminal_frame,
            height=50,
            fg_color="#161b22",
            corner_radius=6
        )
        input_frame.pack(fill="x", padx=5, pady=(0, 5))
        input_frame.pack_propagate(False)
        input_frame.grid_columnconfigure(0, weight=1)
        
        # Command prompt label
        prompt_label = ctk.CTkLabel(
            input_frame,
            text="$",
            font=ctk.CTkFont(family="Consolas", size=12, weight="bold"),
            text_color="#56d364",
            width=20
        )
        prompt_label.grid(row=0, column=0, sticky="w", padx=(10, 5), pady=10)
        
        # Command entry
        self.command_entry = ctk.CTkEntry(
            input_frame,
            placeholder_text="Enter terminal command...",
            font=ctk.CTkFont(family="Consolas", size=11),
            fg_color="#0d1117",
            border_color="#30363d",
            placeholder_text_color="#6e7681",
            height=30
        )
        self.command_entry.grid(row=0, column=1, sticky="ew", padx=5, pady=10)
        
        # Buttons frame
        buttons_frame = ctk.CTkFrame(input_frame, fg_color="transparent")
        buttons_frame.grid(row=0, column=2, padx=(5, 10), pady=10)
        
        # Send button
        self.send_button = ctk.CTkButton(
            buttons_frame,
            text="Execute",
            width=70,
            height=30,
            font=ctk.CTkFont(size=10, weight="bold"),
            command=self._execute_command,
            fg_color="#238636",
            hover_color="#2ea043"
        )
        self.send_button.pack(side="left", padx=(0, 5))
        
        # Clear button
        self.clear_button = ctk.CTkButton(
            buttons_frame,
            text="Clear",
            width=60,
            height=30,
            font=ctk.CTkFont(size=10, weight="bold"),
            command=self._clear_output,
            fg_color="#da3633",
            hover_color="#f85149"
        )
        self.clear_button.pack(side="left")
        
        # Status bar
        status_frame = ctk.CTkFrame(
            self.terminal_frame,
            height=25,
            fg_color="#161b22",
            corner_radius=6
        )
        status_frame.pack(fill="x", padx=5, pady=(0, 5))
        status_frame.pack_propagate(False)
        
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="Terminal ready",
            font=ctk.CTkFont(size=9),
            text_color="#8b949e"
        )
        self.status_label.pack(side="left", padx=10, pady=3)
        
        # Bind events
        self.command_entry.bind("<Return>", lambda e: self._execute_command())
        self.command_entry.bind("<Up>", self._history_up)
        self.command_entry.bind("<Down>", self._history_down)
        
        # Initialize with welcome message
        self._add_output("🤖 Gideon Terminal Access Initialized", "system")
        self._add_output("Type 'help' for available commands", "info")
        self._update_session_list()
    
    def _create_fallback_terminal_interface(self):
        """Create fallback terminal interface using standard Tkinter"""
        # Main terminal frame
        self.terminal_frame = tk.Frame(self.parent, bg="#0a0e13")
        self.terminal_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Terminal header
        header_frame = tk.Frame(self.terminal_frame, bg="#161b22", height=40)
        header_frame.pack(fill="x", padx=5, pady=(5, 0))
        header_frame.pack_propagate(False)
        
        # Terminal title
        title_label = tk.Label(
            header_frame,
            text="💻 TERMINAL ACCESS",
            font=("Arial", 10, "bold"),
            fg="#58a6ff",
            bg="#161b22"
        )
        title_label.pack(side="left", padx=10, pady=8)
        
        # Terminal output display
        output_frame = tk.Frame(self.terminal_frame, bg="#0d1117")
        output_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        self.output_display = scrolledtext.ScrolledText(
            output_frame,
            font=("Consolas", 10),
            bg="#0d1117",
            fg="#f0f6fc",
            insertbackground="#f0f6fc",
            wrap=tk.WORD,
            state=tk.NORMAL
        )
        self.output_display.pack(fill="both", expand=True, padx=2, pady=2)
        
        # Command input area
        input_frame = tk.Frame(self.terminal_frame, bg="#161b22", height=40)
        input_frame.pack(fill="x", padx=5, pady=(0, 5))
        input_frame.pack_propagate(False)
        
        # Command entry
        self.command_entry = tk.Entry(
            input_frame,
            font=("Consolas", 10),
            bg="#0d1117",
            fg="#f0f6fc",
            insertbackground="#f0f6fc",
            relief="flat",
            bd=5
        )
        self.command_entry.pack(side="left", fill="x", expand=True, padx=10, pady=5)
        
        # Send button
        self.send_button = tk.Button(
            input_frame,
            text="Execute",
            font=("Arial", 9, "bold"),
            command=self._execute_command,
            bg="#238636",
            fg="white",
            relief="flat"
        )
        self.send_button.pack(side="right", padx=(5, 10), pady=5)
        
        # Bind events
        self.command_entry.bind("<Return>", lambda e: self._execute_command())
        
        # Initialize with welcome message
        self._add_output("🤖 Gideon Terminal Access Initialized", "system")
        self._add_output("Type 'help' for available commands", "info")

    def _execute_command(self):
        """Execute the command entered by user"""
        command = self.command_entry.get().strip()
        if not command:
            return

        # Clear command entry
        self.command_entry.delete(0, tk.END if not CUSTOMTKINTER_AVAILABLE else "end")

        # Add to history
        if command not in self.command_history:
            self.command_history.append(command)
        self.history_index = len(self.command_history)

        # Display command in output
        self._add_output(f"$ {command}", "command")

        # Update status
        self._update_status("Executing command...")

        # Execute command in background thread
        threading.Thread(
            target=self._execute_command_async,
            args=(command,),
            daemon=True
        ).start()

    def _execute_command_async(self, command: str):
        """Execute command asynchronously"""
        try:
            # Handle built-in commands first
            if self._handle_builtin_command(command):
                return

            # Execute through terminal manager
            result = self.terminal_manager.execute_command(command)

            # Display result
            if result['success']:
                if result.get('output'):
                    self._add_output(result['output'], "output")
                if result.get('error'):
                    self._add_output(result['error'], "error")
                self._update_status("Command completed successfully")
            else:
                error_msg = result.get('error', 'Unknown error')
                self._add_output(f"Error: {error_msg}", "error")
                self._update_status("Command failed")

                # Handle confirmation required
                if result.get('requires_confirmation'):
                    self._handle_confirmation_required(command, result)

        except Exception as e:
            self.logger.error(f"Error executing command: {e}")
            self._add_output(f"Error: {str(e)}", "error")
            self._update_status("Command execution failed")

    def _handle_builtin_command(self, command: str) -> bool:
        """Handle built-in terminal commands"""
        command_lower = command.lower().strip()

        if command_lower in ['help', '?']:
            self._show_help()
            return True
        elif command_lower in ['clear', 'cls']:
            self._clear_output()
            return True
        elif command_lower.startswith('cd '):
            path = command[3:].strip()
            self._change_directory(path)
            return True
        elif command_lower == 'pwd':
            self._show_current_directory()
            return True
        elif command_lower == 'sessions':
            self._show_sessions()
            return True
        elif command_lower == 'history':
            self._show_command_history()
            return True
        elif command_lower.startswith('session '):
            session_id = command[8:].strip()
            self._switch_session(session_id)
            return True

        return False

    def _show_help(self):
        """Show help information"""
        help_text = """
🤖 Gideon Terminal Help:

Built-in Commands:
  help, ?          - Show this help
  clear, cls       - Clear terminal output
  cd <path>        - Change directory
  pwd              - Show current directory
  sessions         - List terminal sessions
  history          - Show command history
  session <id>     - Switch to session

System Commands:
  All standard Windows CMD/PowerShell commands are supported
  Examples: dir, ls, ping, ipconfig, systeminfo, tasklist

Security:
  • Dangerous commands require confirmation
  • Some commands are blocked for safety
  • All commands are logged

Arabic Commands Supported:
  مساعدة - Help
  مسح - Clear
  تاريخ - History
        """
        self._add_output(help_text, "info")
        self._update_status("Help displayed")

    def _clear_output(self):
        """Clear terminal output"""
        if CUSTOMTKINTER_AVAILABLE:
            self.output_display.delete("0.0", "end")
        else:
            self.output_display.delete("1.0", tk.END)

        self._add_output("Terminal cleared", "system")
        self._update_status("Terminal cleared")

    def _change_directory(self, path: str):
        """Change current directory"""
        result = self.terminal_manager.change_directory(path)

        if result['success']:
            self._add_output(f"Changed directory to: {result['new_path']}", "output")
            self._update_status(f"Directory: {result['new_path']}")
        else:
            self._add_output(f"Error: {result['error']}", "error")
            self._update_status("Directory change failed")

    def _show_current_directory(self):
        """Show current working directory"""
        cwd = self.terminal_manager.get_working_directory()
        self._add_output(f"Current directory: {cwd}", "output")
        self._update_status(f"Directory: {cwd}")

    def _show_sessions(self):
        """Show available terminal sessions"""
        sessions = self.terminal_manager.get_sessions()

        if not sessions:
            self._add_output("No active sessions", "info")
            return

        self._add_output("Active Terminal Sessions:", "info")
        for session_id, info in sessions.items():
            status = "CURRENT" if info['is_current'] else "ACTIVE"
            self._add_output(f"  {session_id}: {info['shell_type']} [{status}]", "output")

        self._update_status(f"{len(sessions)} sessions active")

    def _show_command_history(self):
        """Show command history"""
        if not self.command_history:
            self._add_output("No command history", "info")
            return

        self._add_output("Command History:", "info")
        for i, cmd in enumerate(self.command_history[-20:], 1):  # Show last 20
            self._add_output(f"  {i:2d}. {cmd}", "output")

        self._update_status(f"{len(self.command_history)} commands in history")

    def _switch_session(self, session_id: str):
        """Switch to different session"""
        if self.terminal_manager.switch_session(session_id):
            self._add_output(f"Switched to session: {session_id}", "system")
            self._update_status(f"Session: {session_id}")
            self._update_session_list()
        else:
            self._add_output(f"Error: Session '{session_id}' not found", "error")
            self._update_status("Session switch failed")

    def _add_output(self, text: str, output_type: str = "output"):
        """Add text to terminal output display"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")

            # Color coding based on output type
            colors = {
                "command": "#58a6ff",    # Blue for commands
                "output": "#f0f6fc",     # White for normal output
                "error": "#f85149",      # Red for errors
                "system": "#56d364",     # Green for system messages
                "info": "#e3b341",       # Yellow for info
                "warning": "#ff7eb6"     # Pink for warnings
            }

            color = colors.get(output_type, "#f0f6fc")

            # Format message
            if output_type == "command":
                formatted_text = f"[{timestamp}] {text}\n"
            else:
                formatted_text = f"{text}\n"

            # Add to display
            if CUSTOMTKINTER_AVAILABLE:
                self.output_display.insert("end", formatted_text)
                # Scroll to bottom
                self.output_display.see("end")
            else:
                self.output_display.insert(tk.END, formatted_text)
                # Scroll to bottom
                self.output_display.see(tk.END)

            # Limit output length
            self._limit_output_length()

        except Exception as e:
            self.logger.error(f"Error adding output: {e}")

    def _limit_output_length(self):
        """Limit the number of lines in output display"""
        try:
            if CUSTOMTKINTER_AVAILABLE:
                content = self.output_display.get("0.0", "end")
                lines = content.split('\n')

                if len(lines) > self.max_output_lines:
                    # Keep only the last max_output_lines
                    new_content = '\n'.join(lines[-self.max_output_lines:])
                    self.output_display.delete("0.0", "end")
                    self.output_display.insert("0.0", new_content)
            else:
                content = self.output_display.get("1.0", tk.END)
                lines = content.split('\n')

                if len(lines) > self.max_output_lines:
                    # Keep only the last max_output_lines
                    new_content = '\n'.join(lines[-self.max_output_lines:])
                    self.output_display.delete("1.0", tk.END)
                    self.output_display.insert("1.0", new_content)

        except Exception as e:
            self.logger.error(f"Error limiting output length: {e}")

    def _update_status(self, message: str):
        """Update status label"""
        try:
            if self.status_label:
                timestamp = datetime.now().strftime("%H:%M:%S")
                self.status_label.configure(text=f"[{timestamp}] {message}")
        except Exception as e:
            self.logger.error(f"Error updating status: {e}")

    def _update_session_list(self):
        """Update session selector with current sessions"""
        try:
            if not CUSTOMTKINTER_AVAILABLE or not self.session_selector:
                return

            sessions = self.terminal_manager.get_sessions()
            session_names = [f"{sid} ({info['shell_type']})" for sid, info in sessions.items()]

            if session_names:
                self.session_selector.configure(values=session_names)
                # Set current session
                current_session = self.terminal_manager.active_session_id
                if current_session:
                    for name in session_names:
                        if name.startswith(current_session):
                            self.session_selector.set(name)
                            break
            else:
                self.session_selector.configure(values=["No sessions"])

        except Exception as e:
            self.logger.error(f"Error updating session list: {e}")

    def _on_session_changed(self, selection: str):
        """Handle session selection change"""
        try:
            # Extract session ID from selection
            session_id = selection.split(' ')[0]
            self._switch_session(session_id)
        except Exception as e:
            self.logger.error(f"Error changing session: {e}")

    def _history_up(self, event):
        """Navigate up in command history"""
        if self.command_history and self.history_index > 0:
            self.history_index -= 1
            command = self.command_history[self.history_index]
            self.command_entry.delete(0, tk.END if not CUSTOMTKINTER_AVAILABLE else "end")
            self.command_entry.insert(0, command)

    def _history_down(self, event):
        """Navigate down in command history"""
        if self.command_history and self.history_index < len(self.command_history) - 1:
            self.history_index += 1
            command = self.command_history[self.history_index]
            self.command_entry.delete(0, tk.END if not CUSTOMTKINTER_AVAILABLE else "end")
            self.command_entry.insert(0, command)
        elif self.history_index >= len(self.command_history) - 1:
            self.command_entry.delete(0, tk.END if not CUSTOMTKINTER_AVAILABLE else "end")
            self.history_index = len(self.command_history)

    def _handle_confirmation_required(self, command: str, result: Dict[str, Any]):
        """Handle commands that require user confirmation"""
        risk_level = result.get('risk_level', 'warning')
        reason = result.get('error', 'Command requires confirmation')

        # Show confirmation dialog
        title = "Command Confirmation Required"
        message = f"Command: {command}\n\nRisk Level: {risk_level.upper()}\nReason: {reason}\n\nDo you want to execute this command?"

        if messagebox.askyesno(title, message):
            # Execute with confirmation bypass
            threading.Thread(
                target=self._execute_confirmed_command,
                args=(command,),
                daemon=True
            ).start()
        else:
            self._add_output("Command execution cancelled by user", "warning")
            self._update_status("Command cancelled")

    def _execute_confirmed_command(self, command: str):
        """Execute command after user confirmation"""
        try:
            result = self.terminal_manager.execute_command(command, require_confirmation=False)

            if result['success']:
                if result.get('output'):
                    self._add_output(result['output'], "output")
                if result.get('error'):
                    self._add_output(result['error'], "error")
                self._update_status("Confirmed command completed")
            else:
                error_msg = result.get('error', 'Unknown error')
                self._add_output(f"Error: {error_msg}", "error")
                self._update_status("Confirmed command failed")

        except Exception as e:
            self.logger.error(f"Error executing confirmed command: {e}")
            self._add_output(f"Error: {str(e)}", "error")
            self._update_status("Confirmed command execution failed")

    # Terminal manager callback handlers
    def _on_terminal_output(self, output: str):
        """Handle terminal output callback"""
        if output:
            self._add_output(output, "output")

    def _on_command_executed(self, command: str, result: Dict[str, Any]):
        """Handle command executed callback"""
        if self.on_command_executed:
            self.on_command_executed(command, result)

    def _on_terminal_error(self, error: str):
        """Handle terminal error callback"""
        self._add_output(f"Terminal Error: {error}", "error")

    def _on_confirmation_required(self, command: str, risk_level: str, reason: str) -> bool:
        """Handle confirmation required callback"""
        title = "Terminal Command Confirmation"
        message = f"Command: {command}\n\nRisk Level: {risk_level.upper()}\nReason: {reason}\n\nExecute this command?"
        return messagebox.askyesno(title, message)

    # Public interface methods
    def show(self):
        """Show the terminal interface"""
        if self.terminal_frame:
            self.terminal_frame.pack(fill="both", expand=True)
            self.is_visible = True

    def hide(self):
        """Hide the terminal interface"""
        if self.terminal_frame:
            self.terminal_frame.pack_forget()
            self.is_visible = False

    def toggle(self):
        """Toggle terminal interface visibility"""
        if self.is_visible:
            self.hide()
        else:
            self.show()

    def execute_command_from_voice(self, command: str):
        """Execute command from voice input"""
        self.command_entry.delete(0, tk.END if not CUSTOMTKINTER_AVAILABLE else "end")
        self.command_entry.insert(0, command)
        self._execute_command()

    def get_status(self) -> Dict[str, Any]:
        """Get terminal interface status"""
        return {
            'visible': self.is_visible,
            'terminal_available': self.terminal_manager.is_available(),
            'active_session': self.terminal_manager.active_session_id,
            'command_history_count': len(self.command_history)
        }
