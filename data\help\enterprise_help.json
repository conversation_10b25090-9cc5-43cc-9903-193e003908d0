{"getting_started": {"title": "Getting Started with Gideon AI Enterprise", "sections": [{"title": "Welcome to Gideon AI Assistant", "content": "\nGideon AI Assistant Enterprise Edition is a professional-grade AI assistant designed for enterprise use. \nThis guide will help you get started with all the powerful features available.\n\nKey Features:\n• Full HD (1920x1080) optimized interface\n• High-DPI scaling for 4K displays\n• Bilingual support (Arabic/English)\n• Voice interaction with wake word detection\n• Advanced AI model management\n• Enterprise-grade security and data management\n                        "}, {"title": "First Steps", "content": "\n1. Interface Overview: The main interface consists of a sidebar with navigation and status, \n   and a main content area for conversations.\n\n2. Starting a Conversation: You can interact with <PERSON> in two ways:\n   • Type your message in the chat input area\n   • Use voice commands by saying \"<PERSON>\" followed by your question\n\n3. Language Support: <PERSON> supports both Arabic (primary) and English (secondary).\n   The interface automatically detects and responds in the appropriate language.\n                        "}]}, "voice_interaction": {"title": "Voice Interaction Guide", "sections": [{"title": "Voice Commands", "content": "\n<PERSON> supports advanced voice interaction with wake word detection.\n\nWake Word: \"<PERSON>\"\n• Say \"<PERSON>\" followed by your question or command\n• The system will automatically detect when you're speaking to <PERSON>\n• Voice recognition works in both Arabic and English\n\nVoice Settings:\n• Adjust voice sensitivity in Settings > Voice\n• Choose between different TTS voices\n• Configure noise suppression and echo cancellation\n                        "}, {"title": "Voice Troubleshooting", "content": "\nIf voice interaction isn't working:\n\n1. Check microphone permissions\n2. Verify audio device settings\n3. Adjust voice activation threshold\n4. Test microphone in system settings\n5. Restart audio services if needed\n\nThe system includes automatic audio device detection and recovery.\n                        "}]}, "ai_models": {"title": "AI Model Management", "sections": [{"title": "Managing AI Models", "content": "\nGideon supports multiple AI models for enhanced capabilities:\n\nSupported Models:\n• Ollama models (dolphin-llama3:70b, qwen3:235b, gemma3:27b)\n• Local transformer models\n• Rule-based fallback system\n\nModel Management:\n• Drag and drop model files to add new models\n• Switch between models in real-time\n• Monitor model performance and resource usage\n• Automatic fallback to rule-based responses if models fail\n                        "}, {"title": "Model Performance", "content": "\nOptimizing AI Model Performance:\n\n1. GPU Acceleration: Enable GPU acceleration in Settings > Performance\n2. Memory Management: Monitor memory usage in the performance panel\n3. Model Selection: Choose appropriate models based on your hardware\n4. Caching: Enable response caching for faster repeated queries\n\nThe system automatically optimizes performance based on available resources.\n                        "}]}, "keyboard_shortcuts": {"title": "Keyboard Shortcuts", "sections": [{"title": "Essential Shortcuts", "content": "\nMaster these keyboard shortcuts for efficient use:\n\nChat & Communication:\n• Enter: Send message\n• Shift+Enter: New line in message\n• Ctrl+L: Clear chat history\n• Ctrl+S: Save conversation\n\nNavigation:\n• F1: Show this help system\n• Ctrl+,: Open settings\n• Ctrl+Shift+T: Toggle terminal\n• Ctrl+M: Toggle model manager\n\nSystem:\n• Ctrl+Q: Quit application\n• F11: Toggle fullscreen\n• Ctrl+R: Refresh interface\n• Ctrl+Shift+D: Toggle debug mode\n                        "}]}, "enterprise_features": {"title": "Enterprise Features", "sections": [{"title": "Data Management", "content": "\nEnterprise Data Management Features:\n\nBackup & Restore:\n• Automatic daily backups\n• Manual backup creation\n• Full system restore capabilities\n• Encrypted backup storage\n\nData Export:\n• Export conversations in multiple formats (JSON, CSV, XML, SQLite)\n• Bulk data export with date filtering\n• Secure data transfer protocols\n\nData Retention:\n• Configurable data retention policies\n• Automatic cleanup of old data\n• Compliance with data protection regulations\n                        "}, {"title": "Security Features", "content": "\nEnterprise Security:\n\nAuthentication:\n• Optional user authentication\n• Session management\n• Auto-lock functionality\n\nData Protection:\n• End-to-end encryption\n• Secure memory clearing\n• Anonymized logging options\n• Audit trail maintenance\n\nAccess Control:\n• Role-based permissions\n• Activity monitoring\n• Secure configuration management\n                        "}]}, "troubleshooting": {"title": "Troubleshooting Guide", "sections": [{"title": "Common Issues", "content": "\nCommon Issues and Solutions:\n\nPerformance Issues:\n• Check system resources in Performance Monitor\n• Reduce AI model complexity if needed\n• Clear cache and temporary files\n• Restart the application\n\nVoice Recognition Problems:\n• Verify microphone permissions\n• Check audio device settings\n• Adjust noise suppression settings\n• Test with different wake word sensitivity\n\nAI Response Issues:\n• Check AI model status\n• Verify internet connection for online models\n• Try switching to a different model\n• Check error logs for detailed information\n                        "}, {"title": "Error Recovery", "content": "\nAutomatic Error Recovery:\n\nThe system includes comprehensive error recovery:\n• Automatic component restart\n• Fallback to alternative systems\n• Graceful degradation of features\n• Detailed error reporting\n\nManual Recovery Steps:\n1. Check the Performance Monitor for system status\n2. Review error logs in the terminal\n3. Try restarting individual components\n4. Use the Settings > Advanced > Reset options if needed\n5. Contact support with error reports if issues persist\n                        "}]}}