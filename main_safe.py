#!/usr/bin/env python3
"""
Gideon AI Assistant - Ultra Safe Mode

Designed to prevent system freezes with:
- Hard resource limits
- Progressive loading with user control
- Emergency fallback mode
- Comprehensive error handling
"""

import sys
import traceback
import gc
import threading
import time
import signal
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

print("🛡️ GIDEON AI ASSISTANT - ULTRA SAFE MODE")
print("=" * 60)
print("Designed to prevent system freezes with hard resource limits")
print("=" * 60)

class SafetyMonitor:
    """Monitor system resources and prevent freezes"""
    
    def __init__(self):
        self.max_memory_mb = 512  # Conservative 512MB limit
        self.max_cpu_time = 30    # Max 30 seconds CPU time per operation
        self.monitoring = True
        self.start_time = time.time()
        
    def check_memory(self):
        """Check memory usage"""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / (1024 * 1024)
            
            if memory_mb > self.max_memory_mb:
                print(f"🚨 MEMORY LIMIT EXCEEDED: {memory_mb:.1f} MB")
                self.emergency_cleanup()
                return False
            return True
        except:
            return True
    
    def check_cpu_time(self):
        """Check CPU time usage"""
        elapsed = time.time() - self.start_time
        if elapsed > self.max_cpu_time:
            print(f"🚨 CPU TIME LIMIT EXCEEDED: {elapsed:.1f}s")
            return False
        return True
    
    def emergency_cleanup(self):
        """Emergency cleanup"""
        print("🧹 Emergency cleanup...")
        gc.collect()
    
    def reset_timer(self):
        """Reset CPU timer for new operation"""
        self.start_time = time.time()

# Global safety monitor
safety = SafetyMonitor()

def safe_import(module_name, timeout=5):
    """Safely import a module with timeout"""
    print(f"📦 Importing {module_name}...")
    safety.reset_timer()
    
    try:
        start_time = time.time()
        module = __import__(module_name)
        import_time = time.time() - start_time
        
        if import_time > timeout:
            print(f"⚠️ {module_name} import took {import_time:.1f}s (slow)")
        else:
            print(f"✅ {module_name} imported in {import_time:.3f}s")
        
        if not safety.check_memory():
            raise MemoryError(f"Memory limit exceeded during {module_name} import")
        
        return module
    except Exception as e:
        print(f"❌ Failed to import {module_name}: {e}")
        raise

def check_dependencies_safe():
    """Check dependencies safely"""
    print("🔍 Checking dependencies safely...")
    
    required_deps = ["customtkinter", "PIL"]
    optional_deps = ["psutil", "speech_recognition", "pyttsx3"]
    
    missing_required = []
    
    for dep in required_deps:
        try:
            safe_import(dep, timeout=3)
        except Exception as e:
            missing_required.append(dep)
            print(f"❌ Required dependency missing: {dep}")
    
    if missing_required:
        print(f"\n❌ Missing required dependencies: {missing_required}")
        print("Install with: pip install " + " ".join(missing_required))
        return False
    
    print("✅ All required dependencies available")
    return True

class UltraSafeInterface:
    """Ultra-safe minimal interface"""
    
    def __init__(self):
        self.root = None
        self.chat_display = None
        self.input_entry = None
        self.features_loaded = set()
        
    def create_safe_window(self):
        """Create ultra-safe window"""
        print("🎨 Creating ultra-safe interface...")
        safety.reset_timer()
        
        try:
            import customtkinter as ctk
            
            # Create root with minimal settings
            self.root = ctk.CTk()
            self.root.title("Gideon AI Assistant - Ultra Safe Mode")
            self.root.geometry("800x600")
            
            # Configure grid
            self.root.grid_columnconfigure(0, weight=1)
            self.root.grid_rowconfigure(1, weight=1)
            
            # Status frame
            status_frame = ctk.CTkFrame(self.root)
            status_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=5)
            
            status_label = ctk.CTkLabel(
                status_frame,
                text="🛡️ ULTRA SAFE MODE - System Freeze Prevention Active",
                font=ctk.CTkFont(size=14, weight="bold")
            )
            status_label.pack(pady=5)
            
            # Chat frame
            chat_frame = ctk.CTkFrame(self.root)
            chat_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=5)
            chat_frame.grid_columnconfigure(0, weight=1)
            chat_frame.grid_rowconfigure(0, weight=1)
            
            # Chat display
            self.chat_display = ctk.CTkTextbox(
                chat_frame,
                wrap="word",
                font=ctk.CTkFont(size=11)
            )
            self.chat_display.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
            
            # Input frame
            input_frame = ctk.CTkFrame(self.root)
            input_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=5)
            input_frame.grid_columnconfigure(0, weight=1)
            
            # Input entry
            self.input_entry = ctk.CTkEntry(
                input_frame,
                placeholder_text="Type your message here...",
                font=ctk.CTkFont(size=11)
            )
            self.input_entry.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
            self.input_entry.bind("<Return>", lambda e: self.send_message())
            
            # Send button
            send_button = ctk.CTkButton(
                input_frame,
                text="Send",
                command=self.send_message,
                width=80
            )
            send_button.grid(row=0, column=1, padx=5, pady=5)
            
            # Control frame
            control_frame = ctk.CTkFrame(self.root)
            control_frame.grid(row=3, column=0, sticky="ew", padx=10, pady=5)
            
            # Feature loading buttons
            button_frame = ctk.CTkFrame(control_frame)
            button_frame.pack(fill="x", padx=5, pady=5)
            
            # Safe feature loading buttons
            ctk.CTkButton(
                button_frame, text="Load Basic AI", command=self.load_basic_ai, width=120
            ).grid(row=0, column=0, padx=2, pady=2)
            
            ctk.CTkButton(
                button_frame, text="Test Memory", command=self.test_memory, width=120
            ).grid(row=0, column=1, padx=2, pady=2)
            
            ctk.CTkButton(
                button_frame, text="System Info", command=self.show_system_info, width=120
            ).grid(row=0, column=2, padx=2, pady=2)
            
            ctk.CTkButton(
                button_frame, text="Emergency Stop", command=self.emergency_stop, width=120
            ).grid(row=0, column=3, padx=2, pady=2)
            
            # Add initial messages
            self.add_message("System", "🛡️ Ultra Safe Mode Active")
            self.add_message("System", "✅ Basic chat interface loaded")
            self.add_message("System", "🔒 System freeze prevention enabled")
            self.add_message("System", "💡 Use buttons above to load features safely")
            
            print("✅ Ultra-safe interface created successfully")
            return self.root
            
        except Exception as e:
            print(f"❌ Failed to create interface: {e}")
            raise
    
    def add_message(self, sender, message):
        """Add message safely"""
        try:
            timestamp = time.strftime("%H:%M:%S")
            formatted_message = f"[{timestamp}] {sender}: {message}\n"
            
            self.chat_display.insert("end", formatted_message)
            self.chat_display.see("end")
            
        except Exception as e:
            print(f"Error adding message: {e}")
    
    def send_message(self):
        """Send message safely"""
        try:
            message = self.input_entry.get().strip()
            if not message:
                return
            
            self.input_entry.delete(0, "end")
            self.add_message("You", message)
            
            # Simple safe response
            response = self.get_safe_response(message)
            self.add_message("Gideon", response)
            
        except Exception as e:
            self.add_message("System", f"Error: {e}")
    
    def get_safe_response(self, message):
        """Get safe rule-based response"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["hello", "hi", "hey"]):
            return "Hello! I'm running in ultra-safe mode to prevent system freezes."
        elif any(word in message_lower for word in ["help", "what"]):
            return "I'm in ultra-safe mode. Use the buttons above to load features safely."
        elif any(word in message_lower for word in ["status", "mode"]):
            return "Ultra-safe mode active with system freeze prevention."
        elif any(word in message_lower for word in ["memory", "system"]):
            return f"Memory usage: {self.get_memory_usage():.1f} MB"
        else:
            return "I'm in ultra-safe mode. I can provide basic responses and system information."
    
    def get_memory_usage(self):
        """Get current memory usage"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / (1024 * 1024)
        except:
            return 0
    
    def load_basic_ai(self):
        """Load basic AI safely"""
        try:
            self.add_message("System", "🧠 Loading basic AI engine...")
            
            # Simulate safe AI loading
            time.sleep(0.5)  # Brief delay
            
            if not safety.check_memory():
                self.add_message("System", "❌ Memory limit reached - AI loading cancelled")
                return
            
            self.features_loaded.add("basic_ai")
            self.add_message("System", "✅ Basic AI engine loaded safely")
            
        except Exception as e:
            self.add_message("System", f"❌ Failed to load AI: {e}")
    
    def test_memory(self):
        """Test memory allocation"""
        try:
            self.add_message("System", "🧪 Testing memory allocation...")
            
            # Small memory test
            test_data = [0] * (1024 * 1024)  # 1MB test
            memory_usage = self.get_memory_usage()
            
            del test_data
            gc.collect()
            
            self.add_message("System", f"✅ Memory test passed - Usage: {memory_usage:.1f} MB")
            
        except Exception as e:
            self.add_message("System", f"❌ Memory test failed: {e}")
    
    def show_system_info(self):
        """Show system information"""
        try:
            import psutil
            
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            
            self.add_message("System", f"💻 CPU: {cpu_percent:.1f}%")
            self.add_message("System", f"💾 Memory: {memory.percent:.1f}% used")
            self.add_message("System", f"🔧 Features loaded: {len(self.features_loaded)}")
            
        except Exception as e:
            self.add_message("System", f"❌ System info error: {e}")
    
    def emergency_stop(self):
        """Emergency stop"""
        self.add_message("System", "🚨 EMERGENCY STOP - Shutting down safely")
        self.root.after(1000, self.root.quit)
    
    def run(self):
        """Run the interface safely"""
        try:
            print("🚀 Starting ultra-safe interface...")
            self.root.mainloop()
        except Exception as e:
            print(f"❌ Interface error: {e}")

def main():
    """Ultra-safe main entry point"""
    print("🛡️ Starting Ultra Safe Mode...")
    
    try:
        # Check dependencies
        if not check_dependencies_safe():
            input("Press Enter to exit...")
            return 1
        
        # Create essential directories
        Path("data/logs").mkdir(parents=True, exist_ok=True)
        
        # Memory cleanup
        gc.collect()
        print("🧹 Memory cleanup completed")
        
        # Create ultra-safe interface
        interface = UltraSafeInterface()
        root = interface.create_safe_window()
        
        print("✅ Ultra Safe Mode ready")
        print("🛡️ System freeze prevention active")
        
        # Run interface
        interface.run()
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
        return 0
        
    except Exception as e:
        print(f"❌ Ultra Safe Mode failed: {e}")
        traceback.print_exc()
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
