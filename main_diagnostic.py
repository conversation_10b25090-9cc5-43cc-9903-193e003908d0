#!/usr/bin/env python3
"""
Gideon AI Assistant - Diagnostic Mode

Detailed logging and step-by-step initialization to identify freeze points.
"""

import sys
import traceback
import time
import gc
import threading
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

print("🔍 GIDEON AI ASSISTANT - DIAGNOSTIC MODE")
print("=" * 60)
print("This mode provides detailed logging to identify freeze points")
print("=" * 60)

class DiagnosticLogger:
    """Enhanced logger for diagnostic mode"""
    
    def __init__(self):
        self.log_file = Path("data/logs/diagnostic.log")
        self.log_file.parent.mkdir(parents=True, exist_ok=True)
        self.start_time = time.time()
        
        # Clear previous diagnostic log
        with open(self.log_file, "w", encoding="utf-8") as f:
            f.write(f"=== DIAGNOSTIC SESSION STARTED ===\n")
            f.write(f"Start time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
    
    def log_step(self, step_name, details=""):
        """Log a diagnostic step with timing"""
        elapsed = time.time() - self.start_time
        timestamp = time.strftime("%H:%M:%S")
        
        message = f"[{timestamp}] [{elapsed:.3f}s] STEP: {step_name}"
        if details:
            message += f" - {details}"
        
        print(message)
        
        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(message + "\n")
        except:
            pass
    
    def log_error(self, step_name, error):
        """Log an error with full traceback"""
        elapsed = time.time() - self.start_time
        timestamp = time.strftime("%H:%M:%S")
        
        message = f"[{timestamp}] [{elapsed:.3f}s] ERROR in {step_name}: {error}"
        print(f"❌ {message}")
        
        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(message + "\n")
                f.write(traceback.format_exc() + "\n")
        except:
            pass
    
    def log_success(self, step_name, timing=""):
        """Log successful completion of a step"""
        elapsed = time.time() - self.start_time
        timestamp = time.strftime("%H:%M:%S")
        
        message = f"[{timestamp}] [{elapsed:.3f}s] ✅ SUCCESS: {step_name}"
        if timing:
            message += f" ({timing})"
        
        print(message)
        
        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(message + "\n")
        except:
            pass

def check_system_resources_diagnostic(logger):
    """Check system resources with detailed logging"""
    logger.log_step("System Resource Check", "Starting resource analysis")
    
    try:
        import psutil
        logger.log_step("psutil import", "Successfully imported psutil")
        
        # CPU check
        logger.log_step("CPU Check", "Getting CPU usage")
        cpu_percent = psutil.cpu_percent(interval=0.1)
        logger.log_step("CPU Result", f"CPU Usage: {cpu_percent:.1f}%")
        
        # Memory check
        logger.log_step("Memory Check", "Getting memory information")
        memory = psutil.virtual_memory()
        available_memory_gb = memory.available / (1024**3)
        logger.log_step("Memory Result", f"Available: {available_memory_gb:.1f} GB, Used: {memory.percent:.1f}%")
        
        # Disk check
        logger.log_step("Disk Check", "Getting disk information")
        disk = psutil.disk_usage('.')
        disk_free_gb = disk.free / (1024**3)
        logger.log_step("Disk Result", f"Free: {disk_free_gb:.1f} GB")
        
        # Process check
        logger.log_step("Process Check", "Getting process information")
        process = psutil.Process()
        process_memory = process.memory_info().rss / (1024**2)  # MB
        logger.log_step("Process Result", f"Current process memory: {process_memory:.1f} MB")
        
        logger.log_success("System Resource Check", f"CPU: {cpu_percent:.1f}%, RAM: {memory.percent:.1f}%")
        
        # Determine mode
        if cpu_percent > 80 or memory.percent > 85:
            mode = "emergency"
            logger.log_step("Mode Selection", "HIGH LOAD DETECTED - Emergency mode recommended")
        elif available_memory_gb < 2:
            mode = "minimal"
            logger.log_step("Mode Selection", "LOW MEMORY DETECTED - Minimal mode recommended")
        else:
            mode = "standard"
            logger.log_step("Mode Selection", "Resources adequate - Standard mode possible")
        
        return mode
        
    except Exception as e:
        logger.log_error("System Resource Check", e)
        return "emergency"

def check_dependencies_diagnostic(logger, mode):
    """Check dependencies with detailed logging"""
    logger.log_step("Dependency Check", f"Checking dependencies for {mode} mode")
    
    dependencies = [
        ("customtkinter", "customtkinter", True),
        ("Pillow", "PIL", True),
        ("speech_recognition", "speech_recognition", mode != "emergency"),
        ("pyttsx3", "pyttsx3", mode != "emergency"),
        ("pyaudio", "pyaudio", mode == "standard"),
        ("psutil", "psutil", True),
        ("ollama", "ollama", mode == "standard"),
        ("transformers", "transformers", mode == "standard"),
        ("torch", "torch", mode == "standard"),
    ]
    
    missing_required = []
    
    for dep_name, import_name, required in dependencies:
        logger.log_step(f"Checking {dep_name}", f"Required: {required}")
        
        try:
            start_time = time.time()
            __import__(import_name)
            import_time = (time.time() - start_time) * 1000
            logger.log_success(f"{dep_name} import", f"{import_time:.1f}ms")
            
        except ImportError as e:
            if required:
                missing_required.append(dep_name)
                logger.log_error(f"{dep_name} import", f"REQUIRED dependency missing: {e}")
            else:
                logger.log_step(f"{dep_name} import", f"Optional dependency missing (OK for {mode} mode)")
    
    if missing_required:
        logger.log_error("Dependency Check", f"Missing required dependencies: {missing_required}")
        return False
    
    logger.log_success("Dependency Check", f"All required dependencies available for {mode} mode")
    return True

def test_component_loading(logger):
    """Test loading each component individually"""
    logger.log_step("Component Loading Test", "Testing individual component imports")
    
    components = [
        ("GideonCore", "src.core.gideon_core", "GideonCore"),
        ("AIEngine", "src.core.ai_engine", "AIEngine"),
        ("UltraProfessionalInterface", "src.ui.ultra_professional_interface", "UltraProfessionalInterface"),
        ("STTEngine", "src.speech.stt_engine", "STTEngine"),
        ("TTSEngine", "src.speech.tts_engine", "TTSEngine"),
        ("MemorySystem", "src.core.memory_system", "MemorySystem"),
        ("ModelManager", "src.core.model_manager", "ModelManager"),
    ]
    
    results = {}
    
    for component_name, module_path, class_name in components:
        logger.log_step(f"Testing {component_name}", f"Importing from {module_path}")
        
        try:
            start_time = time.time()
            module = __import__(module_path, fromlist=[class_name])
            component_class = getattr(module, class_name)
            import_time = (time.time() - start_time) * 1000
            
            logger.log_success(f"{component_name} import", f"{import_time:.1f}ms")
            results[component_name] = {"status": "success", "time": import_time}
            
        except Exception as e:
            logger.log_error(f"{component_name} import", e)
            results[component_name] = {"status": "failed", "error": str(e)}
    
    return results

def test_memory_allocation(logger):
    """Test memory allocation patterns"""
    logger.log_step("Memory Allocation Test", "Testing memory allocation patterns")
    
    try:
        import psutil
        process = psutil.Process()
        
        # Initial memory
        initial_memory = process.memory_info().rss / (1024**2)
        logger.log_step("Initial Memory", f"{initial_memory:.1f} MB")
        
        # Test small allocation
        logger.log_step("Small Allocation Test", "Allocating 10MB")
        small_data = [0] * (10 * 1024 * 1024 // 8)  # 10MB of integers
        small_memory = process.memory_info().rss / (1024**2)
        logger.log_step("Small Allocation Result", f"{small_memory:.1f} MB (+{small_memory - initial_memory:.1f} MB)")
        
        # Test medium allocation
        logger.log_step("Medium Allocation Test", "Allocating 100MB")
        medium_data = [0] * (100 * 1024 * 1024 // 8)  # 100MB of integers
        medium_memory = process.memory_info().rss / (1024**2)
        logger.log_step("Medium Allocation Result", f"{medium_memory:.1f} MB (+{medium_memory - small_memory:.1f} MB)")
        
        # Cleanup
        del small_data, medium_data
        gc.collect()
        cleanup_memory = process.memory_info().rss / (1024**2)
        logger.log_step("Memory Cleanup", f"{cleanup_memory:.1f} MB (freed {medium_memory - cleanup_memory:.1f} MB)")
        
        logger.log_success("Memory Allocation Test", "Memory allocation working normally")
        return True
        
    except Exception as e:
        logger.log_error("Memory Allocation Test", e)
        return False

def test_threading(logger):
    """Test threading functionality"""
    logger.log_step("Threading Test", "Testing thread creation and management")
    
    try:
        # Test simple thread
        logger.log_step("Simple Thread Test", "Creating simple thread")
        
        def simple_task():
            time.sleep(0.1)
            logger.log_step("Thread Task", "Simple thread completed")
        
        thread = threading.Thread(target=simple_task, daemon=True)
        thread.start()
        thread.join(timeout=1.0)
        
        if thread.is_alive():
            logger.log_error("Simple Thread Test", "Thread did not complete in time")
            return False
        
        logger.log_success("Simple Thread Test", "Thread completed successfully")
        
        # Test multiple threads
        logger.log_step("Multiple Thread Test", "Creating 5 threads")
        
        def multi_task(thread_id):
            time.sleep(0.05)
            logger.log_step(f"Thread {thread_id}", f"Thread {thread_id} completed")
        
        threads = []
        for i in range(5):
            thread = threading.Thread(target=multi_task, args=(i,), daemon=True)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join(timeout=1.0)
        
        alive_threads = [t for t in threads if t.is_alive()]
        if alive_threads:
            logger.log_error("Multiple Thread Test", f"{len(alive_threads)} threads did not complete")
            return False
        
        logger.log_success("Multiple Thread Test", "All threads completed successfully")
        return True
        
    except Exception as e:
        logger.log_error("Threading Test", e)
        return False

def main():
    """Diagnostic mode main entry point"""
    logger = DiagnosticLogger()
    logger.log_step("Diagnostic Mode Start", "Beginning comprehensive diagnostic")
    
    try:
        # Step 1: System Resources
        logger.log_step("PHASE 1", "System Resource Analysis")
        mode = check_system_resources_diagnostic(logger)
        
        # Step 2: Dependencies
        logger.log_step("PHASE 2", "Dependency Verification")
        if not check_dependencies_diagnostic(logger, mode):
            logger.log_error("Diagnostic", "Dependency check failed")
            return 1
        
        # Step 3: Component Loading
        logger.log_step("PHASE 3", "Component Loading Test")
        component_results = test_component_loading(logger)
        
        # Step 4: Memory Allocation
        logger.log_step("PHASE 4", "Memory Allocation Test")
        memory_ok = test_memory_allocation(logger)
        
        # Step 5: Threading
        logger.log_step("PHASE 5", "Threading Test")
        threading_ok = test_threading(logger)
        
        # Summary
        logger.log_step("DIAGNOSTIC SUMMARY", "Generating final report")
        
        failed_components = [name for name, result in component_results.items() if result["status"] == "failed"]
        
        if failed_components:
            logger.log_error("Diagnostic Summary", f"Failed components: {failed_components}")
        
        if not memory_ok:
            logger.log_error("Diagnostic Summary", "Memory allocation issues detected")
        
        if not threading_ok:
            logger.log_error("Diagnostic Summary", "Threading issues detected")
        
        if not failed_components and memory_ok and threading_ok:
            logger.log_success("Diagnostic Summary", "All tests passed - system should be stable")
            print("\n✅ DIAGNOSTIC COMPLETE - No issues detected")
            print("You can try running the normal application now.")
        else:
            logger.log_error("Diagnostic Summary", "Issues detected - recommend emergency mode")
            print("\n⚠️ DIAGNOSTIC COMPLETE - Issues detected")
            print("Recommend using emergency mode: python main_emergency.py")
        
        print(f"\n📋 Full diagnostic log saved to: {logger.log_file}")
        input("Press Enter to exit...")
        return 0
        
    except Exception as e:
        logger.log_error("Diagnostic Mode", e)
        print(f"❌ Diagnostic mode failed: {e}")
        traceback.print_exc()
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
