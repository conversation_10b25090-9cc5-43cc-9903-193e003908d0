"""
Configuration management for Gideon AI Assistant
"""

import os
import json
from pathlib import Path
from typing import Dict, Any


class Config:
    """Configuration manager for Gideon AI Assistant"""
    
    def __init__(self):
        self.config_file = "data/config.json"
        self.default_config = {
            "app": {
                "name": "Gideon AI Assistant",
                "version": "1.0.0",
                "theme": "dark",
                "language": "ar",
                "detected_language": "ar"
            },
            "ui": {
                "window_width": 1200,
                "window_height": 800,
                "always_on_top": False,
                "transparency": 0.95,
                "font_family": "Segoe UI",
                "font_size": 12
            },
            "speech": {
                "stt_enabled": True,
                "tts_enabled": True,
                "voice_activation": True,
                "wake_word": "gideon",
                "tts_rate": 200,
                "tts_volume": 0.8,
                "language": "en-US"
            },
            "ai": {
                "model_name": "gpt2",
                "max_tokens": 150,
                "temperature": 0.7,
                "response_timeout": 30
            },
            "system": {
                "auto_start": False,
                "minimize_to_tray": True,
                "screen_capture_enabled": True,
                "voice_commands_enabled": True
            },
            "memory": {
                "max_conversations": 1000,
                "auto_save": True,
                "learning_enabled": True
            },
            "languages": {
                "supported": ["ar", "en"],
                "default": "ar",
                "primary": "ar",
                "secondary": "en",
                "rtl_support": True
            },
            "models": {
                "max_models": 10,
                "auto_load": True,
                "default_model": None,
                "multi_model_enabled": True,
                "model_switching": True,
                "drag_drop_enabled": True
            }
        }
        self._config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # Merge with defaults to ensure all keys exist
                return self._merge_configs(self.default_config, config)
            else:
                # Create config file with defaults
                self.save_config(self.default_config)
                return self.default_config.copy()
        except Exception as e:
            print(f"Error loading config: {e}")
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any] = None):
        """Save configuration to file"""
        try:
            # Ensure data directory exists
            Path(self.config_file).parent.mkdir(parents=True, exist_ok=True)
            
            config_to_save = config or self._config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def _merge_configs(self, default: Dict, user: Dict) -> Dict:
        """Recursively merge user config with defaults"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def get(self, key_path: str, default=None):
        """Get configuration value using dot notation (e.g., 'ui.window_width')"""
        keys = key_path.split('.')
        value = self._config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value):
        """Set configuration value using dot notation"""
        keys = key_path.split('.')
        config = self._config
        
        # Navigate to the parent of the target key
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # Set the value
        config[keys[-1]] = value
        
        # Save to file
        self.save_config()
    
    def get_all(self) -> Dict[str, Any]:
        """Get all configuration"""
        return self._config.copy()
    
    def reset_to_defaults(self):
        """Reset configuration to defaults"""
        self._config = self.default_config.copy()
        self.save_config()
    
    # Convenience properties
    @property
    def app_name(self) -> str:
        return self.get("app.name", "Gideon AI Assistant")
    
    @property
    def window_size(self) -> tuple:
        return (self.get("ui.window_width", 1200), self.get("ui.window_height", 800))
    
    @property
    def theme(self) -> str:
        return self.get("app.theme", "dark")
    
    @property
    def language(self) -> str:
        return self.get("app.language", "ar")
    
    @property
    def speech_enabled(self) -> bool:
        return self.get("speech.stt_enabled", True) and self.get("speech.tts_enabled", True)
    
    @property
    def wake_word(self) -> str:
        return self.get("speech.wake_word", "gideon")
