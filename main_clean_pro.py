#!/usr/bin/env python3
"""
Clean Professional Gideon AI Assistant
Organized, user-friendly interface with Arabic support and professional design
"""

import sys
import os
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging to be clean but informative
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s: %(message)s'
)

def main():
    """Main entry point for clean professional Gideon AI Assistant"""
    
    print("🚀 Starting Gideon AI Assistant - Professional Edition")
    print("=" * 60)
    
    try:
        # Import core components
        from src.core.gideon_core import GideonCore
        from src.ui.clean_professional_interface import CleanProfessionalInterface
        
        print("✅ Initializing AI core systems...")
        
        # Initialize core with proper setup
        gideon_core = GideonCore()
        gideon_core.initialize()
        
        print("✅ Creating professional interface...")
        
        # Create clean professional interface
        interface = CleanProfessionalInterface(gideon_core)
        
        print("✅ Gideon AI Assistant Professional Edition Ready!")
        print("\nFEATURES:")
        print("• Clean, organized professional interface")
        print("• Arabic and English support with proper RTL/LTR")
        print("• Voice input and wake word detection")
        print("• Ultra-low latency AI responses")
        print("• Model management and performance monitoring")
        print("• Professional Flash-inspired design")
        print("\nKEYBOARD SHORTCUTS:")
        print("• Enter: Send message")
        print("• Ctrl+N: Clear chat")
        print("• Ctrl+L: Toggle language")
        print("• F1: Help")
        print("\nStarting interface...")
        print("=" * 60)
        
        # Start the interface
        interface.run()
        
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("\nPlease install required packages:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting Gideon: {e}")
        print("\nFor detailed logs, check the logs directory")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
