"""
Modern Professional Interface for Gideon AI Assistant
Sleek, professional design with advanced UI/UX patterns
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from datetime import datetime
from typing import Optional, Callable
import threading

from src.utils.logger import <PERSON><PERSON><PERSON><PERSON>
from src.utils.config import Config
from src.utils.i18n import get_i18n
from src.utils.text_direction import text_direction_manager

try:
    import customtkinter as ctk
    CUSTOMTKINTER_AVAILABLE = True
    # Set appearance mode and color theme
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False


class ModernGideonInterface:
    """Modern professional interface for Gideon AI Assistant"""
    
    def __init__(self, gideon_core=None):
        self.logger = GideonLogger("ModernInterface")
        self.config = Config()
        self.i18n = get_i18n()
        self.gideon_core = gideon_core
        
        # Window setup
        self.root = None
        self.is_running = False
        
        # Professional color scheme - GitHub Dark inspired
        self.colors = {
            # Background layers
            'bg_primary': '#0d1117',      # Main background
            'bg_secondary': '#161b22',    # Secondary surfaces
            'bg_tertiary': '#21262d',     # Elevated surfaces
            'bg_elevated': '#30363d',     # Cards and panels
            'bg_overlay': '#1c2128',      # Overlays and modals
            
            # Accent colors
            'accent_primary': '#58a6ff',  # Primary blue
            'accent_secondary': '#f78166', # Coral
            'accent_success': '#56d364',  # Success green
            'accent_warning': '#e3b341',  # Warning amber
            'accent_error': '#f85149',    # Error red
            'accent_purple': '#bc8cff',   # Purple
            'accent_pink': '#ff7eb6',     # Pink
            
            # Text hierarchy
            'text_primary': '#f0f6fc',    # Primary text
            'text_secondary': '#8b949e',  # Secondary text
            'text_muted': '#6e7681',      # Muted text
            'text_disabled': '#484f58',   # Disabled text
            'text_inverse': '#0d1117',    # Inverse text
            
            # Interactive states
            'border_default': '#30363d',  # Default borders
            'border_muted': '#21262d',    # Muted borders
            'border_accent': '#58a6ff',   # Accent borders
            'hover_bg': '#262c36',        # Hover state
            'active_bg': '#2d333b',       # Active state
            'focus_ring': '#58a6ff40',    # Focus ring
            
            # Special effects
            'glow_primary': '#58a6ff40',  # Primary glow
            'glow_success': '#56d36440',  # Success glow
            'glow_error': '#f8514940',    # Error glow
            'shadow_light': '#ffffff10',  # Light shadow
            'shadow_dark': '#010409',     # Dark shadow
        }
        
        # UI Components
        self.main_container = None
        self.sidebar = None
        self.chat_area = None
        self.input_area = None
        self.status_bar = None
        
        # Chat components
        self.chat_display = None
        self.input_entry = None
        self.send_button = None
        self.voice_button = None
        
        # State
        self.is_voice_active = False
        self.current_status = "ready"
        self.ai_status = "offline"
        
        # Setup callbacks
        if self.gideon_core:
            self.gideon_core.set_response_ready_callback(self._on_response_ready)
            self.gideon_core.set_status_change_callback(self._on_status_change)
            self.gideon_core.set_error_callback(self._on_error)
    
    def create_window(self):
        """Create the main window"""
        if CUSTOMTKINTER_AVAILABLE:
            self.root = ctk.CTk()
            self._setup_modern_window()
        else:
            self.root = tk.Tk()
            self._setup_fallback_window()
        
        self._create_layout()
        self._setup_bindings()
        self._apply_animations()
        
        return self.root
    
    def _setup_modern_window(self):
        """Setup modern CustomTkinter window"""
        self.root.title("🤖 GIDEON AI Assistant")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)
        
        # Configure window
        self.root.configure(fg_color=self.colors['bg_primary'])
        
        # Set window icon if available
        try:
            self.root.iconbitmap("assets/gideon_icon.ico")
        except:
            pass
    
    def _setup_fallback_window(self):
        """Setup fallback Tkinter window"""
        self.root.title("🤖 GIDEON AI Assistant")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)
        self.root.configure(bg=self.colors['bg_primary'])
    
    def _create_layout(self):
        """Create the main layout"""
        if CUSTOMTKINTER_AVAILABLE:
            self._create_modern_layout()
        else:
            self._create_fallback_layout()
    
    def _create_modern_layout(self):
        """Create modern layout with CustomTkinter"""
        # Main container with grid
        self.main_container = ctk.CTkFrame(self.root, fg_color="transparent")
        self.main_container.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Configure grid
        self.main_container.grid_columnconfigure(1, weight=1)
        self.main_container.grid_rowconfigure(0, weight=1)
        
        # Create sidebar
        self._create_sidebar()
        
        # Create main content area
        self._create_main_content()
        
        # Create status bar
        self._create_status_bar()
    
    def _create_sidebar(self):
        """Create professional sidebar"""
        self.sidebar = ctk.CTkFrame(
            self.main_container,
            width=280,
            fg_color=self.colors['bg_secondary'],
            corner_radius=12
        )
        self.sidebar.grid(row=0, column=0, sticky="nsew", padx=(0, 20))
        self.sidebar.grid_propagate(False)
        
        # Sidebar header
        header_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        header_frame.pack(fill="x", padx=20, pady=(20, 10))
        
        # Gideon logo/title
        title_label = ctk.CTkLabel(
            header_frame,
            text="🤖 GIDEON",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=self.colors['accent_primary']
        )
        title_label.pack(anchor="w")
        
        subtitle_label = ctk.CTkLabel(
            header_frame,
            text="AI Assistant",
            font=ctk.CTkFont(size=14),
            text_color=self.colors['text_secondary']
        )
        subtitle_label.pack(anchor="w")
        
        # AI Status indicator
        self._create_ai_status_indicator()
        
        # Navigation buttons
        self._create_navigation_buttons()
        
        # Quick actions
        self._create_quick_actions()
    
    def _create_ai_status_indicator(self):
        """Create AI status indicator"""
        status_frame = ctk.CTkFrame(self.sidebar, fg_color=self.colors['bg_tertiary'], corner_radius=8)
        status_frame.pack(fill="x", padx=20, pady=10)
        
        # Status header
        status_header = ctk.CTkLabel(
            status_frame,
            text="🧠 AI Status",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        status_header.pack(anchor="w", padx=15, pady=(15, 5))
        
        # Model status
        self.model_status_label = ctk.CTkLabel(
            status_frame,
            text="🤖 Model: Loading...",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_secondary']
        )
        self.model_status_label.pack(anchor="w", padx=15, pady=2)
        
        # Voice status
        self.voice_status_label = ctk.CTkLabel(
            status_frame,
            text="🎤 Voice: Ready",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['accent_success']
        )
        self.voice_status_label.pack(anchor="w", padx=15, pady=2)
        
        # Memory status
        self.memory_status_label = ctk.CTkLabel(
            status_frame,
            text="🧠 Memory: Active",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['accent_success']
        )
        self.memory_status_label.pack(anchor="w", padx=15, pady=(2, 15))
    
    def _create_navigation_buttons(self):
        """Create navigation buttons"""
        nav_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        nav_frame.pack(fill="x", padx=20, pady=10)
        
        nav_label = ctk.CTkLabel(
            nav_frame,
            text="Navigation",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary']
        )
        nav_label.pack(anchor="w", pady=(0, 10))
        
        # Navigation buttons with icons
        nav_buttons = [
            ("💬 Chat", self._focus_chat, self.colors['accent_primary']),
            ("🤖 AI Models", self._open_model_manager, self.colors['accent_purple']),
            ("🎤 Voice Test", self._test_speech, self.colors['accent_secondary']),
            ("📸 Screenshot", self._take_screenshot, self.colors['accent_warning']),
            ("⚙️ Settings", self._open_settings, self.colors['text_secondary']),
        ]
        
        for text, command, color in nav_buttons:
            btn = ctk.CTkButton(
                nav_frame,
                text=text,
                command=command,
                fg_color=self.colors['bg_elevated'],
                hover_color=self.colors['hover_bg'],
                text_color=color,
                font=ctk.CTkFont(size=12),
                height=40,
                anchor="w"
            )
            btn.pack(fill="x", pady=2)
    
    def _create_quick_actions(self):
        """Create quick action buttons"""
        actions_frame = ctk.CTkFrame(self.sidebar, fg_color="transparent")
        actions_frame.pack(fill="x", padx=20, pady=20, side="bottom")
        
        # Language toggle
        self.language_button = ctk.CTkButton(
            actions_frame,
            text="🌍 عربي/EN",
            command=self._toggle_language,
            fg_color=self.colors['accent_success'],
            hover_color=self.colors['accent_success'],
            font=ctk.CTkFont(size=12),
            height=35
        )
        self.language_button.pack(fill="x", pady=(0, 10))
        
        # Clear chat
        clear_button = ctk.CTkButton(
            actions_frame,
            text="🗑️ Clear Chat",
            command=self._clear_chat,
            fg_color=self.colors['accent_error'],
            hover_color=self.colors['accent_error'],
            font=ctk.CTkFont(size=12),
            height=35
        )
        clear_button.pack(fill="x")
    
    def _create_main_content(self):
        """Create main content area"""
        content_frame = ctk.CTkFrame(
            self.main_container,
            fg_color=self.colors['bg_secondary'],
            corner_radius=12
        )
        content_frame.grid(row=0, column=1, sticky="nsew")
        content_frame.grid_rowconfigure(0, weight=1)
        content_frame.grid_columnconfigure(0, weight=1)
        
        # Chat area
        self._create_chat_area(content_frame)
        
        # Input area
        self._create_input_area(content_frame)
    
    def _create_chat_area(self, parent):
        """Create chat display area"""
        chat_container = ctk.CTkFrame(parent, fg_color="transparent")
        chat_container.grid(row=0, column=0, sticky="nsew", padx=20, pady=(20, 10))
        chat_container.grid_rowconfigure(0, weight=1)
        chat_container.grid_columnconfigure(0, weight=1)
        
        # Chat header
        chat_header = ctk.CTkFrame(chat_container, fg_color=self.colors['bg_tertiary'], height=60)
        chat_header.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        chat_header.grid_propagate(False)
        
        header_label = ctk.CTkLabel(
            chat_header,
            text="💬 Conversation with Gideon",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=self.colors['text_primary']
        )
        header_label.pack(side="left", padx=20, pady=15)
        
        # Status indicator
        self.chat_status_label = ctk.CTkLabel(
            chat_header,
            text="● Online",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['accent_success']
        )
        self.chat_status_label.pack(side="right", padx=20, pady=15)
        
        # Chat display
        self.chat_display = ctk.CTkTextbox(
            chat_container,
            font=ctk.CTkFont(size=13, family="Consolas"),
            fg_color=self.colors['bg_primary'],
            text_color=self.colors['text_primary'],
            wrap="word",
            corner_radius=8
        )
        self.chat_display.grid(row=1, column=0, sticky="nsew")
    
    def _create_input_area(self, parent):
        """Create input area"""
        input_container = ctk.CTkFrame(parent, fg_color="transparent", height=100)
        input_container.grid(row=1, column=0, sticky="ew", padx=20, pady=(0, 20))
        input_container.grid_propagate(False)
        input_container.grid_columnconfigure(0, weight=1)
        
        # Input frame
        input_frame = ctk.CTkFrame(input_container, fg_color=self.colors['bg_tertiary'], corner_radius=25)
        input_frame.grid(row=0, column=0, sticky="ew", pady=10)
        input_frame.grid_columnconfigure(0, weight=1)
        
        # Input entry
        self.input_entry = ctk.CTkEntry(
            input_frame,
            placeholder_text="Type your message to Gideon...",
            font=ctk.CTkFont(size=14),
            fg_color="transparent",
            border_width=0,
            height=50
        )
        self.input_entry.grid(row=0, column=0, sticky="ew", padx=20, pady=10)
        
        # Voice button
        self.voice_button = ctk.CTkButton(
            input_frame,
            text="🎤",
            width=50,
            height=50,
            font=ctk.CTkFont(size=20),
            command=self._toggle_voice_input,
            fg_color=self.colors['accent_primary'],
            hover_color=self.colors['accent_primary'],
            corner_radius=25
        )
        self.voice_button.grid(row=0, column=1, padx=(0, 10), pady=10)
        
        # Send button
        self.send_button = ctk.CTkButton(
            input_frame,
            text="Send",
            width=80,
            height=50,
            font=ctk.CTkFont(size=14, weight="bold"),
            command=self._send_message,
            fg_color=self.colors['accent_success'],
            hover_color=self.colors['accent_success'],
            corner_radius=25
        )
        self.send_button.grid(row=0, column=2, padx=(0, 10), pady=10)
    
    def _create_status_bar(self):
        """Create status bar"""
        self.status_bar = ctk.CTkFrame(
            self.main_container,
            height=40,
            fg_color=self.colors['bg_tertiary'],
            corner_radius=8
        )
        self.status_bar.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(20, 0))
        self.status_bar.grid_propagate(False)
        
        # Status text
        self.status_label = ctk.CTkLabel(
            self.status_bar,
            text="Ready",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_secondary']
        )
        self.status_label.pack(side="left", padx=15, pady=10)
        
        # Version info
        version_label = ctk.CTkLabel(
            self.status_bar,
            text="Gideon AI v2.0",
            font=ctk.CTkFont(size=10),
            text_color=self.colors['text_muted']
        )
        version_label.pack(side="right", padx=15, pady=10)

    def _create_fallback_layout(self):
        """Create fallback layout for standard Tkinter"""
        # Simple layout for fallback
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Header
        header_frame = tk.Frame(main_frame, bg=self.colors['bg_secondary'], height=60)
        header_frame.pack(fill="x", pady=(0, 10))
        header_frame.pack_propagate(False)

        title_label = tk.Label(
            header_frame,
            text="🤖 GIDEON AI Assistant",
            font=("Arial", 18, "bold"),
            fg=self.colors['accent_primary'],
            bg=self.colors['bg_secondary']
        )
        title_label.pack(side="left", padx=20, pady=15)

        # Chat area
        self.chat_display = scrolledtext.ScrolledText(
            main_frame,
            font=("Consolas", 11),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            insertbackground=self.colors['text_primary'],
            wrap=tk.WORD,
            height=25
        )
        self.chat_display.pack(fill="both", expand=True, pady=(0, 10))

        # Input area
        input_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        input_frame.pack(fill="x", pady=(0, 10))

        self.input_entry = tk.Entry(
            input_frame,
            font=("Arial", 12),
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            insertbackground=self.colors['text_primary'],
            relief="flat",
            bd=5
        )
        self.input_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))

        self.voice_button = tk.Button(
            input_frame,
            text="🎤",
            font=("Arial", 14),
            bg=self.colors['accent_primary'],
            fg=self.colors['text_primary'],
            command=self._toggle_voice_input,
            relief="flat",
            width=4
        )
        self.voice_button.pack(side="right", padx=(0, 5))

        self.send_button = tk.Button(
            input_frame,
            text="Send",
            font=("Arial", 10),
            bg=self.colors['accent_success'],
            fg=self.colors['text_inverse'],
            command=self._send_message,
            relief="flat",
            width=8
        )
        self.send_button.pack(side="right")

    def _setup_bindings(self):
        """Setup event bindings"""
        # Enter key to send message
        self.input_entry.bind('<Return>', lambda e: self._send_message())

        # Window close event
        self.root.protocol("WM_DELETE_WINDOW", self._on_window_close)

        # Focus on input entry
        self.input_entry.focus_set()

    def _apply_animations(self):
        """Apply subtle animations and effects"""
        # Update AI status periodically
        self._update_ai_status()

        # Schedule periodic updates
        self.root.after(5000, self._apply_animations)

    def _update_ai_status(self):
        """Update AI status indicators"""
        if not hasattr(self, 'model_status_label'):
            return

        try:
            if self.gideon_core and hasattr(self.gideon_core, 'ai_engine'):
                ai_engine = self.gideon_core.ai_engine

                if ai_engine.llm_enabled and ai_engine.active_backend:
                    model_name = ai_engine.active_backend.current_model
                    if model_name:
                        # Truncate long model names
                        display_name = model_name.split(':')[0] if ':' in model_name else model_name
                        if len(display_name) > 20:
                            display_name = display_name[:17] + "..."

                        self.model_status_label.configure(
                            text=f"🤖 Model: {display_name}",
                            text_color=self.colors['accent_success']
                        )
                        self.ai_status = "online"
                    else:
                        self.model_status_label.configure(
                            text="🤖 Model: Loading...",
                            text_color=self.colors['accent_warning']
                        )
                        self.ai_status = "loading"
                else:
                    self.model_status_label.configure(
                        text="🤖 Model: Rule-based",
                        text_color=self.colors['accent_warning']
                    )
                    self.ai_status = "limited"
            else:
                self.model_status_label.configure(
                    text="🤖 Model: Offline",
                    text_color=self.colors['accent_error']
                )
                self.ai_status = "offline"

            # Update chat status
            if hasattr(self, 'chat_status_label'):
                status_text = {
                    "online": "● AI Online",
                    "loading": "⏳ Loading...",
                    "limited": "⚠️ Limited Mode",
                    "offline": "● Offline"
                }

                status_colors = {
                    "online": self.colors['accent_success'],
                    "loading": self.colors['accent_warning'],
                    "limited": self.colors['accent_warning'],
                    "offline": self.colors['accent_error']
                }

                self.chat_status_label.configure(
                    text=status_text.get(self.ai_status, "● Unknown"),
                    text_color=status_colors.get(self.ai_status, self.colors['text_muted'])
                )

        except Exception as e:
            self.logger.error(f"Error updating AI status: {e}")

    # Event handlers and functionality methods
    def _send_message(self):
        """Send text message"""
        message = self.input_entry.get().strip()
        if not message:
            return

        # Clear input
        self.input_entry.delete(0, tk.END)

        # Display user message
        self._add_message("You", message, self.colors['accent_primary'])

        # Process with Gideon
        if self.gideon_core:
            self.gideon_core.process_text_input(message, self._on_text_response)
        else:
            self._add_message("Gideon", "Core system not available", self.colors['accent_error'])

    def _toggle_voice_input(self):
        """Toggle voice input"""
        if not self.gideon_core:
            self._add_message("System", "Voice input not available", self.colors['accent_error'])
            return

        if self.is_voice_active:
            # Stop voice input
            self.is_voice_active = False
            self._update_voice_button()
        else:
            # Start voice input
            self.is_voice_active = True
            self._update_voice_button()
            self.gideon_core.process_voice_input(self._on_voice_response)

    def _update_voice_button(self):
        """Update voice button appearance"""
        if self.is_voice_active:
            if CUSTOMTKINTER_AVAILABLE:
                self.voice_button.configure(text="🔴", fg_color=self.colors['accent_error'])
            else:
                self.voice_button.configure(text="🔴", bg=self.colors['accent_error'])
        else:
            if CUSTOMTKINTER_AVAILABLE:
                self.voice_button.configure(text="🎤", fg_color=self.colors['accent_primary'])
            else:
                self.voice_button.configure(text="🎤", bg=self.colors['accent_primary'])

    def _focus_chat(self):
        """Focus on chat input"""
        self.input_entry.focus_set()

    def _take_screenshot(self):
        """Take screenshot"""
        if self.gideon_core:
            filepath = self.gideon_core.take_screenshot()
            if filepath:
                self._add_message("System", f"📸 Screenshot saved: {filepath}", self.colors['accent_success'])
            else:
                self._add_message("System", "❌ Failed to take screenshot", self.colors['accent_error'])
        else:
            self._add_message("System", "❌ Screenshot functionality not available", self.colors['accent_error'])

    def _clear_chat(self):
        """Clear chat display"""
        if CUSTOMTKINTER_AVAILABLE:
            self.chat_display.delete("0.0", tk.END)
        else:
            self.chat_display.delete("1.0", tk.END)

        self._add_message("System", "Chat cleared", self.colors['text_muted'])

    def _open_settings(self):
        """Open settings window"""
        messagebox.showinfo("Settings", "Settings panel coming soon!")

    def _open_model_manager(self):
        """Open AI model selector dialog"""
        try:
            from src.ui.model_selector import ModelSelectorDialog

            def on_model_changed(backend, model):
                self._add_message("System", f"🧠 AI Model changed: {model} via {backend}", self.colors['accent_success'])
                if self.gideon_core and self.gideon_core.tts_engine:
                    self.gideon_core.tts_engine.speak(f"AI model loaded: {model}")

            if self.gideon_core and hasattr(self.gideon_core, 'ai_engine'):
                dialog = ModelSelectorDialog(
                    self.root,
                    self.gideon_core.ai_engine,
                    on_model_changed
                )
                result = dialog.show()

                if result:
                    self.logger.info(f"Model configuration updated: {result}")
                    self._add_message("System", "✅ AI model configuration updated", self.colors['accent_success'])
            else:
                self._add_message("System", "❌ AI engine not available", self.colors['accent_error'])

        except ImportError as e:
            self.logger.error(f"Failed to import model selector: {e}")
            self._add_message("System", "❌ Model selector not available", self.colors['accent_error'])
        except Exception as e:
            self.logger.error(f"Error opening model selector: {e}")
            self._add_message("System", f"❌ Model selector error: {e}", self.colors['accent_error'])

    def _test_speech(self):
        """Test speech functionality"""
        try:
            if not self.gideon_core:
                messagebox.showerror("Error", "Gideon core not available")
                return

            # Test TTS first
            self._add_message("System", "🔊 Testing text-to-speech...", self.colors['accent_warning'])
            if self.gideon_core.tts_engine and self.gideon_core.tts_engine.is_available():
                self.gideon_core.tts_engine.speak("Hello! This is a text to speech test. Can you hear me clearly?")
                self._add_message("System", "✅ TTS test completed", self.colors['accent_success'])
            else:
                self._add_message("System", "❌ TTS not available", self.colors['accent_error'])

            # Test STT
            self._add_message("System", "🎤 Testing speech recognition...", self.colors['accent_warning'])
            if self.gideon_core.stt_engine and self.gideon_core.stt_engine.is_available():
                self._add_message("System", "🎤 Say 'Hello Gideon' now...", self.colors['accent_primary'])

                def test_callback(user_input, ai_response):
                    if user_input:
                        self._add_message("System", f"✅ Heard: '{user_input}'", self.colors['accent_success'])
                        if "gideon" in user_input.lower():
                            self._add_message("System", "🎯 Wake word detected successfully!", self.colors['accent_success'])
                        else:
                            self._add_message("System", "⚠️ Wake word not detected, but speech recognition works", self.colors['accent_warning'])
                    else:
                        self._add_message("System", "❌ No speech detected", self.colors['accent_error'])

                # Start voice input test
                self.gideon_core.process_voice_input(test_callback)
            else:
                self._add_message("System", "❌ Speech recognition not available", self.colors['accent_error'])

        except Exception as e:
            self.logger.error(f"Error testing speech: {e}")
            self._add_message("System", f"❌ Speech test error: {e}", self.colors['accent_error'])

    def _toggle_language(self):
        """Toggle between English and Arabic"""
        try:
            current_lang = self.config.get("app.language", "en")

            if current_lang == "en":
                # Switch to Arabic
                self.config.set("app.language", "ar")
                self.i18n.set_language("ar")

                # CRITICAL FIX: Set AI engine response language to Arabic
                if self.gideon_core and self.gideon_core.ai_engine:
                    self.gideon_core.ai_engine.set_response_language("ar")
                    self.logger.info("🌍 AI engine response language set to Arabic")

                self._add_message("System", "تم تغيير اللغة إلى العربية 🇸🇦", self.colors['accent_success'])

                # Test Arabic TTS
                if self.gideon_core and self.gideon_core.tts_engine:
                    self.gideon_core.tts_engine.speak("مرحباً! تم تغيير اللغة إلى العربية")

                # Update button text
                if CUSTOMTKINTER_AVAILABLE:
                    self.language_button.configure(text="🌍 English")
                else:
                    self.language_button.configure(text="🌍 English")

            else:
                # Switch to English
                self.config.set("app.language", "en")
                self.i18n.set_language("en")

                # CRITICAL FIX: Set AI engine response language to English
                if self.gideon_core and self.gideon_core.ai_engine:
                    self.gideon_core.ai_engine.set_response_language("en")
                    self.logger.info("🌍 AI engine response language set to English")

                self._add_message("System", "Language changed to English 🇺🇸", self.colors['accent_success'])

                # Test English TTS
                if self.gideon_core and self.gideon_core.tts_engine:
                    self.gideon_core.tts_engine.speak("Language changed to English. Hello!")

                # Update button text
                if CUSTOMTKINTER_AVAILABLE:
                    self.language_button.configure(text="🌍 عربي/AR")
                else:
                    self.language_button.configure(text="🌍 عربي/AR")

            # Reinitialize TTS with new language
            if self.gideon_core and self.gideon_core.tts_engine:
                self.gideon_core.tts_engine._set_voice()

        except Exception as e:
            self.logger.error(f"Error toggling language: {e}")
            self._add_message("System", f"❌ Language toggle error: {e}", self.colors['accent_error'])

    def _add_message(self, sender: str, message: str, color: str = None):
        """Add message to chat display with professional formatting and text direction support"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        if color is None:
            color = self.colors['text_primary']

        # Format message with modern styling
        if sender == "You":
            prefix = "👤"
            sender_color = self.colors['accent_primary']
        elif sender == "Gideon":
            prefix = "🤖"
            sender_color = self.colors['accent_success']
        elif sender == "System":
            prefix = "⚙️"
            sender_color = self.colors['text_secondary']
        else:
            prefix = "💬"
            sender_color = self.colors['text_muted']

        # Detect text direction and format accordingly
        formatted_message, direction = text_direction_manager.format_text_for_display(message)

        # Create directional message format
        if direction == "rtl":
            # RTL format: message - sender - timestamp (right-aligned)
            display_message = f"{formatted_message} :{sender} {prefix} [{timestamp}]\n"
            tag_name = f"rtl_{sender.lower()}"
            justify = 'right'
        else:
            # LTR format: timestamp - sender - message (left-aligned)
            display_message = f"[{timestamp}] {prefix} {sender}: {formatted_message}\n"
            tag_name = f"ltr_{sender.lower()}"
            justify = 'left'

        # Configure text tags for direction if widget supports it
        if hasattr(self.chat_display, 'tag_configure'):
            self.chat_display.tag_configure(
                tag_name,
                justify=justify,
                foreground=color,
                lmargin1=20 if direction == "ltr" else 0,
                rmargin=20 if direction == "rtl" else 0
            )

            # Insert with tag
            start_pos = self.chat_display.index(tk.END)
            self.chat_display.insert(tk.END, display_message)
            end_pos = self.chat_display.index(tk.END)
            self.chat_display.tag_add(tag_name, start_pos, end_pos)
        else:
            # Fallback for widgets without tag support
            if CUSTOMTKINTER_AVAILABLE:
                self.chat_display.insert(tk.END, display_message)
            else:
                self.chat_display.insert(tk.END, display_message)

        # Scroll to end
        if hasattr(self.chat_display, 'see'):
            self.chat_display.see(tk.END)

    # Callback methods
    def _on_text_response(self, user_input: str, ai_response: str):
        """Handle text response from Gideon"""
        if ai_response:
            self._add_message("Gideon", ai_response, self.colors['accent_success'])
        else:
            self._add_message("Gideon", "I didn't understand that. Could you rephrase?", self.colors['accent_warning'])

    def _on_voice_response(self, user_input: str, ai_response: str):
        """Handle voice response from Gideon"""
        self.is_voice_active = False
        self._update_voice_button()

        if user_input:
            self._add_message("You", f"🎤 {user_input}", self.colors['accent_primary'])

            if ai_response:
                self._add_message("Gideon", ai_response, self.colors['accent_success'])
            else:
                self._add_message("Gideon", "I heard you but couldn't process that.", self.colors['accent_warning'])
        else:
            self._add_message("System", "No speech detected", self.colors['accent_error'])

    def _on_response_ready(self, response: str):
        """Handle when response is ready"""
        if response:
            self._add_message("Gideon", response, self.colors['accent_success'])

    def _on_status_change(self, status: str):
        """Handle status change"""
        self.current_status = status

        if hasattr(self, 'status_label'):
            status_messages = {
                "listening": "🎤 Listening...",
                "processing": "🧠 Processing...",
                "speaking": "🔊 Speaking...",
                "ready": "✅ Ready",
                "error": "❌ Error",
                "offline": "⚠️ Offline"
            }

            status_colors = {
                "listening": self.colors['accent_primary'],
                "processing": self.colors['accent_warning'],
                "speaking": self.colors['accent_success'],
                "ready": self.colors['accent_success'],
                "error": self.colors['accent_error'],
                "offline": self.colors['accent_error']
            }

            message = status_messages.get(status, status)
            color = status_colors.get(status, self.colors['text_secondary'])

            if CUSTOMTKINTER_AVAILABLE:
                self.status_label.configure(text=message, text_color=color)
            else:
                self.status_label.configure(text=message, fg=color)

    def _on_error(self, error_message: str):
        """Handle error"""
        self._add_message("System", f"❌ Error: {error_message}", self.colors['accent_error'])

    def _on_window_close(self):
        """Handle window close event"""
        try:
            if self.gideon_core:
                self.gideon_core.shutdown()

            self.is_running = False
            self.root.quit()
            self.root.destroy()

        except Exception as e:
            self.logger.error(f"Error during window close: {e}")
            self.root.destroy()

    def run(self):
        """Run the interface"""
        try:
            self.is_running = True

            # Add welcome message
            self._add_message("System", "🚀 Gideon AI Assistant started successfully!", self.colors['accent_success'])
            self._add_message("System", "💡 Say 'Gideon' followed by your question, or type in the chat.", self.colors['text_secondary'])

            # Check AI status
            if self.gideon_core and hasattr(self.gideon_core, 'ai_engine'):
                if self.gideon_core.ai_engine.llm_enabled:
                    model_name = self.gideon_core.ai_engine.active_backend.current_model if self.gideon_core.ai_engine.active_backend else "Unknown"
                    self._add_message("System", f"🧠 AI Model active: {model_name}", self.colors['accent_success'])
                else:
                    self._add_message("System", "⚠️ Using rule-based responses. Click '🤖 AI Models' to configure advanced AI.", self.colors['accent_warning'])

            # Start the main loop
            self.root.mainloop()

        except Exception as e:
            self.logger.error(f"Error running interface: {e}")
            messagebox.showerror("Error", f"Failed to run interface: {e}")
        finally:
            self.is_running = False


def create_modern_interface(gideon_core=None):
    """Create and return modern interface"""
    interface = ModernGideonInterface(gideon_core)
    root = interface.create_window()
    return interface, root
