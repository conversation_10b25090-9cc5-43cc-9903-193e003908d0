"""
Simple and Clean Interface for Gideon AI Assistant
Focused on usability and clarity
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
from datetime import datetime
from typing import Optional, Callable
import threading

from src.utils.logger import <PERSON><PERSON><PERSON><PERSON>
from src.utils.config import Config
from src.utils.i18n import get_i18n
from src.utils.text_direction import text_direction_manager

try:
    import customtkinter as ctk
    CUSTOMTKINTER_AVAILABLE = True
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False


class SimpleInterface:
    """Simple, clean, and organized interface for Gideon AI Assistant"""
    
    def __init__(self, gideon_core=None):
        self.logger = GideonLogger("SimpleInterface")
        self.config = Config()
        self.i18n = get_i18n()
        self.gideon_core = gideon_core
        
        # Window setup
        self.root = None
        self.is_running = False
        
        # Simple color scheme
        self.colors = {
            'bg_primary': '#1a1a1a',      # Dark background
            'bg_secondary': '#2d2d2d',    # Secondary background
            'bg_input': '#3d3d3d',        # Input background
            'text_primary': '#ffffff',    # Primary text
            'text_secondary': '#b0b0b0',  # Secondary text
            'accent_blue': '#4a9eff',     # Blue accent
            'accent_green': '#4ade80',    # Green accent
            'accent_red': '#ef4444',      # Red accent
            'border': '#404040',          # Border color
        }
        
        # UI Components
        self.chat_display = None
        self.input_entry = None
        self.send_button = None
        self.voice_button = None
        self.status_label = None
        
        # State
        self.is_voice_active = False
    
    def create_window(self):
        """Create the simple main window"""
        if CUSTOMTKINTER_AVAILABLE:
            self.root = ctk.CTk()
            self._setup_modern_window()
        else:
            self.root = tk.Tk()
            self._setup_basic_window()
        
        self._create_layout()
        self._setup_bindings()
        
        return self.root
    
    def _setup_modern_window(self):
        """Setup modern CustomTkinter window"""
        self.root.title("🤖 Gideon AI Assistant")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        self.root.configure(fg_color=self.colors['bg_primary'])
    
    def _setup_basic_window(self):
        """Setup basic Tkinter window"""
        self.root.title("🤖 Gideon AI Assistant")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        self.root.configure(bg=self.colors['bg_primary'])
    
    def _create_layout(self):
        """Create simple and organized layout"""
        # Main container
        if CUSTOMTKINTER_AVAILABLE:
            main_frame = ctk.CTkFrame(self.root, fg_color="transparent")
        else:
            main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Header
        self._create_header(main_frame)
        
        # Chat area
        self._create_chat_area(main_frame)
        
        # Input area
        self._create_input_area(main_frame)
        
        # Status bar
        self._create_status_bar(main_frame)
    
    def _create_header(self, parent):
        """Create simple header"""
        if CUSTOMTKINTER_AVAILABLE:
            header = ctk.CTkFrame(parent, height=60, fg_color=self.colors['bg_secondary'])
            header.pack(fill="x", pady=(0, 20))
            header.pack_propagate(False)
            
            # Title
            title = ctk.CTkLabel(
                header,
                text="🤖 Gideon AI Assistant",
                font=ctk.CTkFont(size=20, weight="bold"),
                text_color=self.colors['text_primary']
            )
            title.pack(side="left", padx=20, pady=15)
            
            # Language indicator
            self.lang_label = ctk.CTkLabel(
                header,
                text="🌍 Arabic/English",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['text_secondary']
            )
            self.lang_label.pack(side="right", padx=20, pady=15)
        else:
            header = tk.Frame(parent, bg=self.colors['bg_secondary'], height=60)
            header.pack(fill="x", pady=(0, 20))
            header.pack_propagate(False)
            
            # Title
            title = tk.Label(
                header,
                text="🤖 Gideon AI Assistant",
                font=("Arial", 16, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']
            )
            title.pack(side="left", padx=20, pady=15)
            
            # Language indicator
            self.lang_label = tk.Label(
                header,
                text="🌍 Arabic/English",
                font=("Arial", 10),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_secondary']
            )
            self.lang_label.pack(side="right", padx=20, pady=15)
    
    def _create_chat_area(self, parent):
        """Create clean chat area"""
        if CUSTOMTKINTER_AVAILABLE:
            # Chat container
            chat_container = ctk.CTkFrame(parent, fg_color=self.colors['bg_secondary'])
            chat_container.pack(fill="both", expand=True, pady=(0, 20))
            
            # Chat display
            self.chat_display = ctk.CTkTextbox(
                chat_container,
                font=ctk.CTkFont(family="Consolas", size=12),
                fg_color=self.colors['bg_primary'],
                text_color=self.colors['text_primary'],
                wrap="word",
                corner_radius=8,
                border_width=1,
                border_color=self.colors['border']
            )
            self.chat_display.pack(fill="both", expand=True, padx=15, pady=15)
        else:
            # Chat container
            chat_container = tk.Frame(parent, bg=self.colors['bg_secondary'])
            chat_container.pack(fill="both", expand=True, pady=(0, 20))
            
            # Chat display
            self.chat_display = scrolledtext.ScrolledText(
                chat_container,
                font=("Consolas", 11),
                bg=self.colors['bg_primary'],
                fg=self.colors['text_primary'],
                insertbackground=self.colors['text_primary'],
                wrap=tk.WORD,
                relief="flat",
                bd=1
            )
            self.chat_display.pack(fill="both", expand=True, padx=15, pady=15)
    
    def _create_input_area(self, parent):
        """Create simple input area"""
        if CUSTOMTKINTER_AVAILABLE:
            input_frame = ctk.CTkFrame(parent, height=80, fg_color=self.colors['bg_secondary'])
            input_frame.pack(fill="x", pady=(0, 10))
            input_frame.pack_propagate(False)
            
            # Input entry
            self.input_entry = ctk.CTkEntry(
                input_frame,
                placeholder_text="Type your message here... (Arabic/English)",
                font=ctk.CTkFont(size=12),
                height=40,
                fg_color=self.colors['bg_input'],
                text_color=self.colors['text_primary'],
                placeholder_text_color=self.colors['text_secondary']
            )
            self.input_entry.pack(side="left", fill="x", expand=True, padx=(15, 10), pady=20)
            
            # Voice button
            self.voice_button = ctk.CTkButton(
                input_frame,
                text="🎤",
                width=50,
                height=40,
                font=ctk.CTkFont(size=16),
                fg_color=self.colors['accent_blue'],
                hover_color=self.colors['accent_green'],
                command=self._toggle_voice
            )
            self.voice_button.pack(side="right", padx=(5, 10), pady=20)
            
            # Send button
            self.send_button = ctk.CTkButton(
                input_frame,
                text="Send",
                width=80,
                height=40,
                font=ctk.CTkFont(size=12, weight="bold"),
                fg_color=self.colors['accent_green'],
                hover_color=self.colors['accent_blue'],
                command=self._send_message
            )
            self.send_button.pack(side="right", padx=(5, 5), pady=20)
        else:
            input_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], height=80)
            input_frame.pack(fill="x", pady=(0, 10))
            input_frame.pack_propagate(False)
            
            # Input entry
            self.input_entry = tk.Entry(
                input_frame,
                font=("Arial", 12),
                bg=self.colors['bg_input'],
                fg=self.colors['text_primary'],
                insertbackground=self.colors['text_primary'],
                relief="flat",
                bd=5
            )
            self.input_entry.pack(side="left", fill="x", expand=True, padx=(15, 10), pady=20)
            
            # Voice button
            self.voice_button = tk.Button(
                input_frame,
                text="🎤",
                font=("Arial", 14),
                bg=self.colors['accent_blue'],
                fg=self.colors['text_primary'],
                relief="flat",
                bd=0,
                width=4,
                command=self._toggle_voice
            )
            self.voice_button.pack(side="right", padx=(5, 10), pady=20)
            
            # Send button
            self.send_button = tk.Button(
                input_frame,
                text="Send",
                font=("Arial", 10, "bold"),
                bg=self.colors['accent_green'],
                fg=self.colors['text_primary'],
                relief="flat",
                bd=0,
                width=8,
                command=self._send_message
            )
            self.send_button.pack(side="right", padx=(5, 5), pady=20)
    
    def _create_status_bar(self, parent):
        """Create simple status bar"""
        if CUSTOMTKINTER_AVAILABLE:
            status_frame = ctk.CTkFrame(parent, height=30, fg_color=self.colors['bg_secondary'])
            status_frame.pack(fill="x")
            status_frame.pack_propagate(False)
            
            self.status_label = ctk.CTkLabel(
                status_frame,
                text="✅ Ready",
                font=ctk.CTkFont(size=10),
                text_color=self.colors['text_secondary']
            )
            self.status_label.pack(side="left", padx=15, pady=5)

    def _setup_bindings(self):
        """Setup keyboard shortcuts and bindings"""
        self.root.bind('<Return>', lambda e: self._send_message())
        self.root.bind('<Control-n>', lambda e: self._clear_chat())
        self.root.bind('<Control-l>', lambda e: self._toggle_language())
        self.root.bind('<F1>', lambda e: self._show_help())

        # Focus on input
        if self.input_entry:
            self.input_entry.focus()

    def _send_message(self):
        """Send message to AI"""
        if not self.input_entry:
            return

        message = self.input_entry.get().strip()
        if not message:
            return

        # Clear input
        self.input_entry.delete(0, 'end')

        # Add user message
        self._add_message("You", message, "user")

        # Update status
        self._update_status("🤔 Thinking...")

        # Process with AI in background
        threading.Thread(target=self._process_ai_response, args=(message,), daemon=True).start()

    def _process_ai_response(self, message: str):
        """Process AI response in background"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'ai_engine') and self.gideon_core.ai_engine:
                # Ensure the core is initialized
                if not self.gideon_core.is_initialized:
                    self.root.after(0, lambda: self._add_message("System", "Initializing AI engine...", "system"))
                    self.gideon_core.initialize()

                response = self.gideon_core.ai_engine.generate_response(message)
                self.root.after(0, lambda: self._add_message("Gideon", response, "ai"))
                self.root.after(0, lambda: self._update_status("✅ Ready"))
            else:
                self.root.after(0, lambda: self._add_message("System", "AI engine not available - initializing...", "system"))
                # Try to initialize the core
                if self.gideon_core and not self.gideon_core.is_initialized:
                    self.gideon_core.initialize()
                    if self.gideon_core.ai_engine:
                        response = self.gideon_core.ai_engine.generate_response(message)
                        self.root.after(0, lambda: self._add_message("Gideon", response, "ai"))
                        self.root.after(0, lambda: self._update_status("✅ Ready"))
                    else:
                        self.root.after(0, lambda: self._update_status("❌ Error"))
                else:
                    self.root.after(0, lambda: self._update_status("❌ Error"))
        except Exception as e:
            self.logger.error(f"AI response error: {e}")
            self.root.after(0, lambda: self._add_message("System", f"Error: {e}", "system"))
            self.root.after(0, lambda: self._update_status("❌ Error"))

    def _add_message(self, sender: str, message: str, msg_type: str = "user"):
        """Add message to chat display"""
        if not self.chat_display:
            return

        timestamp = datetime.now().strftime("%H:%M")

        # Format message based on type
        if msg_type == "user":
            icon = "👤"
            color_tag = "user"
        elif msg_type == "ai":
            icon = "🤖"
            color_tag = "ai"
        else:
            icon = "⚙️"
            color_tag = "system"

        # Handle Arabic text direction
        formatted_message, direction = text_direction_manager.format_text_for_display(message)

        if direction == "rtl":
            # Arabic text - right to left
            display_text = f"{formatted_message} :{sender} {icon} [{timestamp}]\n"
        else:
            # English text - left to right
            display_text = f"[{timestamp}] {icon} {sender}: {formatted_message}\n"

        # Insert message
        if CUSTOMTKINTER_AVAILABLE:
            self.chat_display.insert("end", display_text)
            self.chat_display.see("end")
        else:
            self.chat_display.insert(tk.END, display_text)
            self.chat_display.see(tk.END)

    def _toggle_voice(self):
        """Toggle voice recording"""
        if not self.is_voice_active:
            self._start_voice_recording()
        else:
            self._stop_voice_recording()

    def _start_voice_recording(self):
        """Start voice recording"""
        self.is_voice_active = True
        if CUSTOMTKINTER_AVAILABLE:
            self.voice_button.configure(text="🔴", fg_color=self.colors['accent_red'])
        else:
            self.voice_button.configure(text="🔴", bg=self.colors['accent_red'])

        self._update_status("🎤 Listening...")

        # Start voice recognition in background
        threading.Thread(target=self._voice_recognition, daemon=True).start()

    def _stop_voice_recording(self):
        """Stop voice recording"""
        self.is_voice_active = False
        if CUSTOMTKINTER_AVAILABLE:
            self.voice_button.configure(text="🎤", fg_color=self.colors['accent_blue'])
        else:
            self.voice_button.configure(text="🎤", bg=self.colors['accent_blue'])

        self._update_status("✅ Ready")

    def _voice_recognition(self):
        """Handle voice recognition"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'stt_engine'):
                # Listen for speech
                text = self.gideon_core.stt_engine.listen_once()
                if text:
                    # Insert recognized text into input
                    self.root.after(0, lambda: self.input_entry.insert(0, text))
                    self.root.after(0, lambda: self._stop_voice_recording())
                else:
                    self.root.after(0, lambda: self._stop_voice_recording())
            else:
                self.root.after(0, lambda: self._add_message("System", "Voice recognition not available", "system"))
                self.root.after(0, lambda: self._stop_voice_recording())
        except Exception as e:
            self.logger.error(f"Voice recognition error: {e}")
            self.root.after(0, lambda: self._stop_voice_recording())

    def _clear_chat(self):
        """Clear chat history"""
        if self.chat_display:
            if CUSTOMTKINTER_AVAILABLE:
                self.chat_display.delete("1.0", "end")
            else:
                self.chat_display.delete(1.0, tk.END)

            self._add_message("System", "Chat cleared", "system")

    def _toggle_language(self):
        """Toggle between Arabic and English"""
        # This would integrate with the language system
        self._add_message("System", "Language toggle feature", "system")

    def _show_help(self):
        """Show help information"""
        help_text = """
🤖 Gideon AI Assistant - Help

KEYBOARD SHORTCUTS:
• Enter: Send message
• Ctrl+N: Clear chat
• Ctrl+L: Toggle language
• F1: Show this help

FEATURES:
• Type messages in Arabic or English
• Click 🎤 to use voice input
• Automatic language detection
• Real-time AI responses
        """
        self._add_message("System", help_text.strip(), "system")

    def _update_status(self, status: str):
        """Update status bar"""
        if self.status_label:
            if CUSTOMTKINTER_AVAILABLE:
                self.status_label.configure(text=status)
            else:
                self.status_label.configure(text=status)

    def run(self):
        """Start the interface"""
        if not self.root:
            self.create_window()

        self.is_running = True
        self._add_message("System", "🚀 Gideon AI Assistant Ready!", "system")
        self._add_message("System", "Type a message or click 🎤 for voice input", "system")

        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.logger.info("Interface closed by user")
        finally:
            self.is_running = False

    def stop(self):
        """Stop the interface"""
        self.is_running = False
        if self.root:
            self.root.quit()
            self.root.destroy()
        else:
            status_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], height=30)
            status_frame.pack(fill="x")
            status_frame.pack_propagate(False)
            
            self.status_label = tk.Label(
                status_frame,
                text="✅ Ready",
                font=("Arial", 9),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_secondary']
            )
            self.status_label.pack(side="left", padx=15, pady=5)
