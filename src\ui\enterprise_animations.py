#!/usr/bin/env python3
"""
Enterprise Animation System for Gideon AI Assistant
Smooth transitions, micro-animations, and glass morphism effects
"""

import time
import threading
import math
from typing import Callable, Optional, Dict, Any
from src.utils.logger import <PERSON><PERSON>ogger

try:
    import customtkinter as ctk
    CUSTOMTKINTER_AVAILABLE = True
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False

class EnterpriseAnimationManager:
    """Manages smooth animations and transitions for enterprise UI"""
    
    def __init__(self):
        self.logger = GideonLogger("EnterpriseAnimations")
        self.active_animations = {}
        self.animation_id_counter = 0
        
        # Animation settings
        self.fps = 60
        self.frame_time = 1.0 / self.fps
        
        # Easing functions
        self.easing_functions = {
            'linear': lambda t: t,
            'ease_in': lambda t: t * t,
            'ease_out': lambda t: 1 - (1 - t) * (1 - t),
            'ease_in_out': lambda t: 2 * t * t if t < 0.5 else 1 - 2 * (1 - t) * (1 - t),
            'bounce': lambda t: self._bounce_easing(t),
            'elastic': lambda t: self._elastic_easing(t),
            'smooth': lambda t: t * t * (3 - 2 * t),
        }
    
    def _bounce_easing(self, t: float) -> float:
        """Bounce easing function"""
        if t < 1/2.75:
            return 7.5625 * t * t
        elif t < 2/2.75:
            t -= 1.5/2.75
            return 7.5625 * t * t + 0.75
        elif t < 2.5/2.75:
            t -= 2.25/2.75
            return 7.5625 * t * t + 0.9375
        else:
            t -= 2.625/2.75
            return 7.5625 * t * t + 0.984375
    
    def _elastic_easing(self, t: float) -> float:
        """Elastic easing function"""
        if t == 0 or t == 1:
            return t
        return -(2**(-10 * t)) * math.sin((t - 0.1) * (2 * math.pi) / 0.4) + 1
    
    def fade_in(self, widget, duration: float = 0.3, easing: str = 'ease_out', 
                on_complete: Optional[Callable] = None) -> str:
        """Fade in animation for widgets"""
        animation_id = f"fade_in_{self.animation_id_counter}"
        self.animation_id_counter += 1
        
        def animate():
            start_time = time.time()
            easing_func = self.easing_functions.get(easing, self.easing_functions['linear'])
            
            while True:
                elapsed = time.time() - start_time
                progress = min(elapsed / duration, 1.0)
                
                if animation_id not in self.active_animations:
                    break
                
                # Apply easing
                eased_progress = easing_func(progress)
                
                try:
                    # Update widget opacity/alpha
                    if hasattr(widget, 'configure'):
                        # For CustomTkinter widgets
                        if hasattr(widget, '_fg_color'):
                            current_color = widget._fg_color
                            if isinstance(current_color, tuple) and len(current_color) == 4:
                                new_color = (*current_color[:3], eased_progress)
                                widget.configure(fg_color=new_color)
                        
                        # Alternative: modify text color alpha
                        if hasattr(widget, '_text_color'):
                            current_color = widget._text_color
                            if isinstance(current_color, tuple) and len(current_color) == 4:
                                new_color = (*current_color[:3], eased_progress)
                                widget.configure(text_color=new_color)
                    
                except Exception as e:
                    self.logger.warning(f"Error in fade animation: {e}")
                
                if progress >= 1.0:
                    break
                
                time.sleep(self.frame_time)
            
            # Cleanup
            if animation_id in self.active_animations:
                del self.active_animations[animation_id]
            
            if on_complete:
                on_complete()
        
        self.active_animations[animation_id] = True
        threading.Thread(target=animate, daemon=True).start()
        return animation_id
    
    def slide_in(self, widget, direction: str = 'left', distance: int = 30, 
                 duration: float = 0.3, easing: str = 'ease_out',
                 on_complete: Optional[Callable] = None) -> str:
        """Slide in animation for widgets"""
        animation_id = f"slide_in_{self.animation_id_counter}"
        self.animation_id_counter += 1
        
        def animate():
            start_time = time.time()
            easing_func = self.easing_functions.get(easing, self.easing_functions['linear'])
            
            # Get initial position
            try:
                initial_x = widget.winfo_x()
                initial_y = widget.winfo_y()
            except:
                return
            
            # Calculate start position based on direction
            start_x, start_y = initial_x, initial_y
            if direction == 'left':
                start_x = initial_x - distance
            elif direction == 'right':
                start_x = initial_x + distance
            elif direction == 'up':
                start_y = initial_y - distance
            elif direction == 'down':
                start_y = initial_y + distance
            
            while True:
                elapsed = time.time() - start_time
                progress = min(elapsed / duration, 1.0)
                
                if animation_id not in self.active_animations:
                    break
                
                # Apply easing
                eased_progress = easing_func(progress)
                
                try:
                    # Calculate current position
                    current_x = start_x + (initial_x - start_x) * eased_progress
                    current_y = start_y + (initial_y - start_y) * eased_progress
                    
                    # Update widget position
                    widget.place(x=current_x, y=current_y)
                    
                except Exception as e:
                    self.logger.warning(f"Error in slide animation: {e}")
                
                if progress >= 1.0:
                    break
                
                time.sleep(self.frame_time)
            
            # Cleanup
            if animation_id in self.active_animations:
                del self.active_animations[animation_id]
            
            if on_complete:
                on_complete()
        
        self.active_animations[animation_id] = True
        threading.Thread(target=animate, daemon=True).start()
        return animation_id
    
    def scale_animation(self, widget, from_scale: float = 0.8, to_scale: float = 1.0,
                       duration: float = 0.3, easing: str = 'bounce',
                       on_complete: Optional[Callable] = None) -> str:
        """Scale animation for widgets"""
        animation_id = f"scale_{self.animation_id_counter}"
        self.animation_id_counter += 1
        
        def animate():
            start_time = time.time()
            easing_func = self.easing_functions.get(easing, self.easing_functions['linear'])
            
            while True:
                elapsed = time.time() - start_time
                progress = min(elapsed / duration, 1.0)
                
                if animation_id not in self.active_animations:
                    break
                
                # Apply easing
                eased_progress = easing_func(progress)
                
                try:
                    # Calculate current scale
                    current_scale = from_scale + (to_scale - from_scale) * eased_progress
                    
                    # Apply scale transformation (simplified for CustomTkinter)
                    if hasattr(widget, 'configure'):
                        # This is a simplified approach - real scaling would require more complex transformations
                        pass
                    
                except Exception as e:
                    self.logger.warning(f"Error in scale animation: {e}")
                
                if progress >= 1.0:
                    break
                
                time.sleep(self.frame_time)
            
            # Cleanup
            if animation_id in self.active_animations:
                del self.active_animations[animation_id]
            
            if on_complete:
                on_complete()
        
        self.active_animations[animation_id] = True
        threading.Thread(target=animate, daemon=True).start()
        return animation_id
    
    def pulse_animation(self, widget, intensity: float = 0.1, duration: float = 1.0,
                       cycles: int = -1, on_complete: Optional[Callable] = None) -> str:
        """Pulse animation for widgets (continuous until stopped)"""
        animation_id = f"pulse_{self.animation_id_counter}"
        self.animation_id_counter += 1
        
        def animate():
            start_time = time.time()
            cycle_count = 0
            
            while True:
                elapsed = time.time() - start_time
                cycle_progress = (elapsed % duration) / duration
                
                if animation_id not in self.active_animations:
                    break
                
                if cycles > 0 and cycle_count >= cycles:
                    break
                
                # Calculate pulse value using sine wave
                pulse_value = math.sin(cycle_progress * 2 * math.pi) * intensity
                
                try:
                    # Apply pulse effect (simplified)
                    if hasattr(widget, 'configure'):
                        # This would modify opacity or color intensity
                        pass
                    
                except Exception as e:
                    self.logger.warning(f"Error in pulse animation: {e}")
                
                # Check if we completed a cycle
                if elapsed >= (cycle_count + 1) * duration:
                    cycle_count += 1
                
                time.sleep(self.frame_time)
            
            # Cleanup
            if animation_id in self.active_animations:
                del self.active_animations[animation_id]
            
            if on_complete:
                on_complete()
        
        self.active_animations[animation_id] = True
        threading.Thread(target=animate, daemon=True).start()
        return animation_id
    
    def stop_animation(self, animation_id: str):
        """Stop a specific animation"""
        if animation_id in self.active_animations:
            del self.active_animations[animation_id]
    
    def stop_all_animations(self):
        """Stop all active animations"""
        self.active_animations.clear()
    
    def create_glass_effect(self, widget, blur_radius: int = 10, opacity: float = 0.1):
        """Apply glass morphism effect to widget"""
        try:
            if CUSTOMTKINTER_AVAILABLE and hasattr(widget, 'configure'):
                # Create glass-like appearance
                glass_color = (*self._hex_to_rgb('#1c212880'), opacity)
                widget.configure(
                    fg_color=glass_color,
                    border_width=1,
                    border_color=(*self._hex_to_rgb('#30363d'), 0.2)
                )
        except Exception as e:
            self.logger.warning(f"Error applying glass effect: {e}")
    
    def _hex_to_rgb(self, hex_color: str) -> tuple:
        """Convert hex color to RGB tuple"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) / 255.0 for i in (0, 2, 4))

# Global instance
enterprise_animations = EnterpriseAnimationManager()
