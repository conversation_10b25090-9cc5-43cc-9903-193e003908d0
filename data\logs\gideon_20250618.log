2025-06-18 17:24:14,869 - <PERSON><PERSON><PERSON> - INFO - 🤖 Starting Gideon AI Assistant
2025-06-18 17:24:14,872 - <PERSON><PERSON><PERSON> - INFO - Initializing Gideon Core System...
2025-06-18 17:24:14,878 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 17:24:14,878 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 17:24:14,879 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 17:24:14,880 - ModelManager - INFO - No existing models config found
2025-06-18 17:24:14,881 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 17:24:14,881 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 17:24:14,883 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 17:24:14,883 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 17:24:14,883 - <PERSON><PERSON><PERSON>ine - INFO - ✅ Ollama backend available
2025-06-18 17:24:14,884 - <PERSON>E<PERSON>ine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 17:24:14,884 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 17:24:14,884 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 17:24:14,885 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 17:24:14,885 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 17:24:15,146 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 17:24:15,147 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 17:24:15,147 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 17:24:15,147 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 17:24:15,150 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 17:24:15,151 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 17:24:15,151 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 17:24:15,260 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 17:24:15,317 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 17:24:15,454 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 17:24:15,454 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 17:24:18,464 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 17:24:18,482 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 17:24:18,483 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 17:24:18,483 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 17:24:18,483 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 17:24:18,483 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 17:24:18,484 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 17:24:18,484 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 17:24:18,484 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 17:24:18,484 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 17:24:18,825 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 17:24:18,826 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 17:24:18,826 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 17:24:22,101 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 17:24:25,128 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 17:24:29,329 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 17:24:29,330 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 17:24:29,330 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 17:24:29,330 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 17:24:30,252 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 17:24:30,412 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 17:24:30,586 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 17:24:30,587 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 17:24:30,626 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 17:24:30,628 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 17:24:30,629 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 17:24:30,633 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-18 17:24:30,634 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: Gideon A...
2025-06-18 17:24:30,822 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,822 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,823 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-18 17:24:30,823 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: Ultra-pr...
2025-06-18 17:24:30,824 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,824 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,824 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-18 17:24:30,824 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: Bilingua...
2025-06-18 17:24:30,825 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,825 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,825 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-18 17:24:30,826 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: Voice in...
2025-06-18 17:24:30,826 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,826 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,827 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-18 17:24:30,827 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: Ultra-lo...
2025-06-18 17:24:30,828 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,828 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,828 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-18 17:24:30,829 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: AI Model...
2025-06-18 17:24:30,829 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,829 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,830 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-18 17:24:30,830 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: Advanced...
2025-06-18 17:24:30,830 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,830 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,831 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-18 17:24:30,831 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: Drag & d...
2025-06-18 17:24:30,831 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,831 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,831 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-18 17:24:30,832 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: 🚀 Gideon...
2025-06-18 17:24:30,841 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,841 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,841 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-18 17:24:30,842 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: 💼 Ultra-...
2025-06-18 17:24:30,846 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,847 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,847 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-18 17:24:30,847 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: 🎯 Real-t...
2025-06-18 17:24:30,851 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,851 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,851 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-18 17:24:30,851 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: 🧠 Enterp...
2025-06-18 17:24:30,852 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,852 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,853 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-18 17:24:30,853 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: 💡 Advanc...
2025-06-18 17:24:30,857 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,857 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:30,857 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-18 17:24:30,857 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:30] ⚙️ System: 💬 Say 'G...
2025-06-18 17:24:30,858 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:30,858 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:24:32,868 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-18 17:24:32,869 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:24:32] 🤖 Gideon: Hello! I'...
2025-06-18 17:24:32,871 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:24:32,871 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:33:51,064 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:33:51,074 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat activated
2025-06-18 17:33:54,765 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 17:33:54,765 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 17:33:54,768 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:33:54] ⚙️ System: 💬 Compac...
2025-06-18 17:33:54,862 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:33:54,863 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
2025-06-18 17:33:54,874 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:33:54,875 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:33:57,936 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 17:33:57,936 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 17:33:57,936 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:33:57] ⚙️ System: 💬 Compac...
2025-06-18 17:33:57,939 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:33:57,939 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:14,657 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-18 17:41:14,659 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 17:41:14,665 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 17:41:14,665 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 17:41:14,665 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 17:41:14,667 - ModelManager - INFO - No existing models config found
2025-06-18 17:41:14,667 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 17:41:14,667 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 17:41:14,668 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 17:41:14,669 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 17:41:14,669 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 17:41:14,669 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 17:41:14,669 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 17:41:14,670 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 17:41:14,670 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 17:41:14,670 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 17:41:14,898 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 17:41:14,899 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 17:41:14,899 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 17:41:14,899 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 17:41:14,901 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 17:41:14,901 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 17:41:14,901 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 17:41:14,991 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 17:41:15,048 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 17:41:15,171 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 17:41:15,171 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 17:41:18,168 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 17:41:18,179 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 17:41:18,180 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 17:41:18,180 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 17:41:18,180 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 17:41:18,180 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 17:41:18,180 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 17:41:18,180 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 17:41:18,180 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 17:41:18,180 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 17:41:18,404 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 17:41:18,404 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 17:41:18,404 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 17:41:21,666 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 17:41:24,674 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 17:41:28,880 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 17:41:28,881 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 17:41:28,881 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 17:41:28,881 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 17:41:29,586 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 17:41:29,714 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 17:41:29,888 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 17:41:29,889 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 17:41:29,929 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 17:41:29,933 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 17:41:29,933 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 17:41:29,936 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-18 17:41:29,936 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:29] ⚙️ System: Gideon A...
2025-06-18 17:41:30,172 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,172 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,172 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-18 17:41:30,173 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: Ultra-pr...
2025-06-18 17:41:30,173 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,173 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,173 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-18 17:41:30,173 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: Bilingua...
2025-06-18 17:41:30,174 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,174 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,174 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-18 17:41:30,174 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: Voice in...
2025-06-18 17:41:30,174 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,175 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,175 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-18 17:41:30,175 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: Ultra-lo...
2025-06-18 17:41:30,175 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,176 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,176 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-18 17:41:30,176 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: AI Model...
2025-06-18 17:41:30,176 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,176 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,177 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-18 17:41:30,177 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: Advanced...
2025-06-18 17:41:30,177 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,177 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,177 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-18 17:41:30,178 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: Drag & d...
2025-06-18 17:41:30,178 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,178 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,178 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-18 17:41:30,179 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: 🚀 Gideon...
2025-06-18 17:41:30,187 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,187 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,187 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-18 17:41:30,188 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: 💼 Ultra-...
2025-06-18 17:41:30,191 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,191 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,191 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-18 17:41:30,191 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: 🎯 Real-t...
2025-06-18 17:41:30,195 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,195 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,195 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-18 17:41:30,195 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: 🧠 Enterp...
2025-06-18 17:41:30,196 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,196 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,196 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-18 17:41:30,196 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: 💡 Advanc...
2025-06-18 17:41:30,200 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,200 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:30,200 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-18 17:41:30,200 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:30] ⚙️ System: 💬 Say 'G...
2025-06-18 17:41:30,201 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:30,202 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:41:32,209 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-18 17:41:32,209 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:41:32] 🤖 Gideon: Hello! I'...
2025-06-18 17:41:32,210 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:41:32,210 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:47:47,812 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:47:47,822 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat activated
2025-06-18 17:48:07,450 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 17:48:07,451 - UltraProfessionalInterface - INFO - 🔄 Window restored - compact chat hidden
2025-06-18 17:48:24,259 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:48:24,259 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat activated
2025-06-18 17:48:46,501 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 17:48:46,501 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 17:48:46,505 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:48:46] ⚙️ System: 💬 Compac...
2025-06-18 17:48:46,510 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:48:46,511 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:48:53,052 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:48:53,053 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat activated
2025-06-18 17:48:55,130 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 17:48:55,130 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 17:48:55,130 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:48:55] ⚙️ System: 💬 Compac...
2025-06-18 17:48:55,137 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:48:55,137 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:48:59,767 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:48:59,786 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
2025-06-18 17:49:35,190 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-18 17:49:35,192 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 17:49:35,198 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 17:49:35,198 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 17:49:35,198 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 17:49:35,200 - ModelManager - INFO - No existing models config found
2025-06-18 17:49:35,200 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 17:49:35,201 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 17:49:35,202 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 17:49:35,202 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 17:49:35,202 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 17:49:35,202 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 17:49:35,202 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 17:49:35,203 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 17:49:35,203 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 17:49:35,203 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 17:49:35,448 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 17:49:35,449 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 17:49:35,449 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 17:49:35,450 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 17:49:35,453 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 17:49:35,453 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 17:49:35,453 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 17:49:35,553 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 17:49:35,611 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 17:49:35,731 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 17:49:35,731 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 17:49:38,728 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 17:49:38,740 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 17:49:38,740 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 17:49:38,740 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 17:49:38,740 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 17:49:38,741 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 17:49:38,741 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 17:49:38,741 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 17:49:38,741 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 17:49:38,741 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 17:49:38,982 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 17:49:38,982 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 17:49:38,983 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 17:49:42,302 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 17:49:45,313 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 17:49:49,513 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 17:49:49,514 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 17:49:49,514 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 17:49:49,514 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 17:49:50,223 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 17:49:50,355 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 17:49:50,525 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 17:49:50,525 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 17:49:50,566 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 17:49:50,568 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 17:49:50,568 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 17:49:50,573 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-18 17:49:50,573 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: Gideon A...
2025-06-18 17:49:50,773 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,773 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,773 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-18 17:49:50,773 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: Ultra-pr...
2025-06-18 17:49:50,775 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,775 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,775 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-18 17:49:50,776 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: Bilingua...
2025-06-18 17:49:50,778 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,778 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,778 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-18 17:49:50,778 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: Voice in...
2025-06-18 17:49:50,780 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,780 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,781 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-18 17:49:50,781 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: Ultra-lo...
2025-06-18 17:49:50,783 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,783 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,784 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-18 17:49:50,784 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: AI Model...
2025-06-18 17:49:50,786 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,786 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,786 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-18 17:49:50,786 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: Advanced...
2025-06-18 17:49:50,788 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,788 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,788 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-18 17:49:50,788 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: Drag & d...
2025-06-18 17:49:50,798 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,798 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,798 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-18 17:49:50,798 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: 🚀 Gideon...
2025-06-18 17:49:50,808 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,808 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,809 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-18 17:49:50,809 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: 💼 Ultra-...
2025-06-18 17:49:50,815 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,815 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,815 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-18 17:49:50,815 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: 🎯 Real-t...
2025-06-18 17:49:50,820 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,821 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,821 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-18 17:49:50,821 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: 🧠 Enterp...
2025-06-18 17:49:50,824 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,824 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,824 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-18 17:49:50,825 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: 💡 Advanc...
2025-06-18 17:49:50,831 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,831 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:50,831 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-18 17:49:50,831 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:50] ⚙️ System: 💬 Say 'G...
2025-06-18 17:49:50,835 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:50,835 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:49:52,848 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-18 17:49:52,848 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:49:52] 🤖 Gideon: Hello! I'...
2025-06-18 17:49:52,853 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:49:52,853 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:50:29,206 - GideonCore - INFO - Always listening stopped
2025-06-18 17:50:29,870 - STTEngine - INFO - Stopped continuous listening
2025-06-18 17:51:02,727 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:51:02,733 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat activated
2025-06-18 17:51:15,369 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 17:51:15,370 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 17:51:15,375 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:51:15] ⚙️ System: 💬 Compac...
2025-06-18 17:51:15,397 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:51:15,398 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 17:51:20,167 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 17:51:20,186 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
2025-06-18 17:52:24,132 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 17:52:24,134 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 17:52:24,137 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [17:52:24] ⚙️ System: 💬 Compac...
2025-06-18 17:52:24,147 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 17:52:24,147 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:14,482 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-18 18:33:14,484 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 18:33:14,489 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 18:33:14,489 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 18:33:14,489 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 18:33:14,490 - ModelManager - INFO - No existing models config found
2025-06-18 18:33:14,491 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 18:33:14,491 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 18:33:14,493 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 18:33:14,493 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 18:33:14,493 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 18:33:14,494 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 18:33:14,494 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 18:33:14,495 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 18:33:14,495 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 18:33:14,495 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 18:33:14,718 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 18:33:14,719 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 18:33:14,720 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 18:33:14,720 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 18:33:14,723 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 18:33:14,723 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 18:33:14,724 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 18:33:14,835 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 18:33:14,893 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 18:33:15,020 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 18:33:15,021 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 18:33:18,031 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 18:33:18,048 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 18:33:18,048 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 18:33:18,048 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 18:33:18,049 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 18:33:18,049 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 18:33:18,049 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 18:33:18,049 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 18:33:18,049 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 18:33:18,049 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 18:33:18,275 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 18:33:18,275 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 18:33:18,276 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 18:33:21,550 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 18:33:24,577 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 18:33:28,776 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 18:33:28,777 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 18:33:28,777 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 18:33:28,777 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 18:33:29,455 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 18:33:29,581 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 18:33:29,747 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 18:33:29,747 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 18:33:29,785 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 18:33:29,787 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 18:33:29,787 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 18:33:29,792 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-18 18:33:29,792 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: Gideon A...
2025-06-18 18:33:29,978 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,978 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,978 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-18 18:33:29,978 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: Ultra-pr...
2025-06-18 18:33:29,981 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,981 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,981 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-18 18:33:29,981 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: Bilingua...
2025-06-18 18:33:29,983 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,984 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,984 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-18 18:33:29,984 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: Voice in...
2025-06-18 18:33:29,987 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,987 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,987 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-18 18:33:29,987 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: Ultra-lo...
2025-06-18 18:33:29,990 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,991 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,991 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-18 18:33:29,991 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: AI Model...
2025-06-18 18:33:29,992 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,993 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,993 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-18 18:33:29,993 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: Advanced...
2025-06-18 18:33:29,995 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,995 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,995 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-18 18:33:29,995 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: Drag & d...
2025-06-18 18:33:29,997 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:29,997 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:29,997 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-18 18:33:29,998 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:29] ⚙️ System: 🚀 Gideon...
2025-06-18 18:33:30,012 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:30,012 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:30,013 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-18 18:33:30,013 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:30] ⚙️ System: 💼 Ultra-...
2025-06-18 18:33:30,020 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:30,021 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:30,021 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-18 18:33:30,021 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:30] ⚙️ System: 🎯 Real-t...
2025-06-18 18:33:30,027 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:30,027 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:30,027 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-18 18:33:30,027 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:30] ⚙️ System: 🧠 Enterp...
2025-06-18 18:33:30,030 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:30,030 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:30,030 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-18 18:33:30,031 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:30] ⚙️ System: 💡 Advanc...
2025-06-18 18:33:30,038 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:30,039 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:30,039 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-18 18:33:30,039 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:30] ⚙️ System: 💬 Say 'G...
2025-06-18 18:33:30,042 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:30,042 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:32,050 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-18 18:33:32,051 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:33:32] 🤖 Gideon: Hello! I'...
2025-06-18 18:33:32,054 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:33:32,055 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 18:33:37,029 - AIEngine - INFO - Found Ollama models: ['qwen3:235b', 'gemma3:27b', 'deepseek-coder-v2:236b', 'dolphin-llama3:70b']
2025-06-18 18:34:21,080 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 18:34:21,098 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
2025-06-18 18:34:25,245 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 18:34:25,247 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 18:34:25,249 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [18:34:25] ⚙️ System: 💬 Compac...
2025-06-18 18:34:25,255 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 18:34:25,255 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 19:31:37,104 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant Enterprise Edition
2025-06-18 19:31:48,121 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 19:31:48,127 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 19:31:48,127 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 19:31:48,128 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 19:31:48,129 - ModelManager - INFO - No existing models config found
2025-06-18 19:31:48,130 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 19:31:48,130 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 19:31:48,131 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 19:31:48,131 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 19:31:48,132 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 19:31:48,132 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 19:31:48,132 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 19:31:48,133 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 19:31:48,133 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 19:31:48,133 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 19:31:48,372 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 19:31:48,374 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 19:31:48,374 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 19:31:48,374 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 19:31:48,376 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 19:31:48,376 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 19:31:48,377 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 19:31:48,485 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 19:31:48,540 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 19:31:48,666 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 19:31:48,666 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 19:31:51,676 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 19:31:51,692 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 19:31:51,693 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 19:31:51,693 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 19:31:51,693 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 19:31:51,693 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 19:31:51,694 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 19:31:51,694 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 19:31:51,694 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 19:31:51,694 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 19:31:51,944 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 19:31:51,945 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 19:31:51,945 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 19:31:55,210 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 19:31:58,238 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 19:32:02,446 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 19:32:02,447 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 19:32:02,447 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 19:32:02,447 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 19:32:02,588 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-18 19:32:02,836 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 19:32:02,964 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 19:32:03,153 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 19:32:03,153 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 19:32:03,194 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 19:32:03,196 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 19:32:03,197 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 19:32:03,200 - GideonMain - ERROR - Error during initialization: cannot access free variable '_add_enterprise_welcome_messages' where it is not associated with a value in enclosing scope
2025-06-18 19:32:19,891 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> هلا...
2025-06-18 19:32:20,187 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫ :You 👤 [19:32:19]
...
2025-06-18 19:32:20,188 - UltraProfessionalInterface - INFO - 🔤 Arabic text processed: ﻼﻫ...
2025-06-18 19:32:20,442 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 19:32:20,443 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 19:32:20,864 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-18 19:32:20,897 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:32:20,897 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-18 19:32:20,898 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:32:20,899 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:32:20,899 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:32:20,900 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:32:20,900 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:32:20,901 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:32:20,902 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:32:20,902 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:32:21,301 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:32:21,646 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'أعمل على ذلك......'
2025-06-18 19:32:21,649 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'أعمل على ذلك......'
2025-06-18 19:32:22,768 - UltraProfessionalInterface - ERROR - Error stopping static thinking display: main thread is not in main loop
2025-06-18 19:32:23,853 - UltraProfessionalInterface - ERROR - Error handling text response: main thread is not in main loop
2025-06-18 19:32:23,854 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ❌ Error displaying response: main thread is not in...
2025-06-18 19:32:23,854 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [19:32:23] ⚙️ System: ❌ Error ...
2025-06-18 19:32:24,950 - UltraProfessionalInterface - ERROR - Error inserting CustomTkinter message: main thread is not in main loop
2025-06-18 19:32:27,118 - UltraProfessionalInterface - ERROR - Fallback insertion also failed: main thread is not in main loop
2025-06-18 19:32:28,218 - UltraProfessionalInterface - ERROR - Optimized processing error: main thread is not in main loop
2025-06-18 19:32:28,249 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-18 19:32:28,249 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:32:28,250 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:32:28,250 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:32:28,250 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:32:28,250 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:32:51,122 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 19:32:51,158 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
2025-06-18 19:34:30,111 - AIEngine - INFO - LLM response generated successfully: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:30,118 - AIEngine - INFO - LLM generated successful response: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:30,139 - AIEngine - INFO - Final response generated: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:43,264 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:43,265 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:43,269 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:52,464 - CompactChatManager - INFO - Compact chat window hidden
2025-06-18 19:34:52,467 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Compact chat closed. Main window is still runnin...
2025-06-18 19:34:52,474 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [19:34:52] ⚙️ System: 💬 Compac...
2025-06-18 19:34:52,568 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-18 19:34:52,569 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-18 19:34:53,058 - AIEngine - INFO - LLM response generated successfully: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:53,058 - AIEngine - INFO - LLM generated successful response: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:53,062 - AIEngine - INFO - Final response generated: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:34:53,066 - UltraProfessionalInterface - INFO - 🎯 FALLBACK 1 RESPONSE: 'مرحباً! كيف يمكنني مساعدتك اليوم؟...'
2025-06-18 19:34:53,067 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'مرحباً! كيف يمكنني مساعدتك اليوم؟...'
2025-06-18 19:34:54,163 - UltraProfessionalInterface - ERROR - Error stopping static thinking display: main thread is not in main loop
2025-06-18 19:34:55,250 - UltraProfessionalInterface - ERROR - Error handling text response: main thread is not in main loop
2025-06-18 19:34:55,250 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ❌ Error displaying response: main thread is not in...
2025-06-18 19:34:55,251 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [19:34:55] ⚙️ System: ❌ Error ...
2025-06-18 19:34:56,337 - UltraProfessionalInterface - ERROR - Error inserting CustomTkinter message: main thread is not in main loop
2025-06-18 19:34:58,515 - UltraProfessionalInterface - ERROR - Fallback insertion also failed: main thread is not in main loop
2025-06-18 19:34:59,604 - UltraProfessionalInterface - ERROR - Direct AI processing error: main thread is not in main loop
2025-06-18 19:35:00,699 - GideonCore - ERROR - Error processing text request: main thread is not in main loop
2025-06-18 19:35:01,800 - GideonCore - ERROR - Worker loop error: main thread is not in main loop
2025-06-18 19:39:05,732 - EnterpriseSettings - INFO - Enterprise settings loaded successfully
2025-06-18 19:39:05,775 - EnterpriseDataManager - INFO - Database initialized successfully
2025-06-18 19:39:05,780 - EnterpriseHelpSystem - INFO - Help content files initialized
2025-06-18 19:40:09,761 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant Enterprise Edition
2025-06-18 19:40:22,823 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant Enterprise Edition
2025-06-18 19:40:33,739 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 19:40:33,741 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 19:40:33,742 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 19:40:33,742 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 19:40:33,743 - ModelManager - INFO - No existing models config found
2025-06-18 19:40:33,744 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 19:40:33,744 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 19:40:33,746 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 19:40:33,746 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 19:40:33,747 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 19:40:33,747 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 19:40:33,747 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 19:40:33,748 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 19:40:33,748 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 19:40:33,748 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 19:40:34,008 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 19:40:34,010 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 19:40:34,010 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 19:40:34,010 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 19:40:34,013 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 19:40:34,013 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 19:40:34,014 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 19:40:34,173 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 19:40:34,228 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 19:40:34,358 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 19:40:34,358 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 19:40:37,368 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 19:40:37,385 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 19:40:37,385 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 19:40:37,385 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 19:40:37,386 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 19:40:37,386 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 19:40:37,386 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 19:40:37,386 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 19:40:37,386 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 19:40:37,386 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 19:40:37,654 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 19:40:37,655 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 19:40:37,655 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 19:40:40,943 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 19:40:43,971 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 19:40:48,171 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 19:40:48,172 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 19:40:48,173 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 19:40:48,173 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 19:40:48,304 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-18 19:40:48,546 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 19:40:48,670 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 19:40:48,840 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 19:40:48,841 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 19:40:48,879 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 19:40:48,882 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 19:40:48,882 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 19:40:48,886 - GideonMain - ERROR - Error during initialization: cannot access free variable '_add_enterprise_welcome_messages' where it is not associated with a value in enclosing scope
2025-06-18 19:41:56,808 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-18 19:41:56,812 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:41:56,813 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:41:56,813 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:41:56,813 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:41:56,814 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:43:12,508 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 19:43:12,509 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 19:43:12,509 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 19:43:12,510 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 19:43:12,510 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 19:43:12,511 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 19:43:12,511 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 19:43:12,512 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 19:43:12,512 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 19:43:12,839 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 19:43:12,841 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 19:43:12,841 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 19:43:12,841 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 19:43:12,845 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 19:43:12,845 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 19:43:12,845 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 19:43:12,845 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-18 19:43:12,846 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-18 19:43:12,846 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:43:12,846 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:43:12,846 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:43:12,846 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:43:12,847 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:43:22,955 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:43:22,959 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:43:22,975 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:43:24,099 - UltraProfessionalInterface - ERROR - Error processing message: main thread is not in main loop
2025-06-18 19:43:55,922 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:43:55,928 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:43:55,928 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:43:55,929 - AIEngine - INFO - Generating response for: 'ما اسمك؟'
2025-06-18 19:43:55,929 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:43:55,929 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:43:55,929 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:43:55,930 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:43:55,930 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:51:45,752 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 19:51:45,757 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 19:51:45,757 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 19:51:45,758 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 19:51:45,759 - ModelManager - INFO - No existing models config found
2025-06-18 19:51:45,760 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 19:51:45,760 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 19:51:45,761 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 19:51:45,761 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 19:51:45,762 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 19:51:45,762 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 19:51:45,762 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 19:51:45,763 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 19:51:45,763 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 19:51:45,763 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 19:51:46,007 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 19:51:46,008 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 19:51:46,008 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 19:51:46,008 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 19:51:46,010 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 19:51:46,011 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 19:51:46,011 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 19:51:46,174 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 19:51:46,241 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 19:51:46,393 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 19:51:46,394 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 19:51:49,404 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 19:51:49,420 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 19:51:49,421 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 19:51:49,421 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 19:51:49,421 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 19:51:49,421 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 19:51:49,421 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 19:51:49,421 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 19:51:49,422 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 19:51:49,422 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 19:51:49,663 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 19:51:49,664 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 19:51:49,664 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 19:51:52,993 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 19:51:56,022 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 19:52:00,230 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 19:52:00,230 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 19:52:00,231 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 19:52:00,231 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 19:52:00,234 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-18 19:52:00,235 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-18 19:52:00,235 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:52:00,235 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:52:00,235 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:52:00,235 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:52:00,235 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:54:07,349 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف حالك اليوم؟...
2025-06-18 19:54:07,362 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف حالك اليوم؟...
2025-06-18 19:54:07,383 - AIEngine - INFO - Final response generated: مرحباً، كيف حالك اليوم؟...
2025-06-18 19:54:07,385 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-18 19:54:07,385 - AIEngine - INFO - Generating response for: 'hello'
2025-06-18 19:54:07,387 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:54:07,387 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-18 19:54:07,387 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-18 19:54:07,387 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-18 19:54:07,388 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:54:27,533 - AIEngine - INFO - LLM response generated successfully: Hello! How can I help you today?...
2025-06-18 19:54:27,534 - AIEngine - INFO - LLM generated successful response: Hello! How can I help you today?...
2025-06-18 19:54:27,540 - AIEngine - INFO - Final response generated: Hello! How can I help you today?...
2025-06-18 19:54:27,622 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 19:54:27,623 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 19:54:27,624 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 19:54:27,626 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 19:54:27,626 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 19:54:27,626 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 19:54:27,628 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 19:54:27,628 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 19:54:27,628 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 19:54:27,924 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 19:54:27,930 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 19:54:27,930 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 19:54:27,930 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 19:54:27,932 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 19:54:27,932 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 19:54:27,932 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 19:54:27,932 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-18 19:54:27,933 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-18 19:54:27,933 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:54:27,933 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:54:27,933 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:54:27,933 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:54:27,934 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:54:43,926 - AIEngine - INFO - LLM response generated successfully: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:54:43,931 - AIEngine - INFO - LLM generated successful response: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:54:43,933 - AIEngine - INFO - Final response generated: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:54:43,934 - AIEngine - INFO - Generating response for: 'ما اسمك؟'
2025-06-18 19:54:43,934 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:54:43,934 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:54:43,935 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:54:43,935 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:54:43,935 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:54:56,296 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant Enterprise Edition
2025-06-18 19:55:07,415 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 19:55:07,424 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 19:55:07,425 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 19:55:07,425 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 19:55:07,427 - ModelManager - INFO - No existing models config found
2025-06-18 19:55:07,428 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 19:55:07,428 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 19:55:07,429 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 19:55:07,430 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 19:55:07,431 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 19:55:07,431 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 19:55:07,431 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 19:55:07,432 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 19:55:07,432 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 19:55:07,433 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 19:55:07,841 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 19:55:07,843 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 19:55:07,843 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 19:55:07,844 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 19:55:07,848 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 19:55:07,849 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 19:55:07,849 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 19:55:08,029 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 19:55:08,094 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 19:55:08,229 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 19:55:08,229 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 19:55:11,226 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 19:55:11,238 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 19:55:11,239 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 19:55:11,239 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 19:55:11,239 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 19:55:11,240 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 19:55:11,240 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 19:55:11,240 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 19:55:11,240 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 19:55:11,241 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 19:55:11,549 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 19:55:11,550 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 19:55:11,550 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 19:55:14,867 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 19:55:17,879 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 19:55:22,096 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 19:55:22,097 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 19:55:22,099 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 19:55:22,099 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 19:55:22,627 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-18 19:55:22,903 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 19:55:23,029 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 19:55:23,204 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 19:55:23,204 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 19:55:23,244 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 19:55:23,246 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 19:55:23,246 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 19:55:23,250 - GideonMain - ERROR - Error during initialization: cannot access free variable '_add_enterprise_welcome_messages' where it is not associated with a value in enclosing scope
2025-06-18 19:55:29,685 - UltraProfessionalInterface - INFO - 🌍 Detected input language: ar
2025-06-18 19:55:29,686 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-18 19:55:29,686 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-18 19:55:29,687 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:55:29,688 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:55:29,688 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:55:29,688 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:55:29,689 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:55:34,411 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 19:55:34,435 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
2025-06-18 19:56:00,941 - AIEngine - INFO - LLM response generated successfully: اسمي جيديون، وأنا مساعد ذكي هنا لمساعدتك. تمنع است...
2025-06-18 19:56:00,947 - AIEngine - INFO - LLM generated successful response: اسمي جيديون، وأنا مساعد ذكي هنا لمساعدتك. تمنع است...
2025-06-18 19:56:00,948 - AIEngine - INFO - Final response generated: اسمي جيديون، وأنا مساعد ذكي هنا لمساعدتك. تمنع است...
2025-06-18 19:56:00,949 - AIEngine - INFO - Generating response for: 'كيف حالك؟'
2025-06-18 19:56:00,949 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:56:00,950 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:56:00,950 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:56:00,950 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:56:00,950 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:56:26,799 - AIEngine - INFO - LLM response generated successfully: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:56:26,801 - AIEngine - INFO - LLM generated successful response: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:56:26,812 - AIEngine - INFO - Final response generated: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:56:26,813 - UltraProfessionalInterface - INFO - 🤖 AI response generated: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:56:27,927 - UltraProfessionalInterface - ERROR - Error processing message: main thread is not in main loop
2025-06-18 19:57:36,033 - AIEngine - INFO - LLM response generated successfully: أنا بخير، الحمد لله! شكرًا على سؤالك. إذا كان لديك...
2025-06-18 19:57:36,047 - AIEngine - INFO - LLM generated successful response: أنا بخير، الحمد لله! شكرًا على سؤالك. إذا كان لديك...
2025-06-18 19:57:36,048 - AIEngine - INFO - Final response generated: أنا بخير، الحمد لله! شكرًا على سؤالك. إذا كان لديك...
2025-06-18 19:57:36,049 - AIEngine - INFO - Generating response for: 'شكراً لك'
2025-06-18 19:57:36,049 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:57:36,049 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-18 19:57:36,049 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:57:36,050 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:57:36,050 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:58:18,924 - AIEngine - INFO - LLM response generated successfully: شكرًا لك! سعدني أنني قد ساعدتك. إذا كان لديك أي أس...
2025-06-18 19:58:18,932 - AIEngine - INFO - LLM generated successful response: شكرًا لك! سعدني أنني قد ساعدتك. إذا كان لديك أي أس...
2025-06-18 19:58:18,932 - AIEngine - INFO - Final response generated: شكرًا لك! سعدني أنني قد ساعدتك. إذا كان لديك أي أس...
2025-06-18 19:58:35,350 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 19:58:35,357 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 19:58:35,357 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 19:58:35,357 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 19:58:35,359 - ModelManager - INFO - No existing models config found
2025-06-18 19:58:35,360 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 19:58:35,360 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 19:58:35,361 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 19:58:35,361 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 19:58:35,362 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 19:58:35,362 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 19:58:35,362 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 19:58:35,363 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 19:58:35,363 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 19:58:35,363 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 19:58:35,623 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 19:58:35,625 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 19:58:35,625 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 19:58:35,625 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 19:58:35,629 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 19:58:35,629 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 19:58:35,629 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 19:58:35,784 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 19:58:35,869 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 19:58:36,053 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 19:58:36,053 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 19:58:39,064 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 19:58:39,083 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 19:58:39,083 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 19:58:39,083 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 19:58:39,084 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 19:58:39,084 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 19:58:39,084 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 19:58:39,084 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 19:58:39,084 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 19:58:39,084 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 19:58:39,356 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 19:58:39,356 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 19:58:39,356 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 19:58:42,688 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 19:58:45,716 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 19:58:49,920 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 19:58:49,921 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 19:58:49,921 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 19:58:49,921 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 19:58:49,921 - AIEngine - INFO - Generating response for: 'hello'
2025-06-18 19:58:49,922 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:58:49,922 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-18 19:58:49,923 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-18 19:58:49,923 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-18 19:58:49,923 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:59:31,180 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:59:31,183 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:59:31,193 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-18 19:59:31,204 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-18 19:59:31,204 - AIEngine - INFO - Generating response for: 'hello'
2025-06-18 19:59:31,205 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 19:59:31,205 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-18 19:59:31,205 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-18 19:59:31,205 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-18 19:59:31,205 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 19:59:41,891 - AIEngine - INFO - LLM response generated successfully: Hello! How can I help you today?...
2025-06-18 19:59:41,892 - AIEngine - INFO - LLM generated successful response: Hello! How can I help you today?...
2025-06-18 19:59:41,895 - AIEngine - INFO - Final response generated: Hello! How can I help you today?...
2025-06-18 19:59:42,055 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-18 19:59:42,547 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 19:59:42,678 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 19:59:42,846 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 19:59:42,846 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 19:59:42,886 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 19:59:42,894 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 19:59:42,894 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 20:01:22,498 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 20:01:22,505 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 20:01:22,505 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 20:01:22,505 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 20:01:22,507 - ModelManager - INFO - No existing models config found
2025-06-18 20:01:22,508 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 20:01:22,508 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 20:01:22,509 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 20:01:22,509 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 20:01:22,509 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 20:01:22,509 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 20:01:22,510 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 20:01:22,510 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 20:01:22,511 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 20:01:22,511 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 20:01:22,792 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 20:01:22,794 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 20:01:22,794 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 20:01:22,794 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 20:01:22,797 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 20:01:22,798 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 20:01:22,798 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 20:01:22,967 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 20:01:23,038 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 20:01:23,196 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 20:01:23,197 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 20:01:26,207 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 20:01:26,226 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 20:01:26,226 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 20:01:26,226 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 20:01:26,226 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 20:01:26,226 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 20:01:26,226 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 20:01:26,227 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 20:01:26,227 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 20:01:26,227 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 20:01:26,484 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 20:01:26,484 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 20:01:26,484 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 20:01:29,811 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 20:01:32,838 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 20:01:37,013 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 20:01:37,013 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 20:01:37,013 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 20:01:37,013 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 20:01:37,216 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-18 20:01:37,788 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 20:01:37,931 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 20:01:38,113 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 20:01:38,114 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 20:01:38,147 - UltraProfessionalInterface - INFO - ✅ Send button created with command binding
2025-06-18 20:01:38,154 - UltraProfessionalInterface - INFO - 🔗 Setting up Enter key binding...
2025-06-18 20:01:38,154 - UltraProfessionalInterface - INFO - ✅ Enter key binding set up successfully
2025-06-18 20:01:38,155 - UltraProfessionalInterface - INFO - ✅ Input entry focused
2025-06-18 20:01:38,156 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 20:01:38,158 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 20:01:38,158 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 20:01:40,192 - UltraProfessionalInterface - INFO - 🔥 _send_message called!
2025-06-18 20:01:40,192 - UltraProfessionalInterface - INFO - 📝 Message retrieved: 'hello test'
2025-06-18 20:01:40,193 - UltraProfessionalInterface - INFO - 💬 Processing message: 'hello test'
2025-06-18 20:01:40,218 - UltraProfessionalInterface - INFO - 🌍 Detected input language: en
2025-06-18 20:01:40,218 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-18 20:01:40,218 - AIEngine - INFO - Generating response for: 'hello test'
2025-06-18 20:01:40,218 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 20:01:40,218 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-18 20:01:40,219 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-18 20:01:40,219 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-18 20:01:40,219 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 21:26:03,825 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant Enterprise Edition
2025-06-18 21:26:14,964 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 21:26:14,970 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 21:26:14,970 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 21:26:14,971 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 21:26:14,972 - ModelManager - INFO - No existing models config found
2025-06-18 21:26:14,973 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 21:26:14,973 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 21:26:14,974 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 21:26:14,975 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 21:26:14,975 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 21:26:14,975 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 21:26:14,976 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 21:26:14,976 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 21:26:14,976 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 21:26:14,977 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 21:26:15,263 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 21:26:15,264 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 21:26:15,265 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 21:26:15,265 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 21:26:15,266 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 21:26:15,266 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 21:26:15,267 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 21:26:15,388 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 21:26:15,445 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 21:26:15,573 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 21:26:15,574 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 21:26:18,584 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 21:26:18,601 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 21:26:18,601 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 21:26:18,601 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 21:26:18,601 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 21:26:18,602 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 21:26:18,602 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 21:26:18,602 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 21:26:18,602 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 21:26:18,603 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 21:26:18,905 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 21:26:18,905 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 21:26:18,905 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 21:26:22,185 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 21:26:25,212 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 21:26:29,424 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 21:26:29,425 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 21:26:29,426 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 21:26:29,426 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 21:26:29,580 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-18 21:26:29,829 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 21:26:29,984 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 21:26:30,193 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 21:26:30,193 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 21:26:30,224 - UltraProfessionalInterface - INFO - ✅ Send button created with command binding
2025-06-18 21:26:30,233 - UltraProfessionalInterface - INFO - 🔗 Setting up Enter key binding...
2025-06-18 21:26:30,233 - UltraProfessionalInterface - INFO - ✅ Enter key binding set up successfully
2025-06-18 21:26:30,234 - UltraProfessionalInterface - INFO - ✅ Input entry focused
2025-06-18 21:26:30,235 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 21:26:30,238 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 21:26:30,238 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 21:26:30,242 - GideonMain - ERROR - Error during initialization: cannot access free variable '_add_enterprise_welcome_messages' where it is not associated with a value in enclosing scope
2025-06-18 21:26:42,192 - UltraProfessionalInterface - INFO - 🔥 _send_message called!
2025-06-18 21:26:42,458 - UltraProfessionalInterface - INFO - 📝 Message retrieved: 'hi'
2025-06-18 21:26:42,461 - UltraProfessionalInterface - INFO - 💬 Processing message: 'hi'
2025-06-18 21:26:43,035 - UltraProfessionalInterface - INFO - 🌍 Detected input language: en
2025-06-18 21:26:43,036 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-18 21:26:43,037 - AIEngine - INFO - Generating response for: 'hi'
2025-06-18 21:26:43,038 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 21:26:43,038 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-18 21:26:43,038 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-18 21:26:43,038 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-18 21:26:43,039 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 21:27:12,770 - CompactChatManager - ERROR - Error creating compact window: can't invoke "bind" command: application has been destroyed
2025-06-18 21:27:12,776 - CompactChatManager - ERROR - Error syncing chat content: 'NoneType' object has no attribute 'delete'
2025-06-18 21:27:12,776 - CompactChatManager - INFO - 💬 Compact chat window shown - chat accessible while minimized
2025-06-18 21:27:12,793 - UltraProfessionalInterface - INFO - 💬 Window minimized - compact chat shown for continued access
2025-06-18 21:28:19,323 - AIEngine - INFO - LLM response generated successfully: Hello! How can I help you today?...
2025-06-18 21:28:19,334 - AIEngine - INFO - LLM generated successful response: Hello! How can I help you today?...
2025-06-18 21:28:19,350 - AIEngine - INFO - Final response generated: Hello! How can I help you today?...
2025-06-18 21:28:19,350 - UltraProfessionalInterface - INFO - 🤖 AI response generated: Hello! How can I help you today?...
2025-06-18 21:28:20,469 - UltraProfessionalInterface - ERROR - Error processing message: main thread is not in main loop
2025-06-18 21:30:45,929 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 21:30:45,934 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 21:30:45,934 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 21:30:45,934 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 21:30:45,936 - ModelManager - INFO - No existing models config found
2025-06-18 21:30:45,937 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 21:30:45,937 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 21:30:45,938 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 21:30:45,938 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 21:30:45,939 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 21:30:45,939 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 21:30:45,940 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 21:30:45,940 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 21:30:45,940 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 21:30:45,940 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 21:30:46,187 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 21:30:46,188 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 21:30:46,188 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 21:30:46,189 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 21:30:46,191 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 21:30:46,192 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 21:30:46,192 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 21:30:46,317 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 21:30:46,379 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 21:30:46,528 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 21:30:46,529 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 21:30:49,529 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 21:30:49,542 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 21:30:49,543 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 21:30:49,543 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 21:30:49,543 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 21:30:49,543 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 21:30:49,544 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 21:30:49,544 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 21:30:49,544 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 21:30:49,544 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 21:30:49,843 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 21:30:49,844 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 21:30:49,844 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 21:30:53,544 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 21:30:56,559 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 21:31:00,778 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 21:31:00,779 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 21:31:00,779 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 21:31:00,779 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 21:31:01,019 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-18 21:31:01,654 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 21:31:01,804 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 21:31:02,004 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 21:31:02,004 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 21:31:02,044 - UltraProfessionalInterface - INFO - ✅ Send button created with command binding
2025-06-18 21:31:02,056 - UltraProfessionalInterface - INFO - 🔗 Setting up Enter key binding...
2025-06-18 21:31:02,056 - UltraProfessionalInterface - INFO - ✅ Enter key binding set up successfully
2025-06-18 21:31:02,056 - UltraProfessionalInterface - INFO - ✅ Input entry focused
2025-06-18 21:31:02,059 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 21:31:02,064 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 21:31:02,064 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 21:31:02,066 - UltraProfessionalInterface - INFO - 🔥 _send_message called!
2025-06-18 21:31:02,066 - UltraProfessionalInterface - INFO - 📝 Message retrieved: 'diagnostic test message'
2025-06-18 21:31:02,067 - UltraProfessionalInterface - INFO - 💬 Processing message: 'diagnostic test message'
2025-06-18 21:31:02,103 - UltraProfessionalInterface - INFO - 🌍 Detected input language: en
2025-06-18 21:31:02,104 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-18 21:31:02,105 - AIEngine - INFO - Generating response for: 'diagnostic test message'
2025-06-18 21:31:02,105 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-18 21:31:02,105 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 21:31:02,105 - AIEngine - INFO - Generating response for: 'hello diagnostic test'
2025-06-18 21:31:02,106 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-18 21:31:02,106 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 21:31:02,106 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-18 21:31:02,107 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-18 21:31:02,107 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-18 21:31:02,107 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-18 21:31:02,107 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-18 21:31:02,108 - AIEngine - INFO - Generating LLM response with max_tokens=100, temperature=0.8
2025-06-18 21:31:02,108 - AIEngine - INFO - Generating LLM response with max_tokens=100, temperature=0.8
2025-06-18 21:32:27,894 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 21:32:27,900 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 21:32:27,901 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 21:32:27,901 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 21:32:27,902 - ModelManager - INFO - No existing models config found
2025-06-18 21:32:27,903 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 21:32:27,903 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 21:32:27,904 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 21:32:27,904 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 21:32:27,905 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 21:32:27,905 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 21:32:27,905 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 21:32:27,906 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 21:32:27,906 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 21:32:27,906 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 21:32:28,149 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 21:32:28,150 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 21:32:28,150 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 21:32:28,150 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 21:32:28,154 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 21:32:28,154 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 21:32:28,154 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 21:32:28,309 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 21:32:28,376 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 21:32:28,521 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 21:32:28,521 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 21:32:31,522 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 21:32:31,536 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 21:32:31,536 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 21:32:31,536 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 21:32:31,536 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 21:32:31,536 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 21:32:31,537 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 21:32:31,537 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 21:32:31,537 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 21:32:31,537 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 21:32:31,796 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 21:32:31,796 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 21:32:31,796 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 21:32:35,120 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 21:32:38,134 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 21:32:42,338 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 21:32:42,339 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 21:32:42,339 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 21:32:42,339 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 21:32:42,593 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-18 21:32:43,277 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 21:32:43,439 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 21:32:43,661 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 21:32:43,661 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 21:32:43,700 - UltraProfessionalInterface - INFO - ✅ Send button created with explicit command binding
2025-06-18 21:32:43,710 - UltraProfessionalInterface - INFO - 🔗 Setting up Enter key binding...
2025-06-18 21:32:43,710 - UltraProfessionalInterface - INFO - ✅ Enter key binding set up successfully
2025-06-18 21:32:43,711 - UltraProfessionalInterface - INFO - ✅ Input entry focused
2025-06-18 21:32:43,711 - UltraProfessionalInterface - INFO - 🔍 Current bindings: None
2025-06-18 21:32:43,713 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 21:32:43,715 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 21:32:43,715 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 21:34:13,897 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 21:34:13,905 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 21:34:13,905 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 21:34:13,905 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 21:34:13,907 - ModelManager - INFO - No existing models config found
2025-06-18 21:34:13,907 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 21:34:13,908 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 21:34:13,909 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 21:34:13,909 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 21:34:13,909 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 21:34:13,909 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 21:34:13,910 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 21:34:13,910 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 21:34:13,911 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 21:34:13,911 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 21:34:14,173 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 21:34:14,175 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 21:34:14,175 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 21:34:14,175 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 21:34:14,178 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 21:34:14,178 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 21:34:14,178 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 21:34:14,326 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 21:34:14,403 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 21:34:14,535 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 21:34:14,535 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 21:34:17,535 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 21:34:17,550 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 21:34:17,550 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 21:34:17,550 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 21:34:17,550 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 21:34:17,550 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 21:34:17,550 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 21:34:17,551 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 21:34:17,551 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 21:34:17,551 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 21:34:17,803 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 21:34:17,803 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 21:34:17,803 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 21:34:21,119 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 21:34:24,132 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 21:34:28,340 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 21:34:28,340 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 21:34:28,341 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 21:34:28,341 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 21:34:28,538 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-18 21:34:29,084 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 21:34:29,220 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 21:34:29,413 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 21:34:29,413 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 21:34:29,457 - UltraProfessionalInterface - INFO - ✅ Send button created with explicit command binding
2025-06-18 21:34:29,466 - UltraProfessionalInterface - INFO - 🔗 Setting up Enter key binding for CustomTkinter...
2025-06-18 21:34:29,467 - UltraProfessionalInterface - INFO - ✅ Enter key bound to CustomTkinter internal widget
2025-06-18 21:34:29,467 - UltraProfessionalInterface - INFO - ✅ Input entry focused
2025-06-18 21:34:29,467 - UltraProfessionalInterface - INFO - ✅ CustomTkinter internal widget binding successful
2025-06-18 21:34:29,470 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 21:34:29,472 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 21:34:29,472 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 21:41:51,791 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 21:41:51,796 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 21:41:51,796 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 21:41:51,796 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 21:41:51,798 - ModelManager - INFO - No existing models config found
2025-06-18 21:41:51,798 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 21:41:51,798 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 21:41:51,799 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 21:41:51,799 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 21:41:51,800 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 21:41:51,800 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 21:41:51,800 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 21:41:51,801 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 21:41:51,801 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 21:41:51,801 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 21:41:52,047 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 21:41:52,048 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 21:41:52,048 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 21:41:52,049 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 21:41:52,050 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 21:41:52,051 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 21:41:52,051 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 21:41:52,162 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 21:41:52,217 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 21:41:52,327 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 21:41:52,327 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 21:41:55,327 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 21:41:55,341 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 21:41:55,341 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 21:41:55,341 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 21:41:55,341 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 21:41:55,341 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 21:41:55,342 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 21:41:55,342 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 21:41:55,342 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 21:41:55,342 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 21:41:55,556 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 21:41:55,556 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 21:41:55,556 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 21:41:58,824 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 21:42:01,838 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 21:42:06,050 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 21:42:06,050 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 21:42:06,051 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 21:42:06,051 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 21:42:06,210 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-18 21:42:06,718 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 21:42:06,845 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 21:42:07,014 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 21:42:07,014 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 21:42:07,034 - UltraProfessionalInterface - INFO - ✅ Input entry created and tested successfully
2025-06-18 21:42:07,045 - UltraProfessionalInterface - INFO - ✅ Send button created with explicit command binding
2025-06-18 21:42:07,053 - UltraProfessionalInterface - INFO - 🔗 Setting up Enter key binding for CustomTkinter...
2025-06-18 21:42:07,053 - UltraProfessionalInterface - INFO - ✅ Enter key bound to CustomTkinter internal widget
2025-06-18 21:42:07,053 - UltraProfessionalInterface - INFO - ✅ Input entry focused
2025-06-18 21:42:07,053 - UltraProfessionalInterface - INFO - ✅ CustomTkinter internal widget binding successful
2025-06-18 21:42:07,055 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 21:42:07,057 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 21:42:07,057 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 21:42:07,058 - UltraProfessionalInterface - INFO - ✅ Message inserted: System
2025-06-18 21:42:07,071 - UltraProfessionalInterface - INFO - ✅ Auto-scroll applied
2025-06-18 21:42:07,094 - UltraProfessionalInterface - INFO - ✅ Display updated successfully
2025-06-18 21:42:07,095 - UltraProfessionalInterface - INFO - ✅ Message successfully displayed: System - 🧪 Test message display functio...
2025-06-18 21:42:07,097 - UltraProfessionalInterface - INFO - ✅ Message inserted: System
2025-06-18 21:42:07,099 - UltraProfessionalInterface - INFO - ✅ Auto-scroll applied
2025-06-18 21:42:07,100 - UltraProfessionalInterface - INFO - ✅ Display updated successfully
2025-06-18 21:42:07,100 - UltraProfessionalInterface - INFO - ✅ Message successfully displayed: System - مرحبا! هذا اختبار للنص العربي...
2025-06-18 21:42:07,100 - UltraProfessionalInterface - INFO - ✅ Message inserted: System
2025-06-18 21:42:07,101 - UltraProfessionalInterface - INFO - ✅ Auto-scroll applied
2025-06-18 21:42:07,101 - UltraProfessionalInterface - INFO - ✅ Display updated successfully
2025-06-18 21:42:07,101 - UltraProfessionalInterface - INFO - ✅ Message successfully displayed: System - Hello! This is an English text...
2025-06-18 21:42:07,101 - UltraProfessionalInterface - INFO - 🖱️ Send button clicked!
2025-06-18 21:42:07,102 - UltraProfessionalInterface - INFO - 🔥 _send_message called!
2025-06-18 21:42:07,102 - UltraProfessionalInterface - INFO - 📝 Message retrieved: 'button click test'
2025-06-18 21:42:07,102 - UltraProfessionalInterface - INFO - ✅ Input field cleared
2025-06-18 21:42:07,102 - UltraProfessionalInterface - INFO - 💬 Processing message: 'button click test'
2025-06-18 21:42:07,102 - UltraProfessionalInterface - INFO - ✅ Message inserted: You
2025-06-18 21:42:07,110 - UltraProfessionalInterface - INFO - ✅ Auto-scroll applied
2025-06-18 21:42:07,110 - UltraProfessionalInterface - INFO - ✅ Display updated successfully
2025-06-18 21:42:07,110 - UltraProfessionalInterface - INFO - ✅ Message successfully displayed: You - button click test...
2025-06-18 21:42:07,117 - UltraProfessionalInterface - INFO - 🌍 Detected input language: en
2025-06-18 21:42:07,117 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-18 21:42:07,117 - AIEngine - INFO - Generating response for: 'button click test'
2025-06-18 21:42:07,117 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 21:42:07,117 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-18 21:42:07,118 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-18 21:42:07,118 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-18 21:42:07,118 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 21:42:07,218 - AIEngine - INFO - Generating response for: 'Hello, how are you?'
2025-06-18 21:42:07,218 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 21:42:07,219 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-18 21:42:07,219 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-18 21:42:07,219 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-18 21:42:07,219 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 21:44:00,241 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant Enterprise Edition
2025-06-18 21:44:11,311 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-18 21:44:11,319 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-18 21:44:11,319 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-18 21:44:11,320 - MemorySystem - INFO - Memory system initialized successfully
2025-06-18 21:44:11,322 - ModelManager - INFO - No existing models config found
2025-06-18 21:44:11,323 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-18 21:44:11,323 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-18 21:44:11,324 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-18 21:44:11,324 - AIEngine - INFO - Initializing LLM backends...
2025-06-18 21:44:11,325 - AIEngine - INFO - ✅ Ollama backend available
2025-06-18 21:44:11,325 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-18 21:44:11,325 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-18 21:44:11,326 - AIEngine - INFO - ✅ Transformers backend available
2025-06-18 21:44:11,326 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-18 21:44:11,326 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-18 21:44:11,575 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-18 21:44:11,576 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-18 21:44:11,577 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-18 21:44:11,577 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-18 21:44:11,580 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-18 21:44:11,580 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-18 21:44:11,581 - AIEngine - INFO - AI Engine initialized successfully
2025-06-18 21:44:11,785 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-18 21:44:11,845 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-18 21:44:11,998 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-18 21:44:11,999 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-18 21:44:15,010 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-18 21:44:15,029 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-18 21:44:15,030 - STTEngine - INFO -    Energy threshold: 150
2025-06-18 21:44:15,030 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-18 21:44:15,030 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-18 21:44:15,031 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-18 21:44:15,031 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-18 21:44:15,032 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-18 21:44:15,032 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-18 21:44:15,032 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-18 21:44:15,327 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-18 21:44:15,328 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-18 21:44:15,328 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-18 21:44:18,651 - STTEngine - INFO - Testing microphone... Say something!
2025-06-18 21:44:21,680 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-18 21:44:25,900 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 21:44:25,903 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-18 21:44:25,904 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-18 21:44:25,904 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-18 21:44:26,089 - UltraProfessionalInterface - INFO - Detected DPI scaling: 1.00x
2025-06-18 21:44:26,406 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-18 21:44:26,562 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-18 21:44:26,778 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-18 21:44:26,779 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-18 21:44:26,805 - UltraProfessionalInterface - INFO - ✅ Input entry created and tested successfully
2025-06-18 21:44:26,820 - UltraProfessionalInterface - INFO - ✅ Send button created with explicit command binding
2025-06-18 21:44:26,828 - UltraProfessionalInterface - INFO - 🔗 Setting up Enter key binding for CustomTkinter...
2025-06-18 21:44:26,829 - UltraProfessionalInterface - INFO - ✅ Enter key bound to CustomTkinter internal widget
2025-06-18 21:44:26,829 - UltraProfessionalInterface - INFO - ✅ Input entry focused
2025-06-18 21:44:26,829 - UltraProfessionalInterface - INFO - ✅ CustomTkinter internal widget binding successful
2025-06-18 21:44:26,831 - UltraProfessionalInterface - INFO - ✅ Static interface initialized - chat box permanently visible
2025-06-18 21:44:26,833 - CompactChatManager - INFO - Compact chat manager initialized
2025-06-18 21:44:26,834 - UltraProfessionalInterface - INFO - ✅ Compact chat initialized - chat accessible when minimized
2025-06-18 21:44:26,837 - GideonMain - ERROR - Error during initialization: cannot access free variable '_add_enterprise_welcome_messages' where it is not associated with a value in enclosing scope
2025-06-18 21:44:33,115 - UltraProfessionalInterface - INFO - ⌨️ Enter key pressed!
2025-06-18 21:44:33,115 - UltraProfessionalInterface - INFO - 🔥 _send_message called!
2025-06-18 21:44:33,116 - UltraProfessionalInterface - INFO - 📝 Message retrieved: 'd\'
2025-06-18 21:44:33,116 - UltraProfessionalInterface - INFO - ✅ Input field cleared
2025-06-18 21:44:33,116 - UltraProfessionalInterface - INFO - 💬 Processing message: 'd\'
2025-06-18 21:44:33,117 - UltraProfessionalInterface - INFO - ✅ Message inserted: You
2025-06-18 21:44:33,125 - UltraProfessionalInterface - INFO - ✅ Auto-scroll applied
2025-06-18 21:44:33,128 - UltraProfessionalInterface - INFO - ✅ Display updated successfully
2025-06-18 21:44:33,128 - UltraProfessionalInterface - INFO - ✅ Message successfully displayed: You - d\...
2025-06-18 21:44:33,140 - UltraProfessionalInterface - INFO - 🌍 Detected input language: en
2025-06-18 21:44:33,140 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-18 21:44:33,140 - AIEngine - INFO - Generating response for: 'd\'
2025-06-18 21:44:33,141 - AIEngine - INFO - Attempting LLM response generation...
2025-06-18 21:44:33,141 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-18 21:44:33,142 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-18 21:44:33,142 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-18 21:44:33,142 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-18 21:44:46,426 - AIEngine - INFO - LLM response generated successfully: Hello there! How can I help you today?...
2025-06-18 21:44:46,427 - AIEngine - INFO - LLM generated successful response: Hello there! How can I help you today?...
2025-06-18 21:44:46,435 - AIEngine - INFO - Final response generated: Hello there! How can I help you today?...
2025-06-18 21:44:46,435 - UltraProfessionalInterface - INFO - 🤖 AI response generated: Hello there! How can I help you today?...
2025-06-18 21:44:47,519 - UltraProfessionalInterface - ERROR - Error processing message: main thread is not in main loop
