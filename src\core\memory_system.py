"""
Memory System for Gideon AI Assistant
Handles conversation history, user preferences, and learning data
"""

import sqlite3
import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

from src.utils.logger import Gideon<PERSON>ogger


class MemorySystem:
    """Memory management system for <PERSON>"""
    
    def __init__(self):
        self.logger = GideonLogger("MemorySystem")
        self.db_path = "data/memory/gideon_memory.db"
        self.connection = None
        
        # In-memory caches
        self.recent_conversations = []
        self.user_preferences = {}
        self.learning_data = {}
    
    def initialize(self):
        """Initialize the memory system"""
        try:
            # Ensure directory exists
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Connect to database
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row
            
            # Create tables
            self._create_tables()
            
            # Load recent data into memory
            self._load_recent_data()
            
            self.logger.info("Memory system initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize memory system: {e}")
            raise
    
    def _create_tables(self):
        """Create database tables"""
        cursor = self.connection.cursor()
        
        # Conversations table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                user_input TEXT NOT NULL,
                ai_response TEXT NOT NULL,
                context TEXT,
                sentiment REAL,
                language TEXT DEFAULT 'en'
            )
        ''')
        
        # User preferences table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_preferences (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        ''')
        
        # Learning data table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS learning_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category TEXT NOT NULL,
                data TEXT NOT NULL,
                confidence REAL DEFAULT 0.5,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        ''')
        
        # User behavior patterns
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS behavior_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pattern_type TEXT NOT NULL,
                pattern_data TEXT NOT NULL,
                frequency INTEGER DEFAULT 1,
                last_seen TEXT NOT NULL
            )
        ''')
        
        self.connection.commit()
    
    def _load_recent_data(self):
        """Load recent data into memory for faster access"""
        try:
            cursor = self.connection.cursor()
            
            # Load recent conversations (last 50)
            cursor.execute('''
                SELECT * FROM conversations 
                ORDER BY timestamp DESC 
                LIMIT 50
            ''')
            self.recent_conversations = [dict(row) for row in cursor.fetchall()]
            
            # Load user preferences
            cursor.execute('SELECT key, value FROM user_preferences')
            for row in cursor.fetchall():
                try:
                    self.user_preferences[row['key']] = json.loads(row['value'])
                except json.JSONDecodeError:
                    self.user_preferences[row['key']] = row['value']
            
            self.logger.info(f"Loaded {len(self.recent_conversations)} recent conversations")
            self.logger.info(f"Loaded {len(self.user_preferences)} user preferences")
            
        except Exception as e:
            self.logger.error(f"Error loading recent data: {e}")
    
    def add_conversation(self, user_input: str, ai_response: str, context: str = None, language: str = "en"):
        """Add a conversation to memory"""
        try:
            timestamp = datetime.now().isoformat()
            
            # Calculate basic sentiment (simple keyword-based)
            sentiment = self._calculate_sentiment(user_input)
            
            cursor = self.connection.cursor()
            cursor.execute('''
                INSERT INTO conversations (timestamp, user_input, ai_response, context, sentiment, language)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (timestamp, user_input, ai_response, context, sentiment, language))
            
            conversation_id = cursor.lastrowid
            self.connection.commit()
            
            # Add to recent conversations cache
            conversation = {
                'id': conversation_id,
                'timestamp': timestamp,
                'user_input': user_input,
                'ai_response': ai_response,
                'context': context,
                'sentiment': sentiment,
                'language': language
            }
            
            self.recent_conversations.insert(0, conversation)
            
            # Keep only recent 50 conversations in memory
            if len(self.recent_conversations) > 50:
                self.recent_conversations = self.recent_conversations[:50]
            
            self.logger.debug(f"Added conversation to memory: {user_input[:50]}...")
            
        except Exception as e:
            self.logger.error(f"Error adding conversation: {e}")
    
    def get_conversation_history(self, limit: int = 10, language: str = None) -> List[Dict[str, Any]]:
        """Get conversation history"""
        try:
            cursor = self.connection.cursor()
            
            if language:
                cursor.execute('''
                    SELECT * FROM conversations 
                    WHERE language = ?
                    ORDER BY timestamp DESC 
                    LIMIT ?
                ''', (language, limit))
            else:
                cursor.execute('''
                    SELECT * FROM conversations 
                    ORDER BY timestamp DESC 
                    LIMIT ?
                ''', (limit,))
            
            return [dict(row) for row in cursor.fetchall()]
            
        except Exception as e:
            self.logger.error(f"Error getting conversation history: {e}")
            return []
    
    def get_recent_context(self, limit: int = 5) -> str:
        """Get recent conversation context for AI"""
        try:
            recent = self.recent_conversations[:limit]
            context_parts = []
            
            for conv in reversed(recent):  # Reverse to get chronological order
                context_parts.append(f"User: {conv['user_input']}")
                context_parts.append(f"AI: {conv['ai_response']}")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            self.logger.error(f"Error getting recent context: {e}")
            return ""
    
    def set_user_preference(self, key: str, value: Any):
        """Set user preference"""
        try:
            timestamp = datetime.now().isoformat()
            value_json = json.dumps(value)
            
            cursor = self.connection.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO user_preferences (key, value, updated_at)
                VALUES (?, ?, ?)
            ''', (key, value_json, timestamp))
            
            self.connection.commit()
            self.user_preferences[key] = value
            
            self.logger.debug(f"Set user preference: {key} = {value}")
            
        except Exception as e:
            self.logger.error(f"Error setting user preference: {e}")
    
    def get_user_preference(self, key: str, default: Any = None) -> Any:
        """Get user preference"""
        return self.user_preferences.get(key, default)
    
    def add_learning_data(self, category: str, data: Any, confidence: float = 0.5):
        """Add learning data"""
        try:
            timestamp = datetime.now().isoformat()
            data_json = json.dumps(data)
            
            cursor = self.connection.cursor()
            cursor.execute('''
                INSERT INTO learning_data (category, data, confidence, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (category, data_json, confidence, timestamp, timestamp))
            
            self.connection.commit()
            
            self.logger.debug(f"Added learning data: {category}")
            
        except Exception as e:
            self.logger.error(f"Error adding learning data: {e}")
    
    def get_learning_data(self, category: str) -> List[Dict[str, Any]]:
        """Get learning data by category"""
        try:
            cursor = self.connection.cursor()
            cursor.execute('''
                SELECT * FROM learning_data 
                WHERE category = ?
                ORDER BY confidence DESC, updated_at DESC
            ''', (category,))
            
            results = []
            for row in cursor.fetchall():
                try:
                    data = json.loads(row['data'])
                    results.append({
                        'id': row['id'],
                        'data': data,
                        'confidence': row['confidence'],
                        'created_at': row['created_at'],
                        'updated_at': row['updated_at']
                    })
                except json.JSONDecodeError:
                    continue
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error getting learning data: {e}")
            return []
    
    def record_behavior_pattern(self, pattern_type: str, pattern_data: Dict[str, Any]):
        """Record user behavior pattern"""
        try:
            timestamp = datetime.now().isoformat()
            data_json = json.dumps(pattern_data)
            
            cursor = self.connection.cursor()
            
            # Check if pattern already exists
            cursor.execute('''
                SELECT id, frequency FROM behavior_patterns 
                WHERE pattern_type = ? AND pattern_data = ?
            ''', (pattern_type, data_json))
            
            existing = cursor.fetchone()
            
            if existing:
                # Update frequency
                cursor.execute('''
                    UPDATE behavior_patterns 
                    SET frequency = frequency + 1, last_seen = ?
                    WHERE id = ?
                ''', (timestamp, existing['id']))
            else:
                # Insert new pattern
                cursor.execute('''
                    INSERT INTO behavior_patterns (pattern_type, pattern_data, frequency, last_seen)
                    VALUES (?, ?, 1, ?)
                ''', (pattern_type, data_json, timestamp))
            
            self.connection.commit()
            
        except Exception as e:
            self.logger.error(f"Error recording behavior pattern: {e}")
    
    def _calculate_sentiment(self, text: str) -> float:
        """Simple sentiment analysis"""
        positive_words = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like', 'happy', 'pleased']
        negative_words = ['bad', 'terrible', 'awful', 'horrible', 'hate', 'dislike', 'sad', 'angry', 'frustrated', 'disappointed']
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count + negative_count == 0:
            return 0.5  # Neutral
        
        return positive_count / (positive_count + negative_count)
    
    def cleanup_old_data(self, days: int = 30):
        """Clean up old data"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            cursor = self.connection.cursor()
            
            # Delete old conversations
            cursor.execute('DELETE FROM conversations WHERE timestamp < ?', (cutoff_date,))
            deleted_conversations = cursor.rowcount
            
            # Delete old learning data with low confidence
            cursor.execute('''
                DELETE FROM learning_data 
                WHERE created_at < ? AND confidence < 0.3
            ''', (cutoff_date,))
            deleted_learning = cursor.rowcount
            
            self.connection.commit()
            
            self.logger.info(f"Cleaned up {deleted_conversations} old conversations and {deleted_learning} low-confidence learning data")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {e}")
    
    def close(self):
        """Close the memory system"""
        if self.connection:
            self.connection.close()
            self.logger.info("Memory system closed")
