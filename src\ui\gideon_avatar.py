#!/usr/bin/env python3
"""
Gideon AI Avatar System - Flash TV Series Inspired
Professional animated face with state indicators
"""

import tkinter as tk
import customtkinter as ctk
import math
import time
import threading
from typing import Dict, Any, Optional, Callable
from PIL import Image, ImageDraw, ImageTk
import numpy as np

class GideonAvatar:
    """Flash TV series inspired Gideon AI avatar with professional animations"""
    
    def __init__(self, parent_frame, size: int = 200):
        self.parent = parent_frame
        self.size = size
        self.canvas_size = size + 40  # Extra space for glow effects

        # STATIC STATE - NO ANIMATIONS
        self.current_state = "idle"
        self.animation_running = False  # PERMANENTLY DISABLED
        self.animation_thread = None

        # Static parameters (no animation phases)
        self.pulse_phase = 0  # STATIC
        self.wave_phase = 0   # STATIC
        self.particle_phase = 0  # STATIC
        
        # Colors (Flash-inspired blue/cyan theme)
        self.colors = {
            'primary': '#00D4FF',      # Bright cyan
            'secondary': '#0099CC',    # Darker cyan
            'accent': '#66E6FF',       # <PERSON> cyan
            'glow': '#00FFFF',         # <PERSON>an glow
            'background': '#0A0E13',   # Dark background
            'inactive': '#334155',     # Inactive state
            'speaking': '#FF6B35',     # Orange for speaking
            'thinking': '#9333EA',     # Purple for thinking
            'listening': '#10B981'     # Green for listening
        }
        
        # Create avatar canvas
        self._create_avatar_canvas()

        # NO ANIMATION INITIALIZATION - Static avatar only
        # self._start_animation()  # DISABLED
    
    def _create_avatar_canvas(self):
        """Create the avatar canvas with professional styling"""
        # Container frame
        self.avatar_container = ctk.CTkFrame(
            self.parent,
            width=self.canvas_size,
            height=self.canvas_size,
            fg_color="transparent"
        )
        self.avatar_container.pack(pady=20)
        self.avatar_container.pack_propagate(False)
        
        # Canvas for avatar
        self.canvas = tk.Canvas(
            self.avatar_container,
            width=self.canvas_size,
            height=self.canvas_size,
            bg=self.colors['background'],
            highlightthickness=0,
            relief='flat'
        )
        self.canvas.pack()
        
        # Center coordinates
        self.center_x = self.canvas_size // 2
        self.center_y = self.canvas_size // 2
        
        # Draw initial avatar
        self._draw_avatar()
    
    def _draw_avatar(self):
        """Draw the Gideon avatar with current state"""
        # Clear canvas
        self.canvas.delete("all")
        
        # Get state colors
        state_color = self._get_state_color()
        
        # Draw outer glow ring
        self._draw_glow_ring(state_color)
        
        # Draw main face circle
        self._draw_main_face(state_color)
        
        # Draw inner patterns
        self._draw_inner_patterns(state_color)
        
        # Draw state-specific elements
        self._draw_state_elements(state_color)
        
        # Draw center core
        self._draw_center_core(state_color)
    
    def _get_state_color(self) -> str:
        """Get color based on current state"""
        state_colors = {
            'idle': self.colors['primary'],
            'listening': self.colors['listening'],
            'thinking': self.colors['thinking'],
            'speaking': self.colors['speaking'],
            'processing': self.colors['accent']
        }
        return state_colors.get(self.current_state, self.colors['primary'])
    
    def _draw_glow_ring(self, color: str):
        """Draw outer glow ring (STATIC - no pulsing effect)"""
        # STATIC glow ring - NO PULSING ANIMATION
        glow_radius = self.size // 2 + 20  # Fixed size, no animation

        # Static glow layers for depth
        for i in range(3):  # Reduced layers for static display
            radius = glow_radius - (i * 5)

            self.canvas.create_oval(
                self.center_x - radius,
                self.center_y - radius,
                self.center_x + radius,
                self.center_y + radius,
                outline=color,
                width=2,
                stipple="gray25" if i > 0 else ""
            )
    
    def _draw_main_face(self, color: str):
        """Draw main face circle"""
        radius = self.size // 2
        
        # Main circle
        self.canvas.create_oval(
            self.center_x - radius,
            self.center_y - radius,
            self.center_x + radius,
            self.center_y + radius,
            outline=color,
            width=3,
            fill=self.colors['background']
        )
        
        # Inner circle
        inner_radius = radius - 10
        self.canvas.create_oval(
            self.center_x - inner_radius,
            self.center_y - inner_radius,
            self.center_x + inner_radius,
            self.center_y + inner_radius,
            outline=color,
            width=2
        )
    
    def _draw_inner_patterns(self, color: str):
        """Draw inner geometric patterns (STATIC - no rotation/animation)"""
        # STATIC lines - NO ROTATION
        num_lines = 8
        line_length = 30

        for i in range(num_lines):
            angle = (i * 2 * math.pi / num_lines)  # STATIC angle, no wave_phase

            start_x = self.center_x + math.cos(angle) * 20
            start_y = self.center_y + math.sin(angle) * 20
            end_x = self.center_x + math.cos(angle) * (20 + line_length)
            end_y = self.center_y + math.sin(angle) * (20 + line_length)

            self.canvas.create_line(
                start_x, start_y, end_x, end_y,
                fill=color,
                width=2
            )

        # STATIC concentric circles - NO WAVE ANIMATION
        for radius in [40, 60, 80]:
            # NO wave_offset - static circles
            self.canvas.create_oval(
                self.center_x - radius,
                self.center_y - radius,
                self.center_x + radius,
                self.center_y + radius,
                outline=color,
                width=1,
                stipple="gray50"
            )
    
    def _draw_state_elements(self, color: str):
        """Draw state-specific visual elements (STATIC - no animations)"""
        # ALL STATE ANIMATIONS DISABLED - Static display only
        if self.current_state == "listening":
            self._draw_static_listening_indicator(color)
        elif self.current_state == "thinking":
            self._draw_static_thinking_indicator(color)
        elif self.current_state == "speaking":
            self._draw_static_speaking_indicator(color)
        elif self.current_state == "processing":
            self._draw_static_processing_indicator(color)
    
    def _draw_static_listening_indicator(self, color: str):
        """Draw static listening indicator (NO WAVE ANIMATION)"""
        # STATIC listening rings - no animation
        for i in range(3):
            wave_radius = 50 + (i * 20)

            self.canvas.create_oval(
                self.center_x - wave_radius,
                self.center_y - wave_radius,
                self.center_x + wave_radius,
                self.center_y + wave_radius,
                outline=self.colors['listening'],
                width=2
            )
    
    def _draw_static_thinking_indicator(self, color: str):
        """Draw static thinking indicator (NO PARTICLE ANIMATION)"""
        # STATIC thinking dots - no floating animation
        num_particles = 8  # Reduced for static display

        for i in range(num_particles):
            angle = (i * 2 * math.pi / num_particles)  # STATIC angle
            distance = 70  # STATIC distance

            x = self.center_x + math.cos(angle) * distance
            y = self.center_y + math.sin(angle) * distance

            size = 4  # STATIC size

            self.canvas.create_oval(
                x - size, y - size, x + size, y + size,
                fill=self.colors['thinking'],
                outline=""
            )
    
    def _draw_static_speaking_indicator(self, color: str):
        """Draw static speaking indicator (NO WAVE ANIMATION)"""
        # STATIC horizontal audio lines - no wave animation
        for i in range(5):
            y_offset = (i - 2) * 15
            wave_length = 70  # STATIC length

            self.canvas.create_line(
                self.center_x - wave_length,
                self.center_y + y_offset,
                self.center_x + wave_length,
                self.center_y + y_offset,
                fill=self.colors['speaking'],
                width=3
            )
    
    def _draw_static_processing_indicator(self, color: str):
        """Draw static processing indicator (NO SPINNER ANIMATION)"""
        # STATIC processing segments - no spinning animation
        num_segments = 8

        for i in range(num_segments):
            angle = (i * 2 * math.pi / num_segments)  # STATIC angle

            start_radius = 45
            end_radius = 65

            start_x = self.center_x + math.cos(angle) * start_radius
            start_y = self.center_y + math.sin(angle) * start_radius
            end_x = self.center_x + math.cos(angle) * end_radius
            end_y = self.center_y + math.sin(angle) * end_radius

            self.canvas.create_line(
                start_x, start_y, end_x, end_y,
                fill=color,
                width=2  # STATIC width
            )
    
    def _draw_center_core(self, color: str):
        """Draw center core (STATIC - no pulsing effect)"""
        # STATIC center core - NO PULSING
        pulse_size = 10  # Fixed size, no animation

        self.canvas.create_oval(
            self.center_x - pulse_size,
            self.center_y - pulse_size,
            self.center_x + pulse_size,
            self.center_y + pulse_size,
            fill=color,
            outline=""
        )

        # STATIC inner core
        inner_size = 6  # Fixed size
        self.canvas.create_oval(
            self.center_x - inner_size,
            self.center_y - inner_size,
            self.center_x + inner_size,
            self.center_y + inner_size,
            fill=self.colors['background'],
            outline=""
        )
    
    def _start_animation(self):
        """ANIMATION DISABLED - Static avatar only"""
        # ANIMATION COMPLETELY DISABLED
        self.animation_running = False
        # No animation thread created
        # No animation loop started
        pass

    def _animation_loop(self):
        """ANIMATION LOOP DISABLED - No continuous redrawing"""
        # ANIMATION LOOP COMPLETELY DISABLED
        # No while loop, no continuous updates, no redrawing
        pass
    
    def set_state(self, state: str):
        """Set avatar state (STATIC - single redraw only)"""
        if state in ['idle', 'listening', 'thinking', 'speaking', 'processing']:
            self.current_state = state
            # SINGLE STATIC REDRAW - no continuous animation
            self._draw_avatar()
    
    def stop_animation(self):
        """Stop animation (ALREADY DISABLED - no action needed)"""
        # ANIMATION ALREADY DISABLED - no threads to stop
        self.animation_running = False
    
    def destroy(self):
        """Clean up avatar resources"""
        self.stop_animation()
        if hasattr(self, 'avatar_container'):
            self.avatar_container.destroy()


class GideonAvatarManager:
    """Manager for Gideon avatar integration"""
    
    def __init__(self, parent_frame):
        self.avatar = GideonAvatar(parent_frame)
        self.current_state = "idle"
    
    def set_listening(self):
        """Set avatar to listening state"""
        self.current_state = "listening"
        self.avatar.set_state("listening")
    
    def set_thinking(self):
        """Set avatar to thinking state"""
        self.current_state = "thinking"
        self.avatar.set_state("thinking")
    
    def set_speaking(self):
        """Set avatar to speaking state"""
        self.current_state = "speaking"
        self.avatar.set_state("speaking")
    
    def set_processing(self):
        """Set avatar to processing state"""
        self.current_state = "processing"
        self.avatar.set_state("processing")
    
    def set_idle(self):
        """Set avatar to idle state"""
        self.current_state = "idle"
        self.avatar.set_state("idle")
    
    def get_state(self) -> str:
        """Get current avatar state"""
        return self.current_state
    
    def destroy(self):
        """Clean up avatar manager"""
        if self.avatar:
            self.avatar.destroy()
