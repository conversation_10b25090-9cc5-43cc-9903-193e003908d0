#!/usr/bin/env python3
"""
Simple launcher for Gideon AI Assistant Ultra Professional Edition
Redirects to the primary entry point: main_ultra_pro.py
"""

import sys
import subprocess
from pathlib import Path

def main():
    """Launch Gideon AI Assistant Ultra Professional Edition"""
    print("🚀 Launching Gideon AI Assistant - Ultra Professional Edition...")
    
    # Get the current directory
    current_dir = Path(__file__).parent
    main_script = current_dir / "main_ultra_pro.py"
    
    if not main_script.exists():
        print("❌ Error: main_ultra_pro.py not found!")
        print("Please ensure you're running this from the correct directory.")
        return 1
    
    try:
        # Launch the main script
        result = subprocess.run([sys.executable, str(main_script)], 
                              cwd=str(current_dir))
        return result.returncode
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
        return 0
    except Exception as e:
        print(f"❌ Error launching Gideon: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
