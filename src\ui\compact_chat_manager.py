"""
Compact Chat Manager for Gideon AI Assistant
Provides a compact chat interface that remains visible when the main window is minimized
"""

import tkinter as tk
from tkinter import messagebox
import threading
import time
from typing import Optional, Callable
from datetime import datetime

try:
    import customtkinter as ctk
    CUSTOMTKINTER_AVAILABLE = True
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False

from src.utils.logger import <PERSON><PERSON>og<PERSON>
from src.utils.config import Config
from src.utils.text_direction import text_direction_manager


class CompactChatManager:
    """Manages compact chat interface for minimized window access"""
    
    def __init__(self, main_interface, gideon_core=None):
        self.logger = GideonLogger("CompactChatManager")
        self.main_interface = main_interface
        self.gideon_core = gideon_core
        self.config = Config()
        
        # Compact chat window
        self.compact_window = None
        self.compact_visible = False
        self.is_minimized_mode = False
        
        # Chat components
        self.compact_chat_display = None
        self.compact_input_entry = None
        
        # Design system (inherit from main interface)
        if hasattr(main_interface, 'design_system'):
            self.design_system = main_interface.design_system
        else:
            # Fallback design system
            self.design_system = {
                'colors': {
                    'bg_primary': '#1a1a1a',
                    'bg_secondary': '#2d2d2d',
                    'bg_elevated': '#3d3d3d',
                    'text_primary': '#ffffff',
                    'text_secondary': '#b0b0b0',
                    'accent_primary': '#00d4ff',
                    'accent_success': '#10b981',
                    'accent_warning': '#f59e0b',
                    'accent_error': '#ef4444',
                    'border_default': '#4a4a4a'
                },
                'spacing': {'sm': 8, 'md': 16, 'lg': 24, 'xl': 32},
                'radius': {'sm': 4, 'md': 8, 'lg': 12}
            }
        
        self.logger.info("Compact chat manager initialized")
    
    def show_compact_chat(self):
        """Show the compact chat window"""
        try:
            if self.compact_visible and self.compact_window:
                # If already visible, just focus it
                self.compact_window.lift()
                self.compact_window.focus_force()
                return
            
            self._create_compact_window()
            self.compact_visible = True
            self.is_minimized_mode = True
            
            # Sync chat content from main interface
            self._sync_chat_content()
            
            self.logger.info("💬 Compact chat window shown - chat accessible while minimized")
            
        except Exception as e:
            self.logger.error(f"Error showing compact chat: {e}")
    
    def hide_compact_chat(self):
        """Hide the compact chat window"""
        try:
            if self.compact_window:
                self.compact_window.destroy()
                self.compact_window = None
            
            self.compact_visible = False
            self.is_minimized_mode = False
            self.logger.info("Compact chat window hidden")
            
        except Exception as e:
            self.logger.error(f"Error hiding compact chat: {e}")
    
    def _create_compact_window(self):
        """Create the compact chat window"""
        try:
            # Create compact window
            if CUSTOMTKINTER_AVAILABLE:
                self.compact_window = ctk.CTkToplevel()
                self.compact_window.configure(fg_color=self.design_system['colors']['bg_primary'])
            else:
                self.compact_window = tk.Toplevel()
                self.compact_window.configure(bg=self.design_system['colors']['bg_primary'])
            
            # Configure compact window
            self.compact_window.title("💬 Gideon AI - Compact Chat")
            self.compact_window.geometry("350x500")
            self.compact_window.minsize(300, 400)
            self.compact_window.maxsize(500, 700)
            
            # Position window in bottom-right corner
            self._position_compact_window()
            
            # Make it always on top and prevent it from being minimized
            self.compact_window.attributes('-topmost', True)
            self.compact_window.resizable(True, True)
            
            # Handle close event
            self.compact_window.protocol("WM_DELETE_WINDOW", self._on_compact_close)
            
            # Create compact content
            self._create_compact_content()
            
            # Focus the compact window
            self.compact_window.focus_force()
            
        except Exception as e:
            self.logger.error(f"Error creating compact window: {e}")
    
    def _position_compact_window(self):
        """Position the compact window in the bottom-right corner"""
        try:
            # Get screen dimensions
            screen_width = self.compact_window.winfo_screenwidth()
            screen_height = self.compact_window.winfo_screenheight()
            
            # Calculate position (bottom-right with margin)
            window_width = 350
            window_height = 500
            margin = 20
            
            x = screen_width - window_width - margin
            y = screen_height - window_height - margin - 80  # Extra margin for taskbar
            
            self.compact_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
            
        except Exception as e:
            self.logger.error(f"Error positioning compact window: {e}")
    
    def _create_compact_content(self):
        """Create the content for the compact chat window"""
        try:
            # Main container
            if CUSTOMTKINTER_AVAILABLE:
                main_frame = ctk.CTkFrame(self.compact_window, fg_color="transparent")
            else:
                main_frame = tk.Frame(self.compact_window, bg=self.design_system['colors']['bg_primary'])
            
            main_frame.pack(fill="both", expand=True, padx=8, pady=8)
            
            # Header
            self._create_compact_header(main_frame)
            
            # Chat display
            self._create_compact_chat_display(main_frame)
            
            # Input area
            self._create_compact_input_area(main_frame)
            
        except Exception as e:
            self.logger.error(f"Error creating compact content: {e}")
    
    def _create_compact_header(self, parent):
        """Create header for compact chat"""
        try:
            if CUSTOMTKINTER_AVAILABLE:
                header_frame = ctk.CTkFrame(parent, fg_color=self.design_system['colors']['bg_secondary'])
                header_frame.pack(fill="x", pady=(0, 8))
                
                # Title
                title_label = ctk.CTkLabel(
                    header_frame,
                    text="💬 Gideon AI",
                    font=ctk.CTkFont(size=14, weight="bold"),
                    text_color=self.design_system['colors']['text_primary']
                )
                title_label.pack(side="left", padx=10, pady=8)
                
                # Status indicator
                status_label = ctk.CTkLabel(
                    header_frame,
                    text="🟢 Online",
                    font=ctk.CTkFont(size=10),
                    text_color=self.design_system['colors']['accent_success']
                )
                status_label.pack(side="left", padx=(5, 10), pady=8)
                
                # Restore button
                restore_button = ctk.CTkButton(
                    header_frame,
                    text="🔄",
                    width=25,
                    height=25,
                    command=self._restore_main_window,
                    fg_color=self.design_system['colors']['accent_primary'],
                    hover_color=self.design_system['colors']['accent_primary']
                )
                restore_button.pack(side="right", padx=5, pady=4)
                
                # Minimize button (hide compact chat)
                minimize_button = ctk.CTkButton(
                    header_frame,
                    text="➖",
                    width=25,
                    height=25,
                    command=self.hide_compact_chat,
                    fg_color=self.design_system['colors']['accent_warning'],
                    hover_color=self.design_system['colors']['accent_warning']
                )
                minimize_button.pack(side="right", padx=(0, 2), pady=4)
            
        except Exception as e:
            self.logger.error(f"Error creating compact header: {e}")
    
    def _create_compact_chat_display(self, parent):
        """Create chat display for compact window"""
        try:
            if CUSTOMTKINTER_AVAILABLE:
                # Chat container
                chat_container = ctk.CTkFrame(parent, fg_color=self.design_system['colors']['bg_secondary'])
                chat_container.pack(fill="both", expand=True, pady=(0, 8))
                
                # Chat display - PERMANENTLY VISIBLE
                self.compact_chat_display = ctk.CTkTextbox(
                    chat_container,
                    font=ctk.CTkFont(family="Consolas", size=10),
                    fg_color=self.design_system['colors']['bg_primary'],
                    text_color=self.design_system['colors']['text_primary'],
                    wrap="word",
                    corner_radius=self.design_system['radius']['sm'],
                    border_width=1,
                    border_color=self.design_system['colors']['border_default'],
                    state="normal"  # ALWAYS NORMAL - permanently visible
                )
                self.compact_chat_display.pack(fill="both", expand=True, padx=8, pady=8)
                
                # Add welcome message
                welcome_msg = "💬 Compact chat mode active!\nMain window minimized - chat remains accessible."
                self.compact_chat_display.insert("0.0", welcome_msg + "\n\n")
                self.compact_chat_display.see("end")
            
        except Exception as e:
            self.logger.error(f"Error creating compact chat display: {e}")
    
    def _create_compact_input_area(self, parent):
        """Create input area for compact window"""
        try:
            if CUSTOMTKINTER_AVAILABLE:
                # Input container
                input_container = ctk.CTkFrame(parent, fg_color=self.design_system['colors']['bg_secondary'])
                input_container.pack(fill="x")
                
                # Input frame
                input_frame = ctk.CTkFrame(input_container, fg_color="transparent")
                input_frame.pack(fill="x", padx=8, pady=8)
                input_frame.grid_columnconfigure(0, weight=1)
                
                # Input entry
                self.compact_input_entry = ctk.CTkEntry(
                    input_frame,
                    placeholder_text="Type message... (Arabic/English)",
                    font=ctk.CTkFont(size=10),
                    height=30,
                    fg_color=self.design_system['colors']['bg_primary'],
                    text_color=self.design_system['colors']['text_primary'],
                    placeholder_text_color=self.design_system['colors']['text_secondary']
                )
                self.compact_input_entry.grid(row=0, column=0, sticky="ew", padx=(0, 4))
                
                # Send button
                send_button = ctk.CTkButton(
                    input_frame,
                    text="Send",
                    width=50,
                    height=30,
                    command=self._send_compact_message,
                    fg_color=self.design_system['colors']['accent_success'],
                    hover_color=self.design_system['colors']['accent_success']
                )
                send_button.grid(row=0, column=1)
                
                # Bind Enter key
                self.compact_input_entry.bind('<Return>', lambda e: self._send_compact_message())
                
                # Focus input
                self.compact_input_entry.focus_set()
            
        except Exception as e:
            self.logger.error(f"Error creating compact input area: {e}")

    def _send_compact_message(self):
        """Send message from compact chat"""
        try:
            if not hasattr(self, 'compact_input_entry') or not self.compact_input_entry:
                return

            message = self.compact_input_entry.get().strip()
            if not message:
                return

            # Clear input
            self.compact_input_entry.delete(0, 'end')

            # Add message to compact display
            self._add_compact_message("You", message, self.design_system['colors']['accent_primary'])

            # Send to main interface if available
            if self.main_interface and hasattr(self.main_interface, '_add_message'):
                self.main_interface._add_message("You", message, self.main_interface.design_system['colors']['accent_primary'])

            # Process with Gideon core
            if self.gideon_core:
                threading.Thread(target=self._process_compact_message, args=(message,), daemon=True).start()
            else:
                # Fallback response
                response = "I'm here to help! (Main AI engine not available in compact mode)"
                self._add_compact_message("Gideon", response, self.design_system['colors']['accent_success'])

        except Exception as e:
            self.logger.error(f"Error sending compact message: {e}")

    def _process_compact_message(self, message: str):
        """Process message with Gideon core"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'process_text_input'):
                # Process the message
                response = self.gideon_core.process_text_input(message)

                # Add response to compact display
                if response:
                    self._add_compact_message("Gideon", response, self.design_system['colors']['accent_success'])

                    # Also add to main interface if available
                    if self.main_interface and hasattr(self.main_interface, '_add_message'):
                        self.main_interface._add_message("Gideon", response, self.main_interface.design_system['colors']['accent_success'])

        except Exception as e:
            self.logger.error(f"Error processing compact message: {e}")
            error_response = "Sorry, I encountered an error processing your message."
            self._add_compact_message("Gideon", error_response, self.design_system['colors']['accent_error'])

    def _add_compact_message(self, sender: str, message: str, color: str):
        """Add message to compact chat display"""
        try:
            if not hasattr(self, 'compact_chat_display') or not self.compact_chat_display:
                return

            timestamp = datetime.now().strftime("%H:%M")

            # Format message based on sender
            if sender == "You":
                prefix = "👤"
            elif sender == "Gideon":
                prefix = "🤖"
            else:
                prefix = "⚙️"

            # Handle text direction
            formatted_message, direction = text_direction_manager.format_text_for_display(message)

            if direction == "rtl":
                display_text = f"{formatted_message} :{sender} {prefix} [{timestamp}]\n"
            else:
                display_text = f"[{timestamp}] {prefix} {sender}: {formatted_message}\n"

            # Insert message
            self.compact_chat_display.insert("end", display_text)
            self.compact_chat_display.see("end")

        except Exception as e:
            self.logger.error(f"Error adding compact message: {e}")

    def _sync_chat_content(self):
        """Sync chat content from main interface to compact"""
        try:
            if (self.main_interface and
                hasattr(self.main_interface, 'chat_display') and
                hasattr(self, 'compact_chat_display')):

                # Get content from main chat
                main_content = self.main_interface.chat_display.get("0.0", "end")

                # Clear compact display and add synced content
                self.compact_chat_display.delete("0.0", "end")

                # Add compact mode header
                header = "💬 Compact Chat Mode - Synced from Main Interface\n" + "="*50 + "\n\n"
                self.compact_chat_display.insert("0.0", header)

                # Add main content
                self.compact_chat_display.insert("end", main_content)
                self.compact_chat_display.see("end")

                self.logger.debug("Chat content synced to compact window")

        except Exception as e:
            self.logger.error(f"Error syncing chat content: {e}")

    def _restore_main_window(self):
        """Restore main window and hide compact chat"""
        try:
            if self.main_interface and hasattr(self.main_interface, 'root'):
                # Show and focus the main window
                self.main_interface.root.deiconify()
                self.main_interface.root.lift()
                self.main_interface.root.focus_force()

                # Hide compact chat
                self.hide_compact_chat()

                self.logger.info("🔄 Main window restored from compact chat")

        except Exception as e:
            self.logger.error(f"Error restoring main window: {e}")

    def _on_compact_close(self):
        """Handle compact window close"""
        try:
            # Just hide the compact chat, don't exit the application
            self.hide_compact_chat()

            # Show a message that the main window is still running
            if self.main_interface and hasattr(self.main_interface, '_add_message'):
                self.main_interface._add_message(
                    "System",
                    "💬 Compact chat closed. Main window is still running (may be minimized).",
                    self.main_interface.design_system['colors']['accent_warning']
                )

        except Exception as e:
            self.logger.error(f"Error handling compact close: {e}")

    def update_compact_status(self, status: str, color: str = None):
        """Update status in compact chat"""
        try:
            if self.compact_visible and hasattr(self, 'compact_chat_display'):
                if color is None:
                    color = self.design_system['colors']['text_secondary']

                status_msg = f"🔄 Status: {status}"
                self._add_compact_message("System", status_msg, color)

        except Exception as e:
            self.logger.error(f"Error updating compact status: {e}")

    def is_compact_visible(self):
        """Check if compact chat is visible"""
        return self.compact_visible

    def is_in_minimized_mode(self):
        """Check if in minimized mode"""
        return self.is_minimized_mode
