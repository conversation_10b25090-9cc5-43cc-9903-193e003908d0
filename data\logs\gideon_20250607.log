2025-06-07 00:00:34,617 - <PERSON><PERSON>ain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 00:00:34,618 - <PERSON><PERSON><PERSON> - INFO - Initializing Gideon Core System...
2025-06-07 00:00:34,622 - MemorySystem - INFO - Loaded 9 recent conversations
2025-06-07 00:00:34,622 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 00:00:34,623 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 00:00:34,624 - ModelManager - INFO - No existing models config found
2025-06-07 00:00:34,625 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 00:00:34,625 - AIEngine - INFO - Loaded 4 learned response patterns
2025-06-07 00:00:34,625 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 00:00:34,626 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 00:00:34,626 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 00:00:34,626 - <PERSON>E<PERSON>ine - INFO - ⚠️ Transformers backend not available
2025-06-07 00:00:34,626 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 00:00:34,626 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 00:00:34,849 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 00:00:34,851 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 00:00:34,851 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 00:00:34,851 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 00:00:34,955 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 00:00:35,070 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 00:00:35,071 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 00:00:37,069 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 9.029870820794633
2025-06-07 00:00:37,082 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 00:00:37,233 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 00:00:37,233 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 00:00:37,233 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 00:00:40,478 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 00:00:43,106 - STTEngine - INFO - Microphone test successful. Heard: هلا
2025-06-07 00:00:45,227 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 00:00:45,228 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 00:00:45,228 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 00:00:54,390 - STTEngine - ERROR - Error during speech recognition: This audio source is already inside a context manager
2025-06-07 00:00:54,390 - GideonCore - WARNING - No speech detected
2025-06-07 00:00:56,513 - STTEngine - ERROR - Error during speech recognition: This audio source is already inside a context manager
2025-06-07 00:00:56,513 - GideonCore - WARNING - No speech detected
2025-06-07 00:01:00,729 - GideonCore - INFO - Processing text: hi
2025-06-07 00:02:23,155 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 00:02:23,627 - STTEngine - INFO - Stopped continuous listening
2025-06-07 00:02:25,174 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 00:02:25,290 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 00:02:54,403 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 00:02:54,404 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 00:02:54,409 - MemorySystem - INFO - Loaded 9 recent conversations
2025-06-07 00:02:54,409 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 00:02:54,409 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 00:02:54,411 - ModelManager - INFO - No existing models config found
2025-06-07 00:02:54,412 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 00:02:54,412 - AIEngine - INFO - Loaded 4 learned response patterns
2025-06-07 00:02:54,412 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 00:02:54,413 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 00:02:54,413 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 00:02:54,413 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 00:02:54,413 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 00:02:54,413 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 00:02:54,666 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 00:02:54,667 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 00:02:54,668 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 00:02:54,668 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 00:02:54,798 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 00:02:54,919 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 00:02:54,919 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 00:02:56,914 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 00:02:56,928 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 00:02:57,140 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 00:02:57,140 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 00:02:57,140 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 00:03:00,390 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 00:03:02,070 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-07 00:03:06,254 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 00:03:06,255 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 00:03:06,255 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 00:03:12,366 - GideonCore - INFO - Processing text: hi
2025-06-07 00:03:51,403 - GideonCore - INFO - CONVERSATION - User: hi
2025-06-07 00:03:51,404 - GideonCore - INFO - CONVERSATION - AI: مرحبًا! أنا دولفين، مساعدك الذكاء. سأقوم بالإجابة على أسئلتك بوضوح وإيجاز باللغة العربية. ما الذي يمكنني مساعدتك فيه؟
2025-06-07 00:03:51,455 - GideonCore - INFO - Processing text: hello\
2025-06-07 00:04:45,710 - GideonCore - INFO - CONVERSATION - User: hello\
2025-06-07 00:04:45,710 - GideonCore - INFO - CONVERSATION - AI: مرحبًا! لقد ساعدتك في تخصيص الخصوصية، ولكنني لم أفهم request السابق الخاص بك. سنكون سعداء للتأكد من أنك تفهم المادة قبل المتابعة، إذا كان ذلك سيوضح الأسلوب الذي يمكنني استخدامه للتواصل معك بشكل أفضل.
2025-06-07 00:07:50,312 - STTEngine - INFO - 🎤 Heard: 'هاو تو رايت'
2025-06-07 00:08:09,791 - GideonCore - INFO - Processing text: how are u
2025-06-07 00:09:11,732 - GideonCore - INFO - CONVERSATION - User: how are u
2025-06-07 00:09:11,733 - GideonCore - INFO - CONVERSATION - AI: أنا بخير، شكرًا على سؤالك! كما ذكرت من قبل، سأقوم بالإجابة على أسئلتك بوضوح وإيجاز باللغة العربية. إذا كان لديك أي أسئلة أخرى أو potřebت مساعدتي في أي شيء، لا تتردد في التأكيد مني.
2025-06-07 00:10:40,141 - STTEngine - INFO - 🎤 Heard: 'جاسر'
2025-06-07 00:10:53,297 - GideonCore - INFO - Processing text: سلام  عليكم
2025-06-07 00:11:32,398 - GideonCore - INFO - CONVERSATION - User: سلام  عليكم
2025-06-07 00:11:32,398 - GideonCore - INFO - CONVERSATION - AI: وعليكم السلام! سأقوم بالإجابة على أسئلتك بوضوح وإيجاز باللغة العربية. ماذا يمكنني مساعدتك في؟
2025-06-07 00:11:56,241 - GideonCore - INFO - Processing text: كيف حالك؟
2025-06-07 00:11:56,247 - GideonCore - INFO - CONVERSATION - User: كيف حالك؟
2025-06-07 00:11:56,247 - GideonCore - INFO - CONVERSATION - AI: الحمد لله، أنا بخير! وأنت كيف حالك؟
2025-06-07 00:14:13,488 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 00:14:13,712 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 00:14:13,825 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 00:23:40,384 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 00:23:40,388 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 00:23:40,389 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 00:23:40,389 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 00:23:40,390 - ModelManager - INFO - No existing models config found
2025-06-07 00:23:40,391 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 00:23:40,391 - AIEngine - INFO - Loaded 7 learned response patterns
2025-06-07 00:23:40,391 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 00:23:40,392 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 00:23:40,392 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 00:23:40,392 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 00:23:40,392 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 00:23:40,392 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 00:23:40,624 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 00:23:40,625 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 00:23:40,625 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 00:23:40,625 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 00:23:40,728 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 00:23:40,841 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 00:23:40,841 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 00:23:42,832 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 17.271451421836495
2025-06-07 00:23:42,844 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 00:23:43,017 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 00:23:43,017 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 00:23:43,017 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 00:23:46,259 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 00:23:48,581 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-07 00:23:52,762 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 00:23:52,763 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 00:23:52,763 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 00:23:52,778 - GideonCore - INFO - Processing text: What is your name?
2025-06-07 00:23:54,780 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 00:23:55,135 - STTEngine - INFO - Stopped continuous listening
2025-06-07 00:23:56,791 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 00:24:07,115 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 00:24:07,115 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 00:24:07,117 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 00:24:07,117 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 00:24:07,117 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 00:24:07,118 - ModelManager - INFO - No existing models config found
2025-06-07 00:24:07,119 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 00:24:07,119 - AIEngine - INFO - Loaded 7 learned response patterns
2025-06-07 00:24:07,119 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 00:24:07,120 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 00:24:07,120 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 00:24:07,120 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 00:24:07,120 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 00:24:07,120 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 00:24:07,337 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 00:24:07,338 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 00:24:07,339 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 00:24:07,339 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 00:24:07,428 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 00:24:07,543 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 00:24:07,543 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 00:24:09,543 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 00:24:09,555 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 00:24:09,655 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 00:24:09,655 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 00:24:09,655 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 00:24:12,837 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 00:24:15,617 - STTEngine - INFO - Microphone test successful. Heard: هل
2025-06-07 00:24:17,739 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 00:24:17,740 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 00:24:17,740 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 00:25:42,926 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 00:25:55,867 - STTEngine - INFO - 🎤 Heard: 'الو قدي'
2025-06-07 00:26:12,160 - STTEngine - INFO - 🎤 Heard: '‏continuous'
2025-06-07 00:26:20,622 - GideonCore - INFO - Always listening stopped
2025-06-07 00:26:21,578 - STTEngine - INFO - Stopped continuous listening
2025-06-07 00:26:22,006 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 00:26:22,006 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 00:26:27,218 - STTEngine - INFO - 🎤 Heard: 'كريم'
2025-06-07 00:26:40,256 - STTEngine - INFO - 🎤 Heard: 'سلام'
2025-06-07 00:26:59,784 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 00:27:00,249 - STTEngine - INFO - Stopped continuous listening
2025-06-07 00:27:00,301 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 00:27:00,389 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 00:29:44,833 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 00:29:44,833 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 00:29:50,203 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 00:29:50,273 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 00:29:50,273 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 00:29:52,270 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.799435188308592
2025-06-07 00:29:52,283 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 00:29:52,320 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 00:29:56,030 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-07 00:29:56,030 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 00:30:05,815 - STTEngine - INFO - 🎤 Heard: 'green hello'
2025-06-07 00:30:06,863 - STTEngine - INFO - Stopped continuous listening
2025-06-07 00:30:15,901 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 00:30:15,903 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 00:30:15,903 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 00:30:15,903 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 00:30:15,904 - ModelManager - INFO - No existing models config found
2025-06-07 00:30:15,904 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 00:30:15,905 - AIEngine - INFO - Loaded 7 learned response patterns
2025-06-07 00:30:15,905 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 00:30:15,905 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 00:30:15,905 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 00:30:15,906 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 00:30:15,906 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 00:30:15,906 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 00:30:16,124 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 00:30:16,125 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 00:30:16,125 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 00:30:16,125 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 00:30:16,165 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 00:30:16,234 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 00:30:16,235 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 00:30:18,231 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 8.616996521210783
2025-06-07 00:30:18,243 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 00:30:18,245 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 00:30:18,245 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 00:30:18,245 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 00:30:21,409 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 00:30:24,413 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 00:30:28,598 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 00:30:28,598 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 00:30:28,598 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 00:30:28,598 - GideonCore - INFO - Always listening already active
2025-06-07 00:30:43,601 - GideonCore - INFO - Always listening stopped
2025-06-07 00:30:43,602 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 00:30:43,748 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 00:31:21,344 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 00:31:21,345 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 00:31:21,346 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 00:31:21,346 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 00:31:21,346 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 00:31:21,347 - ModelManager - INFO - No existing models config found
2025-06-07 00:31:21,348 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 00:31:21,349 - AIEngine - INFO - Loaded 7 learned response patterns
2025-06-07 00:31:21,349 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 00:31:21,349 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 00:31:21,349 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 00:31:21,350 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 00:31:21,350 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 00:31:21,350 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 00:31:21,573 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 00:31:21,574 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 00:31:21,574 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 00:31:21,574 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 00:31:21,667 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 00:31:21,781 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 00:31:21,781 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 00:31:23,773 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 97.08550658438992
2025-06-07 00:31:23,785 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 00:31:23,886 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 00:31:23,886 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 00:31:23,886 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 00:31:27,069 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 00:31:28,794 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-07 00:31:32,979 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 00:31:32,980 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 00:31:32,980 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 00:36:08,166 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 00:36:08,173 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 00:36:08,173 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 00:36:08,173 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 00:36:08,174 - ModelManager - INFO - No existing models config found
2025-06-07 00:36:08,175 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 00:36:08,175 - AIEngine - INFO - Loaded 9 learned response patterns
2025-06-07 00:36:08,175 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 00:36:08,176 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 00:36:08,176 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 00:36:08,176 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 00:36:08,176 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 00:36:08,176 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 00:36:08,439 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 00:36:08,441 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 00:36:08,441 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 00:36:08,441 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 00:36:08,551 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 00:36:08,664 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 00:36:08,664 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 00:36:10,660 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.808821580790303
2025-06-07 00:36:10,671 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 00:36:10,860 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 00:36:10,860 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 00:36:10,860 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 00:36:14,102 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 00:36:17,111 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 00:36:21,295 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 00:36:21,295 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 00:36:21,295 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 00:38:03,417 - GideonCore - INFO - Always listening already active
2025-06-07 00:38:18,006 - GideonCore - INFO - Always listening stopped
2025-06-07 00:38:19,054 - STTEngine - INFO - Stopped continuous listening
2025-06-07 00:38:22,935 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 00:38:23,819 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 00:38:23,925 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 00:41:25,204 - STTEngine - INFO - 🎤 Heard: 'sex'
2025-06-07 00:41:35,998 - STTEngine - INFO - 🎤 Heard: 'han han'
2025-06-07 00:41:56,179 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 00:41:57,028 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 00:42:05,818 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 00:42:05,824 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 00:42:05,824 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 00:42:05,824 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 00:42:05,826 - ModelManager - INFO - No existing models config found
2025-06-07 00:42:05,826 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 00:42:05,827 - AIEngine - INFO - Loaded 14 learned response patterns
2025-06-07 00:42:05,827 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 00:42:05,827 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 00:42:05,827 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 00:42:05,828 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 00:42:05,828 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 00:42:05,828 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 00:42:06,063 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 00:42:06,065 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 00:42:06,065 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 00:42:06,065 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 00:42:06,171 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 00:42:06,290 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 00:42:06,290 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 00:42:08,300 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 00:42:08,316 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 00:42:08,483 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 00:42:08,484 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 00:42:08,484 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 00:42:11,732 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 00:42:14,758 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 00:42:18,939 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 00:42:18,939 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 00:42:18,939 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 00:42:48,010 - STTEngine - INFO - 🎤 Heard: 'what's your name'
2025-06-07 00:43:51,622 - STTEngine - INFO - 🎤 Heard: 'how are you'
2025-06-07 00:44:04,954 - STTEngine - INFO - 🎤 Heard: 'tell me about yourself'
2025-06-07 00:45:19,689 - STTEngine - INFO - 🎤 Heard: 'what can you do'
2025-06-07 00:45:35,658 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 00:45:35,955 - STTEngine - INFO - Stopped continuous listening
2025-06-07 00:45:36,429 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 00:45:46,159 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 00:45:46,160 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 00:45:46,163 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 00:45:46,163 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 00:45:46,164 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 00:45:46,165 - ModelManager - INFO - No existing models config found
2025-06-07 00:45:46,166 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 00:45:46,166 - AIEngine - INFO - Loaded 17 learned response patterns
2025-06-07 00:45:46,166 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 00:45:46,167 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 00:45:46,167 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 00:45:46,167 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 00:45:46,167 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 00:45:46,167 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 00:45:46,391 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 00:45:46,393 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 00:45:46,393 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 00:45:46,393 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 00:45:46,502 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 00:45:46,623 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 00:45:46,624 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 00:45:48,634 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 00:45:48,650 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 00:45:48,807 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 00:45:48,808 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 00:45:48,808 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 00:45:52,060 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 00:45:55,037 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-07 00:45:59,219 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 00:45:59,220 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 00:45:59,220 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 01:01:29,293 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 01:01:29,364 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 01:01:29,492 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 01:06:20,889 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 01:06:20,891 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 01:06:20,891 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 01:06:20,891 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 01:06:20,893 - ModelManager - INFO - No existing models config found
2025-06-07 01:06:20,894 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 01:06:20,895 - AIEngine - INFO - Loaded 17 learned response patterns
2025-06-07 01:06:20,904 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 01:06:20,928 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 01:06:20,929 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 01:06:20,930 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 01:06:20,932 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 01:06:20,934 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 01:06:21,334 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 01:06:21,336 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 01:06:21,336 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 01:06:21,336 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 01:06:21,446 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 01:06:21,584 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 01:06:21,585 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 01:06:23,595 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 01:06:23,611 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 01:06:23,778 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 01:06:23,778 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 01:06:23,778 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 01:06:27,047 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 01:06:30,074 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 01:06:34,327 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 01:06:34,327 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 01:06:34,327 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 01:06:34,328 - AIEngine - INFO - Generating response for: 'What is your name?'
2025-06-07 01:06:34,328 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:06:34,328 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 01:10:42,530 - AIEngine - INFO - LLM response generated successfully: My name is Gideon, and I am an advanced AI assista...
2025-06-07 01:10:42,537 - AIEngine - INFO - LLM generated successful response: My name is Gideon, and I am an advanced AI assista...
2025-06-07 01:10:42,550 - AIEngine - INFO - Final response generated: My name is Gideon, and I am an advanced AI assista...
2025-06-07 01:10:42,558 - AIEngine - INFO - Generating response for: 'Who are you?'
2025-06-07 01:10:42,558 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:10:42,558 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 01:11:47,434 - AIEngine - INFO - LLM response generated successfully: I am Gideon, an advanced female AI assistant desig...
2025-06-07 01:11:47,436 - AIEngine - INFO - LLM generated successful response: I am Gideon, an advanced female AI assistant desig...
2025-06-07 01:11:47,441 - AIEngine - INFO - Final response generated: I am Gideon, an advanced female AI assistant desig...
2025-06-07 01:11:47,443 - AIEngine - INFO - Generating response for: 'What can you do?'
2025-06-07 01:11:47,443 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:11:47,444 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:13:26,281 - AIEngine - INFO - LLM response generated successfully: I am Gideon, a female AI assistant with extensive ...
2025-06-07 01:13:26,285 - AIEngine - INFO - LLM generated successful response: I am Gideon, a female AI assistant with extensive ...
2025-06-07 01:13:26,292 - AIEngine - INFO - Final response generated: I am Gideon, a female AI assistant with extensive ...
2025-06-07 01:13:26,295 - AIEngine - INFO - Generating response for: 'How are you?'
2025-06-07 01:13:26,295 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:13:26,296 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:14:04,625 - AIEngine - INFO - LLM response generated successfully: I am doing well, thank you for asking! As I mentio...
2025-06-07 01:14:04,626 - AIEngine - INFO - LLM generated successful response: I am doing well, thank you for asking! As I mentio...
2025-06-07 01:14:04,629 - AIEngine - INFO - Final response generated: I am doing well, thank you for asking! As I mentio...
2025-06-07 01:14:04,631 - AIEngine - INFO - Generating response for: 'Tell me about artificial intelligence'
2025-06-07 01:14:04,631 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:14:04,632 - AIEngine - INFO - Generating LLM response with max_tokens=400, temperature=0.6
2025-06-07 01:16:28,556 - AIEngine - INFO - LLM response generated successfully: Artificial Intelligence (AI) is a rapidly growing ...
2025-06-07 01:16:28,559 - AIEngine - INFO - LLM generated successful response: Artificial Intelligence (AI) is a rapidly growing ...
2025-06-07 01:16:28,567 - AIEngine - INFO - Final response generated: Artificial Intelligence (AI) is a rapidly growing ...
2025-06-07 01:16:28,568 - AIEngine - INFO - Generating response for: 'Hello'
2025-06-07 01:16:28,569 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:16:28,569 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:17:06,207 - AIEngine - INFO - LLM response generated successfully: مرحبًا! كم كنت أفكر بسماع ذلك. إذا كان لديك أي أسئ...
2025-06-07 01:17:06,208 - AIEngine - INFO - LLM generated successful response: مرحبًا! كم كنت أفكر بسماع ذلك. إذا كان لديك أي أسئ...
2025-06-07 01:17:06,212 - AIEngine - INFO - Final response generated: مرحبًا! كم كنت أفكر بسماع ذلك. إذا كان لديك أي أسئ...
2025-06-07 01:17:06,215 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 01:17:06,552 - STTEngine - INFO - Stopped continuous listening
2025-06-07 01:17:07,082 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 01:17:26,350 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 01:17:26,351 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 01:17:26,355 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 01:17:26,356 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 01:17:26,356 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 01:17:26,357 - ModelManager - INFO - No existing models config found
2025-06-07 01:17:26,358 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 01:17:26,359 - AIEngine - INFO - Loaded 19 learned response patterns
2025-06-07 01:17:26,359 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 01:17:26,359 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 01:17:26,359 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 01:17:26,359 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 01:17:26,360 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 01:17:26,360 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 01:17:26,592 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 01:17:26,593 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 01:17:26,594 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 01:17:26,594 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 01:17:26,701 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 01:17:26,825 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 01:17:26,826 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 01:17:28,836 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 01:17:28,852 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 01:17:29,091 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 01:17:29,091 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 01:17:29,091 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 01:17:32,338 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 01:17:35,365 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 01:17:39,550 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 01:17:39,550 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 01:17:39,550 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 01:17:54,517 - AIEngine - INFO - Generating response for: 'hi'
2025-06-07 01:17:54,517 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:17:54,518 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:18:36,322 - AIEngine - INFO - Generating response for: 'سلام'
2025-06-07 01:18:36,323 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:18:36,323 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:18:46,361 - AIEngine - INFO - LLM response generated successfully: مرحبا! أنا Gideon، AI مساعدة ووديعة. إذا كان لديك ...
2025-06-07 01:18:46,362 - AIEngine - INFO - LLM generated successful response: مرحبا! أنا Gideon، AI مساعدة ووديعة. إذا كان لديك ...
2025-06-07 01:18:46,366 - AIEngine - INFO - Final response generated: مرحبا! أنا Gideon، AI مساعدة ووديعة. إذا كان لديك ...
2025-06-07 01:18:58,887 - AIEngine - INFO - Generating response for: 'كيف حالك'
2025-06-07 01:18:58,887 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:18:58,887 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:19:11,339 - AIEngine - INFO - LLM response generated successfully: وعليكم السلام! إذا كان لديك أي أسئلة أو potřebت مس...
2025-06-07 01:19:11,340 - AIEngine - INFO - LLM generated successful response: وعليكم السلام! إذا كان لديك أي أسئلة أو potřebت مس...
2025-06-07 01:19:11,343 - AIEngine - INFO - Final response generated: وعليكم السلام! إذا كان لديك أي أسئلة أو potřebت مس...
2025-06-07 01:20:06,288 - AIEngine - INFO - LLM response generated successfully: أنا بخير، شكرًا على سؤالك! كما ذكرت من قبل، سأقوم ...
2025-06-07 01:20:06,290 - AIEngine - INFO - LLM generated successful response: أنا بخير، شكرًا على سؤالك! كما ذكرت من قبل، سأقوم ...
2025-06-07 01:20:06,295 - AIEngine - INFO - Final response generated: أنا بخير، شكرًا على سؤالك! كما ذكرت من قبل، سأقوم ...
2025-06-07 01:21:13,389 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 01:21:13,394 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 01:21:13,394 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 01:21:13,394 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 01:21:13,396 - ModelManager - INFO - No existing models config found
2025-06-07 01:21:13,396 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 01:21:13,397 - AIEngine - INFO - Loaded 21 learned response patterns
2025-06-07 01:21:13,397 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 01:21:13,398 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 01:21:13,398 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 01:21:13,398 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 01:21:13,398 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 01:21:13,398 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 01:21:13,651 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 01:21:13,652 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 01:21:13,652 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 01:21:13,652 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 01:21:13,754 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 01:21:13,872 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 01:21:13,872 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 01:21:15,871 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 01:21:15,883 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 01:21:16,049 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 01:21:16,049 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 01:21:16,049 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 01:21:19,240 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 01:21:22,262 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 01:21:26,444 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 01:21:26,445 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 01:21:26,445 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 01:21:26,447 - AIEngine - INFO - Generating response for: 'hello'
2025-06-07 01:21:26,447 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:21:26,447 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:22:16,513 - AIEngine - INFO - LLM response generated successfully: مرحبًا! إذا كان لديك أي أسئلة أو بحاجة للمساعدة، ل...
2025-06-07 01:22:16,516 - AIEngine - INFO - LLM generated successful response: مرحبًا! إذا كان لديك أي أسئلة أو بحاجة للمساعدة، ل...
2025-06-07 01:22:16,522 - AIEngine - INFO - Final response generated: مرحبًا! إذا كان لديك أي أسئلة أو بحاجة للمساعدة، ل...
2025-06-07 01:22:16,527 - AIEngine - INFO - Generating response for: 'what is your name'
2025-06-07 01:22:16,527 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:22:16,527 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 01:23:12,730 - AIEngine - INFO - LLM response generated successfully: My name is Gideon, a professional female AI assist...
2025-06-07 01:23:12,731 - AIEngine - INFO - LLM generated successful response: My name is Gideon, a professional female AI assist...
2025-06-07 01:23:12,736 - AIEngine - INFO - Final response generated: My name is Gideon, a professional female AI assist...
2025-06-07 01:23:12,739 - AIEngine - INFO - Generating response for: 'who are you'
2025-06-07 01:23:12,739 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:23:12,739 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 01:24:11,565 - AIEngine - INFO - LLM response generated successfully: I am Gideon, an advanced female AI assistant desig...
2025-06-07 01:24:11,566 - AIEngine - INFO - LLM generated successful response: I am Gideon, an advanced female AI assistant desig...
2025-06-07 01:24:11,571 - AIEngine - INFO - Final response generated: I am Gideon, an advanced female AI assistant desig...
2025-06-07 01:24:11,572 - AIEngine - INFO - Generating response for: 'what can you do'
2025-06-07 01:24:11,573 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:24:11,573 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:25:52,906 - AIEngine - INFO - LLM response generated successfully: I am Gideon, a knowledgeable and professional fema...
2025-06-07 01:25:52,911 - AIEngine - INFO - LLM generated successful response: I am Gideon, a knowledgeable and professional fema...
2025-06-07 01:25:52,920 - AIEngine - INFO - Final response generated: I am Gideon, a knowledgeable and professional fema...
2025-06-07 01:25:52,927 - AIEngine - INFO - Generating response for: 'Tell me about yourself'
2025-06-07 01:25:52,928 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:25:52,928 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 01:27:15,778 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 01:27:15,780 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 01:27:15,781 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 01:27:15,781 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 01:27:15,782 - ModelManager - INFO - No existing models config found
2025-06-07 01:27:15,783 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 01:27:15,783 - AIEngine - INFO - Loaded 21 learned response patterns
2025-06-07 01:27:15,783 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 01:27:15,784 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 01:27:15,784 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 01:27:15,785 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 01:27:15,785 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 01:27:15,785 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 01:27:16,106 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 01:27:16,108 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 01:27:16,109 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 01:27:16,109 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 01:27:16,263 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 01:27:16,426 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 01:27:16,427 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 01:27:18,426 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 01:27:18,438 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 01:27:18,680 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 01:27:18,680 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 01:27:18,681 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 01:27:21,946 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 01:27:24,972 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 01:27:29,155 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 01:27:29,155 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 01:27:29,156 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 01:27:29,158 - AIEngine - INFO - Generating response for: 'Hello'
2025-06-07 01:27:29,158 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:27:29,158 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:28:03,130 - AIEngine - INFO - LLM response generated successfully: مرحبا! عضوا وكونوا مرحبًا به. إذا كان لديك أي أسئل...
2025-06-07 01:28:03,130 - AIEngine - INFO - LLM generated successful response: مرحبا! عضوا وكونوا مرحبًا به. إذا كان لديك أي أسئل...
2025-06-07 01:28:03,137 - AIEngine - INFO - Final response generated: مرحبا! عضوا وكونوا مرحبًا به. إذا كان لديك أي أسئل...
2025-06-07 01:28:03,140 - AIEngine - INFO - Generating response for: 'What is your name?'
2025-06-07 01:28:03,140 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:28:03,140 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 01:28:11,776 - STTEngine - INFO - 🎤 Heard: 'what's your name'
2025-06-07 01:29:00,089 - AIEngine - INFO - LLM response generated successfully: My name is Gideon, and I am an advanced female AI ...
2025-06-07 01:29:00,090 - AIEngine - INFO - LLM generated successful response: My name is Gideon, and I am an advanced female AI ...
2025-06-07 01:29:00,094 - AIEngine - INFO - Final response generated: My name is Gideon, and I am an advanced female AI ...
2025-06-07 01:29:00,096 - AIEngine - INFO - Generating response for: 'Who are you?'
2025-06-07 01:29:00,096 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:29:00,096 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 01:29:11,114 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 01:29:11,115 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 01:29:11,116 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 01:29:11,117 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 01:29:11,117 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 01:29:11,118 - ModelManager - INFO - No existing models config found
2025-06-07 01:29:11,119 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 01:29:11,119 - AIEngine - INFO - Loaded 21 learned response patterns
2025-06-07 01:29:11,120 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 01:29:11,120 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 01:29:11,120 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 01:29:11,120 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 01:29:11,120 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 01:29:11,120 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 01:29:11,399 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 01:29:11,401 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 01:29:11,401 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 01:29:11,401 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 01:29:11,510 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 01:29:11,638 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 01:29:11,638 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 01:29:13,630 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 25.595988490273335
2025-06-07 01:29:13,643 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 01:29:13,851 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 01:29:13,851 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 01:29:13,851 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 01:29:17,122 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 01:29:18,666 - STTEngine - INFO - 🎤 Heard: 'hello'
2025-06-07 01:29:20,227 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-07 01:29:21,812 - STTEngine - INFO - 🎤 Heard: 'hello'
2025-06-07 01:29:24,423 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 01:29:24,424 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 01:29:24,424 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 01:29:44,037 - AIEngine - INFO - Generating response for: 'شلونك'
2025-06-07 01:29:44,038 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:29:44,038 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:30:14,557 - AIEngine - INFO - LLM response generated successfully: I am Gideon, an advanced AI assistant with a profe...
2025-06-07 01:30:14,558 - AIEngine - INFO - LLM generated successful response: I am Gideon, an advanced AI assistant with a profe...
2025-06-07 01:30:14,564 - AIEngine - INFO - Final response generated: I am Gideon, an advanced AI assistant with a profe...
2025-06-07 01:30:14,567 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-07 01:30:14,568 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:30:14,568 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:30:47,544 - AIEngine - INFO - LLM response generated successfully: أنا بخير، شكرًا على سؤالك! إذا كان لديك أي أسئلة أ...
2025-06-07 01:30:47,544 - AIEngine - INFO - LLM generated successful response: أنا بخير، شكرًا على سؤالك! إذا كان لديك أي أسئلة أ...
2025-06-07 01:30:47,551 - AIEngine - INFO - Final response generated: أنا بخير، شكرًا على سؤالك! إذا كان لديك أي أسئلة أ...
2025-06-07 01:31:22,421 - AIEngine - INFO - LLM response generated successfully: مرحبًا! سعدت بالتواصل معك. إذا كان لديك أي أسئلة أ...
2025-06-07 01:31:22,422 - AIEngine - INFO - LLM generated successful response: مرحبًا! سعدت بالتواصل معك. إذا كان لديك أي أسئلة أ...
2025-06-07 01:31:22,426 - AIEngine - INFO - Final response generated: مرحبًا! سعدت بالتواصل معك. إذا كان لديك أي أسئلة أ...
2025-06-07 01:31:22,427 - AIEngine - INFO - Generating response for: 'ما اسمك؟'
2025-06-07 01:31:22,427 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:31:22,428 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:31:32,005 - AIEngine - INFO - Generating response for: 'من فاز في كاس العالم 2006'
2025-06-07 01:31:32,007 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:31:32,008 - AIEngine - INFO - Generating LLM response with max_tokens=100, temperature=0.8
2025-06-07 01:31:55,041 - AIEngine - INFO - LLM response generated successfully: اسمي هو دولفين. سعدني مساعدتك في أي شيء ت้องการ! إ...
2025-06-07 01:31:55,042 - AIEngine - INFO - LLM generated successful response: اسمي هو دولفين. سعدني مساعدتك في أي شيء ت้องการ! إ...
2025-06-07 01:31:55,045 - AIEngine - INFO - Final response generated: اسمي هو دولفين. سعدني مساعدتك في أي شيء ت้องการ! إ...
2025-06-07 01:31:55,047 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 01:31:55,268 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 01:32:58,904 - AIEngine - INFO - LLM response generated successfully: فاز منتخب إيطاليا بكأس العالم 2006، بعدما تغلب على...
2025-06-07 01:32:58,905 - AIEngine - INFO - LLM generated successful response: فاز منتخب إيطاليا بكأس العالم 2006، بعدما تغلب على...
2025-06-07 01:32:58,909 - AIEngine - INFO - Final response generated: فاز منتخب إيطاليا بكأس العالم 2006، بعدما تغلب على...
2025-06-07 01:37:52,012 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 01:37:52,307 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 01:37:52,372 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 01:39:11,641 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 01:39:11,642 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 01:39:11,642 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 01:39:11,642 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 01:39:11,643 - ModelManager - INFO - No existing models config found
2025-06-07 01:39:11,644 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 01:39:11,645 - AIEngine - INFO - Loaded 24 learned response patterns
2025-06-07 01:39:11,645 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 01:39:11,645 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 01:39:11,645 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 01:39:11,645 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 01:39:11,646 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 01:39:11,646 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 01:39:11,892 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 01:39:11,893 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 01:39:11,893 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 01:39:11,893 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 01:39:11,997 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 01:39:12,115 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 01:39:12,115 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 01:39:14,125 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 01:39:14,142 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 01:39:14,307 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 01:39:14,307 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 01:39:14,308 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 01:39:17,557 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 01:39:20,584 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 01:39:24,769 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 01:39:24,769 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 01:39:24,769 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 01:39:24,781 - AIEngine - INFO - Generating response for: 'Hello'
2025-06-07 01:39:24,781 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:39:24,782 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:40:57,071 - AIEngine - INFO - LLM response generated successfully: مرحبًا! هل لديك أي أسئلة أواحتياجت مساعدتي في أي ش...
2025-06-07 01:40:57,075 - AIEngine - INFO - LLM generated successful response: مرحبًا! هل لديك أي أسئلة أواحتياجت مساعدتي في أي ش...
2025-06-07 01:40:57,087 - AIEngine - INFO - Final response generated: مرحبًا! هل لديك أي أسئلة أواحتياجت مساعدتي في أي ش...
2025-06-07 01:40:57,097 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-07 01:40:57,097 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:40:57,097 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:41:35,137 - AIEngine - INFO - LLM response generated successfully: مرحبا! سعدت بالتواصل معك. إذا كان لديك أي أسئلة أو...
2025-06-07 01:41:35,137 - AIEngine - INFO - LLM generated successful response: مرحبا! سعدت بالتواصل معك. إذا كان لديك أي أسئلة أو...
2025-06-07 01:41:35,141 - AIEngine - INFO - Final response generated: مرحبا! سعدت بالتواصل معك. إذا كان لديك أي أسئلة أو...
2025-06-07 01:41:35,145 - AIEngine - INFO - Generating response for: 'What is your name?'
2025-06-07 01:41:35,145 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:41:35,145 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 01:42:17,604 - AIEngine - INFO - LLM response generated successfully: My name is Gideon, and I am an advanced AI assista...
2025-06-07 01:42:17,605 - AIEngine - INFO - LLM generated successful response: My name is Gideon, and I am an advanced AI assista...
2025-06-07 01:42:17,608 - AIEngine - INFO - Final response generated: My name is Gideon, and I am an advanced AI assista...
2025-06-07 01:42:17,610 - AIEngine - INFO - Generating response for: 'ما اسمك؟'
2025-06-07 01:42:17,610 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:42:17,610 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:43:04,130 - AIEngine - INFO - LLM response generated successfully: اسمي هو دولفين، مساعد ذكي ووديع. سأقوم بالإجابة عل...
2025-06-07 01:43:04,131 - AIEngine - INFO - LLM generated successful response: اسمي هو دولفين، مساعد ذكي ووديع. سأقوم بالإجابة عل...
2025-06-07 01:43:04,135 - AIEngine - INFO - Final response generated: اسمي هو دولفين، مساعد ذكي ووديع. سأقوم بالإجابة عل...
2025-06-07 01:43:04,143 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 01:43:04,857 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 01:43:19,972 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 01:43:19,973 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 01:43:19,974 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 01:43:19,974 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 01:43:19,974 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 01:43:19,976 - ModelManager - INFO - No existing models config found
2025-06-07 01:43:19,977 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 01:43:19,977 - AIEngine - INFO - Loaded 24 learned response patterns
2025-06-07 01:43:19,977 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 01:43:19,978 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 01:43:19,978 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 01:43:19,978 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 01:43:19,978 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 01:43:19,978 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 01:43:20,207 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 01:43:20,209 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 01:43:20,209 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 01:43:20,209 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 01:43:20,309 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 01:43:20,433 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 01:43:20,433 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 01:43:22,444 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 01:43:22,460 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 01:43:22,636 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 01:43:22,636 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 01:43:22,636 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 01:43:25,893 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 01:43:28,919 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 01:43:33,102 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 01:43:33,103 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 01:43:33,103 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 01:44:16,837 - AIEngine - INFO - Generating response for: 'شلونك'
2025-06-07 01:44:16,838 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:44:16,838 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 01:45:10,917 - AIEngine - INFO - LLM response generated successfully: أنا بخير، شكراً على اهتمامك! إذا كان لديك أي أسئلة...
2025-06-07 01:45:10,917 - AIEngine - INFO - LLM generated successful response: أنا بخير، شكراً على اهتمامك! إذا كان لديك أي أسئلة...
2025-06-07 01:45:10,921 - AIEngine - INFO - Final response generated: أنا بخير، شكراً على اهتمامك! إذا كان لديك أي أسئلة...
2025-06-07 01:45:28,743 - STTEngine - INFO - 🎤 Heard: 'afternoon wife hot'
2025-06-07 01:46:01,956 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 01:46:02,119 - STTEngine - INFO - Stopped continuous listening
2025-06-07 01:46:02,215 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 01:46:02,276 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 01:50:49,894 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 01:50:49,895 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 01:50:49,895 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 01:50:49,895 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 01:50:49,897 - ModelManager - INFO - No existing models config found
2025-06-07 01:50:49,897 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 01:50:49,898 - AIEngine - INFO - Loaded 24 learned response patterns
2025-06-07 01:50:49,899 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 01:50:49,899 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 01:50:49,899 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 01:50:49,899 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 01:50:49,899 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 01:50:49,899 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 01:50:50,147 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 01:50:50,148 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 01:50:50,148 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 01:50:50,148 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 01:52:09,043 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 01:52:09,045 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 01:52:09,045 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 01:52:09,045 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 01:52:09,046 - ModelManager - INFO - No existing models config found
2025-06-07 01:52:09,047 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 01:52:09,048 - AIEngine - INFO - Loaded 24 learned response patterns
2025-06-07 01:52:09,048 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 01:52:09,049 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 01:52:09,049 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 01:52:09,049 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 01:52:09,049 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 01:52:09,049 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 01:52:09,283 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 01:52:09,285 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 01:52:09,285 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 01:52:09,285 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 01:52:24,171 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 01:52:24,172 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 01:52:24,172 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 01:52:24,173 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 01:52:24,174 - ModelManager - INFO - No existing models config found
2025-06-07 01:52:24,175 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 01:52:24,175 - AIEngine - INFO - Loaded 24 learned response patterns
2025-06-07 01:52:24,175 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 01:52:24,176 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 01:52:24,176 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 01:52:24,176 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 01:52:24,176 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 01:52:24,176 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 01:52:24,409 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 01:52:24,410 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 01:52:24,410 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 01:52:24,410 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 01:52:31,323 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 01:52:31,323 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 01:52:31,324 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 01:52:31,449 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 01:52:31,580 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 01:52:31,580 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 01:52:33,592 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 01:52:33,608 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 01:52:33,793 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 01:52:33,793 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 01:52:33,793 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 01:52:37,049 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 01:52:40,076 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 01:52:44,260 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 01:52:44,260 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 01:52:44,260 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 01:52:44,271 - AIEngine - INFO - Generating response for: 'explain artificial intelligence'
2025-06-07 01:52:44,271 - AIEngine - INFO - Generating response for: 'explain artificial intelligence'
2025-06-07 01:52:44,272 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:52:44,272 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:52:44,272 - AIEngine - INFO - Generating LLM response with max_tokens=400, temperature=0.6
2025-06-07 01:52:44,272 - AIEngine - INFO - Generating LLM response with max_tokens=400, temperature=0.6
2025-06-07 01:52:44,383 - AIEngine - INFO - Generating response for: 'explain artificial intelligence'
2025-06-07 01:52:44,383 - AIEngine - INFO - Generating response for: 'explain artificial intelligence'
2025-06-07 01:52:44,384 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:52:44,384 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:52:44,384 - AIEngine - INFO - Generating LLM response with max_tokens=400, temperature=0.6
2025-06-07 01:52:44,384 - AIEngine - INFO - Generating LLM response with max_tokens=400, temperature=0.6
2025-06-07 01:52:44,492 - AIEngine - INFO - Generating response for: 'explain artificial intelligence'
2025-06-07 01:52:44,492 - AIEngine - INFO - Generating response for: 'explain artificial intelligence'
2025-06-07 01:52:44,492 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:52:44,493 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:52:44,493 - AIEngine - INFO - Generating LLM response with max_tokens=400, temperature=0.6
2025-06-07 01:52:44,493 - AIEngine - INFO - Generating LLM response with max_tokens=400, temperature=0.6
2025-06-07 01:52:44,601 - AIEngine - INFO - Generating response for: 'tell me about yourself in detail'
2025-06-07 01:52:44,602 - AIEngine - INFO - Generating response for: 'tell me about yourself in detail'
2025-06-07 01:52:44,602 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:52:44,602 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:52:44,602 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 01:52:44,602 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 01:52:44,711 - AIEngine - INFO - Generating response for: 'tell me about yourself in detail'
2025-06-07 01:52:44,711 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:52:44,712 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 01:52:44,822 - AIEngine - INFO - Generating response for: 'tell me about yourself in detail'
2025-06-07 01:52:44,823 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:52:44,823 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 01:52:44,931 - AIEngine - INFO - Generating response for: 'how does machine learning work'
2025-06-07 01:52:44,932 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:52:44,932 - AIEngine - INFO - Generating LLM response with max_tokens=400, temperature=0.6
2025-06-07 01:52:45,040 - AIEngine - INFO - Generating response for: 'how does machine learning work'
2025-06-07 01:52:45,040 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:52:45,041 - AIEngine - INFO - Generating LLM response with max_tokens=400, temperature=0.6
2025-06-07 01:52:45,149 - AIEngine - INFO - Generating response for: 'how does machine learning work'
2025-06-07 01:52:45,149 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:52:45,149 - AIEngine - INFO - Generating LLM response with max_tokens=400, temperature=0.6
2025-06-07 01:52:45,261 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 01:52:45,274 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 01:52:45,368 - STTEngine - INFO - Stopped continuous listening
2025-06-07 01:54:53,287 - AIEngine - INFO - LLM response generated successfully: Artificial Intelligence (AI) is a field of compute...
2025-06-07 01:54:53,288 - AIEngine - INFO - LLM generated successful response: Artificial Intelligence (AI) is a field of compute...
2025-06-07 01:54:53,293 - AIEngine - INFO - Final response generated: Artificial Intelligence (AI) is a field of compute...
2025-06-07 01:56:29,530 - AIEngine - INFO - LLM response generated successfully: Artificial Intelligence (AI) refers to the simulat...
2025-06-07 01:56:29,531 - AIEngine - INFO - LLM generated successful response: Artificial Intelligence (AI) refers to the simulat...
2025-06-07 01:56:29,535 - AIEngine - INFO - Final response generated: Artificial Intelligence (AI) refers to the simulat...
2025-06-07 01:56:29,535 - AIEngine - INFO - Generating response for: 'tell me about yourself in detail'
2025-06-07 01:56:29,536 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 01:56:29,536 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 01:57:54,460 - AIEngine - INFO - LLM response generated successfully: Artificial Intelligence, or AI for short, refers t...
2025-06-07 01:57:54,460 - AIEngine - INFO - LLM generated successful response: Artificial Intelligence, or AI for short, refers t...
2025-06-07 01:57:54,464 - AIEngine - INFO - Final response generated: Artificial Intelligence, or AI for short, refers t...
2025-06-07 01:59:39,076 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 01:59:39,078 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 01:59:39,080 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 01:59:39,081 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 01:59:39,083 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 01:59:39,085 - ModelManager - INFO - No existing models config found
2025-06-07 01:59:39,086 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 01:59:39,087 - AIEngine - INFO - Loaded 25 learned response patterns
2025-06-07 01:59:39,087 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 01:59:39,088 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 01:59:39,088 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 01:59:39,088 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 01:59:39,088 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 01:59:39,089 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 01:59:39,366 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 01:59:39,368 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 01:59:39,368 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 01:59:39,369 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 02:00:11,793 - AIEngine - INFO - LLM response generated successfully: Artificial Intelligence (AI) is a field of compute...
2025-06-07 02:00:11,793 - AIEngine - INFO - LLM generated successful response: Artificial Intelligence (AI) is a field of compute...
2025-06-07 02:00:11,799 - AIEngine - INFO - Final response generated: Artificial Intelligence (AI) is a field of compute...
2025-06-07 02:00:11,799 - AIEngine - INFO - Generating response for: 'tell me about yourself in detail'
2025-06-07 02:00:11,800 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 02:00:11,800 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 02:03:10,003 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 02:03:10,005 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 02:03:10,009 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 02:03:10,010 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 02:03:10,010 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 02:03:10,011 - ModelManager - INFO - No existing models config found
2025-06-07 02:03:10,012 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 02:03:10,012 - AIEngine - INFO - Loaded 25 learned response patterns
2025-06-07 02:03:10,012 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 02:03:10,013 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 02:03:10,013 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 02:03:10,013 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 02:03:10,013 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 02:03:10,013 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 02:03:10,274 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 02:03:10,275 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 02:03:10,275 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 02:03:10,275 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 02:03:46,096 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 02:03:46,098 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 02:03:46,098 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 02:03:46,285 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 02:03:46,442 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 02:03:46,442 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 02:03:48,453 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 02:03:48,470 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 02:03:48,715 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 02:03:48,715 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 02:03:48,715 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 02:03:51,970 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 02:03:56,249 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-07 02:04:00,435 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 02:04:00,435 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 02:04:00,435 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 02:04:34,078 - AIEngine - INFO - Generating response for: 'شلونك'
2025-06-07 02:04:34,079 - AIEngine - INFO - Generating response for: 'شلونك'
2025-06-07 02:04:34,080 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 02:04:34,080 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 02:04:34,080 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 02:04:34,080 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 02:05:27,054 - AIEngine - INFO - LLM response generated successfully: أنا بخير، شكرًا على سؤالك! كما ذكرت من قبل، سأقوم ...
2025-06-07 02:05:27,055 - AIEngine - INFO - LLM generated successful response: أنا بخير، شكرًا على سؤالك! كما ذكرت من قبل، سأقوم ...
2025-06-07 02:05:27,064 - AIEngine - INFO - Final response generated: أنا بخير، شكرًا على سؤالك! كما ذكرت من قبل، سأقوم ...
2025-06-07 02:06:15,671 - AIEngine - INFO - LLM response generated successfully: أنا بخير، شكراً على اهتمامك! كما ذكرت من قبل، سأقو...
2025-06-07 02:06:15,672 - AIEngine - INFO - LLM generated successful response: أنا بخير، شكراً على اهتمامك! كما ذكرت من قبل، سأقو...
2025-06-07 02:06:15,676 - AIEngine - INFO - Final response generated: أنا بخير، شكراً على اهتمامك! كما ذكرت من قبل، سأقو...
2025-06-07 02:07:24,147 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 02:07:24,395 - STTEngine - INFO - Stopped continuous listening
2025-06-07 02:07:25,081 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 02:07:25,140 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 02:12:41,813 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 02:12:41,814 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 02:12:41,815 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 02:12:41,815 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 02:12:41,816 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 02:12:41,817 - ModelManager - INFO - No existing models config found
2025-06-07 02:12:41,818 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 02:12:41,818 - AIEngine - INFO - Loaded 25 learned response patterns
2025-06-07 02:12:41,819 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 02:12:41,819 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 02:12:41,819 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 02:12:41,819 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 02:12:41,819 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 02:12:41,820 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 02:12:42,063 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 02:12:42,065 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 02:12:42,065 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 02:12:42,065 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 02:13:55,630 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 02:13:55,639 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 02:13:55,639 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 02:13:55,865 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 02:13:56,039 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 02:13:56,040 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 02:13:58,050 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 11.035729684572432
2025-06-07 02:13:58,067 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 02:13:58,344 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 02:13:58,344 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 02:13:58,344 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 02:14:01,597 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 02:14:05,106 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-07 02:14:09,288 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 02:14:09,289 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 02:14:09,289 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 02:14:46,566 - AIEngine - INFO - Generating response for: 'شلونك'
2025-06-07 02:14:46,567 - AIEngine - INFO - Generating response for: 'شلونك'
2025-06-07 02:14:46,568 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 02:14:46,568 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 02:14:46,568 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 02:14:46,569 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 02:15:30,008 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 02:15:30,009 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 02:15:30,010 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 02:15:30,011 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 02:15:30,011 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 02:15:30,012 - ModelManager - INFO - No existing models config found
2025-06-07 02:15:30,013 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 02:15:30,014 - AIEngine - INFO - Loaded 25 learned response patterns
2025-06-07 02:15:30,014 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 02:15:30,014 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 02:15:30,014 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 02:15:30,015 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 02:15:30,015 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 02:15:30,015 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 02:15:30,276 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 02:15:30,278 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 02:15:30,278 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 02:15:30,278 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 02:15:38,966 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 02:15:38,967 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 02:15:38,967 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 02:15:39,107 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 02:15:39,240 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 02:15:39,240 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 02:15:41,251 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 02:15:41,267 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 02:15:41,465 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 02:15:41,465 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 02:15:41,465 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 02:15:44,720 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 02:15:47,747 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 02:15:51,931 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 02:15:51,932 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 02:15:51,932 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 02:16:49,162 - AIEngine - INFO - Generating response for: 'شلونك'
2025-06-07 02:16:49,162 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 02:16:49,163 - AIEngine - INFO - Generating response for: 'شلونك'
2025-06-07 02:16:49,163 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 02:16:49,164 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 02:16:49,168 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 02:18:28,168 - AIEngine - INFO - LLM response generated successfully: أنا بخير، شكرًا على اهتمامك! كما ذكرت من قبل، سأقو...
2025-06-07 02:18:28,173 - AIEngine - INFO - LLM generated successful response: أنا بخير، شكرًا على اهتمامك! كما ذكرت من قبل، سأقو...
2025-06-07 02:18:28,187 - AIEngine - INFO - Final response generated: أنا بخير، شكرًا على اهتمامك! كما ذكرت من قبل، سأقو...
2025-06-07 02:19:08,807 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 02:19:08,808 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 02:19:08,810 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 02:19:08,810 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 02:19:08,810 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 02:19:08,811 - ModelManager - INFO - No existing models config found
2025-06-07 02:19:08,812 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 02:19:08,813 - AIEngine - INFO - Loaded 25 learned response patterns
2025-06-07 02:19:08,813 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 02:19:08,813 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 02:19:08,814 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 02:19:08,814 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 02:19:08,814 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 02:19:08,814 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 02:19:09,084 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 02:19:09,086 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 02:19:09,086 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 02:19:09,086 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 02:19:24,323 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 02:19:24,324 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 02:19:24,324 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 02:19:24,452 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 02:19:24,581 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 02:19:24,581 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 02:19:26,592 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 02:19:26,608 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 02:19:26,815 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 02:19:26,815 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 02:19:26,815 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 02:19:30,068 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 02:19:33,095 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 02:19:37,280 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 02:19:37,280 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 02:19:37,280 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 02:21:31,214 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 02:21:31,215 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 02:21:31,220 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 02:21:31,220 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 02:21:31,220 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 02:21:31,222 - ModelManager - INFO - No existing models config found
2025-06-07 02:21:31,223 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 02:21:31,223 - AIEngine - INFO - Loaded 25 learned response patterns
2025-06-07 02:21:31,223 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 02:21:31,224 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 02:21:31,224 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 02:21:31,224 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 02:21:31,224 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 02:21:31,224 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 02:21:31,481 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 02:21:31,483 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 02:21:31,483 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 02:21:31,483 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 02:21:59,633 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 02:21:59,635 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 02:21:59,635 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 02:21:59,778 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 02:21:59,911 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 02:21:59,911 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 02:22:01,921 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 02:22:01,938 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 02:22:02,139 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 02:22:02,140 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 02:22:02,140 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 02:22:05,393 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 02:22:08,421 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 02:22:12,608 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 02:22:12,609 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 02:22:12,609 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 21:27:01,013 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 21:27:01,014 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 21:27:01,015 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 21:27:01,015 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 21:27:01,016 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 21:27:01,017 - ModelManager - INFO - No existing models config found
2025-06-07 21:27:01,018 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 21:27:01,018 - AIEngine - INFO - Loaded 25 learned response patterns
2025-06-07 21:27:01,018 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 21:27:01,019 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 21:27:01,019 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 21:27:01,019 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 21:27:01,019 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 21:27:01,019 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 21:27:01,247 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 21:27:01,248 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 21:27:01,248 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 21:27:01,249 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 21:28:41,571 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 21:28:41,578 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 21:28:41,579 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 21:28:41,782 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 21:28:41,916 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 21:28:41,916 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 21:28:43,909 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-07 21:28:43,926 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-07 21:28:44,425 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 21:28:44,426 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 21:28:44,426 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 21:28:47,682 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 21:28:50,693 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 21:28:54,878 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 21:28:54,878 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 21:28:54,878 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 21:29:53,715 - STTEngine - INFO - 🎤 Heard: 'hey Kiran how are you'
2025-06-07 21:30:01,091 - STTEngine - INFO - 🎤 Heard: 'getting a song'
2025-06-07 21:30:03,724 - STTEngine - INFO - 🎤 Heard: 'mon'
2025-06-07 21:30:12,448 - STTEngine - INFO - 🎤 Heard: 'Star Jalsha'
2025-06-07 21:30:33,665 - GideonCore - INFO - Always listening stopped
2025-06-07 21:30:34,224 - STTEngine - INFO - Stopped continuous listening
2025-06-07 21:39:09,965 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 21:39:10,018 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 21:39:10,131 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 21:39:10,131 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 21:39:13,131 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0217979944915192
2025-06-07 21:39:13,143 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 21:39:13,143 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 21:39:13,143 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 21:39:13,144 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 21:39:13,144 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 21:39:13,144 - STTEngine - INFO - 🌍 Fixed language mode: en
2025-06-07 21:39:13,144 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 21:39:13,144 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 21:39:13,144 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 21:39:13,144 - STTEngine - INFO - 🌍 Language mode set to: en
2025-06-07 21:39:13,145 - STTEngine - INFO - 🌍 Language mode set to: ar
2025-06-07 21:39:13,145 - STTEngine - INFO - 🌍 Language mode set to: auto
2025-06-07 21:39:13,145 - STTEngine - INFO - 🎯 Wake word sensitivity adjusted to: 0.8
2025-06-07 21:39:13,145 - STTEngine - INFO - 🎯 Wake word sensitivity adjusted to: 0.4
2025-06-07 21:39:13,145 - STTEngine - INFO - 🎯 Wake word sensitivity adjusted to: 0.6
2025-06-07 21:39:13,203 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 21:39:19,323 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:39:22,451 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:39:28,930 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 21:39:28,930 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 21:39:28,931 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 21:39:28,931 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 21:39:28,931 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 21:39:28,931 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 21:39:28,931 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 21:39:29,174 - STTEngine - INFO - Stopped continuous listening
2025-06-07 21:39:29,186 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 21:39:29,188 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 21:39:29,188 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 21:39:29,188 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 21:40:45,043 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 21:40:45,048 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 21:40:45,048 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 21:40:45,050 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-07 21:40:45,050 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 21:40:45,050 - AIEngine - INFO - 🌍 Response language set to: auto
2025-06-07 21:40:45,050 - AIEngine - INFO - 🌍 Bilingual mode: enabled
2025-06-07 21:41:09,143 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 21:41:09,144 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 21:41:09,149 - MemorySystem - INFO - Loaded 14 recent conversations
2025-06-07 21:41:09,149 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 21:41:09,149 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 21:41:09,150 - ModelManager - INFO - No existing models config found
2025-06-07 21:41:09,151 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 21:41:09,152 - AIEngine - INFO - Loaded 25 learned response patterns
2025-06-07 21:41:09,152 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 21:41:09,152 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 21:41:09,152 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 21:41:09,153 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 21:41:09,153 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 21:41:09,153 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 21:41:09,403 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 21:41:09,404 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 21:41:09,404 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 21:41:09,405 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 21:41:16,985 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 21:41:16,986 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 21:41:16,986 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 21:41:17,102 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 21:41:17,158 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 21:41:17,278 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 21:41:17,278 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 21:41:19,010 - AIEngine - INFO - Generating response for: 'شلوتج'
2025-06-07 21:41:19,017 - AIEngine - INFO - Generating response for: 'شلوتج'
2025-06-07 21:41:19,019 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:41:19,019 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:41:19,020 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:41:19,020 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:41:20,273 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.1186459966325606
2025-06-07 21:41:20,287 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 21:41:20,287 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 21:41:20,287 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 21:41:20,288 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 21:41:20,288 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 21:41:20,288 - STTEngine - INFO - 🌍 Fixed language mode: en
2025-06-07 21:41:20,288 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 21:41:20,288 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 21:41:20,288 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 21:41:20,595 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 21:41:20,595 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 21:41:20,596 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 21:41:24,001 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 21:41:26,279 - STTEngine - INFO - Microphone test successful. Heard: hello
2025-06-07 21:41:28,415 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 21:41:28,416 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 21:41:28,416 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 21:41:33,417 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 21:41:34,335 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 21:41:34,444 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 21:41:40,482 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:41:43,810 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:41:47,500 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:41:54,255 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:42:03,433 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:42:09,191 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:42:12,884 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 21:42:12,885 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 21:42:12,886 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:42:12,886 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:42:12,887 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:42:12,887 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:42:14,871 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:42:18,126 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:42:26,123 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:42:29,734 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:42:32,158 - STTEngine - INFO - 🎤 Heard: 'Union'
2025-06-07 21:42:37,295 - STTEngine - INFO - 🎤 Heard: 'Indian market'
2025-06-07 21:42:37,295 - STTEngine - INFO - 🎯 Wake word 'gidian' detected in 'Indian market'!
2025-06-07 21:42:37,296 - STTEngine - INFO - 📝 Command detected: 'market'
2025-06-07 21:42:37,296 - GideonCore - INFO - 🎯 WAKE WORD DETECTED! Processing command: 'market'
2025-06-07 21:42:37,296 - GideonCore - INFO - 🌍 Detected language: en
2025-06-07 21:42:37,297 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-07 21:42:37,308 - GideonCore - INFO - Processing text: market
2025-06-07 21:42:37,309 - AIEngine - INFO - Generating response for: 'market'
2025-06-07 21:42:37,309 - AIEngine - INFO - Generating response for: 'market'
2025-06-07 21:42:37,309 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:42:37,310 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:42:37,310 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:42:37,310 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:42:37,427 - GideonCore - INFO - CONVERSATION - User: market
2025-06-07 21:42:37,428 - GideonCore - INFO - CONVERSATION - AI: I'm processing that...
2025-06-07 21:42:42,492 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:42:43,493 - AIEngine - INFO - LLM response generated successfully: شلوطج هو نوع من أنواع الأطعمة المصورة والشائعة في ...
2025-06-07 21:42:43,495 - AIEngine - INFO - LLM generated successful response: شلوطج هو نوع من أنواع الأطعمة المصورة والشائعة في ...
2025-06-07 21:42:43,505 - AIEngine - INFO - Final response generated: شلوطج هو نوع من أنواع الأطعمة المصورة والشائعة في ...
2025-06-07 21:42:47,252 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:42:51,514 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:42:53,958 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:42:58,099 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:43:02,313 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:43:04,645 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:43:12,942 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:43:19,417 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:43:23,418 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:43:27,599 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:43:30,748 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:43:33,956 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:43:38,432 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:43:41,448 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:43:46,103 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:43:51,563 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:43:55,668 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:43:59,869 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:44:04,695 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:44:08,176 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:44:10,185 - AIEngine - INFO - LLM response generated successfully: هلا، سعدتني سؤالك. شلوتج (Chelouche) هو جنس من الن...
2025-06-07 21:44:10,186 - AIEngine - INFO - LLM generated successful response: هلا، سعدتني سؤالك. شلوتج (Chelouche) هو جنس من الن...
2025-06-07 21:44:10,191 - AIEngine - INFO - Final response generated: هلا، سعدتني سؤالك. شلوتج (Chelouche) هو جنس من الن...
2025-06-07 21:44:11,948 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:44:34,458 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:44:38,578 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:44:42,425 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:44:50,424 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:44:56,133 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:45:16,067 - AIEngine - INFO - LLM response generated successfully: أهلاً وسهلاً، سعدت بسماع ذلك! إذا كان لديك أي أسئل...
2025-06-07 21:45:16,069 - AIEngine - INFO - LLM generated successful response: أهلاً وسهلاً، سعدت بسماع ذلك! إذا كان لديك أي أسئل...
2025-06-07 21:45:16,079 - AIEngine - INFO - Final response generated: أهلاً وسهلاً، سعدت بسماع ذلك! إذا كان لديك أي أسئل...
2025-06-07 21:45:54,573 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:46:00,516 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:46:05,637 - AIEngine - INFO - LLM response generated successfully: مرحبا! إذا كان لديك أي أسئلة أو potřebت مساعدتي في...
2025-06-07 21:46:05,638 - AIEngine - INFO - LLM generated successful response: مرحبا! إذا كان لديك أي أسئلة أو potřebت مساعدتي في...
2025-06-07 21:46:05,647 - AIEngine - INFO - Final response generated: مرحبا! إذا كان لديك أي أسئلة أو potřebت مساعدتي في...
2025-06-07 21:46:32,301 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:46:35,778 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:46:57,890 - STTEngine - INFO - 🎤 Heard: 'unable hard'
2025-06-07 21:47:02,226 - STTEngine - INFO - 🎤 Heard: 'Cricbuzz'
2025-06-07 21:47:11,079 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:47:14,076 - STTEngine - INFO - 🎤 Heard: 'vah dikhana'
2025-06-07 21:47:17,252 - STTEngine - INFO - 🎤 Heard: 'new cycle'
2025-06-07 21:47:21,449 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:47:24,319 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:47:24,641 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 21:47:24,641 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 21:47:24,642 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 21:47:24,643 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 21:47:24,643 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 21:47:24,643 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 21:47:24,643 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 21:47:24,950 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 21:47:24,953 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 21:47:24,953 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 21:47:24,953 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 21:47:31,110 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:47:35,589 - STTEngine - INFO - 🎤 Heard: 'watch'
2025-06-07 21:47:38,610 - STTEngine - INFO - 🎤 Heard: 'potas'
2025-06-07 21:47:43,037 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:47:44,964 - AIEngine - INFO - LLM response generated successfully: The user is asking about the concept of a 'market....
2025-06-07 21:47:44,965 - AIEngine - INFO - LLM generated successful response: The user is asking about the concept of a 'market....
2025-06-07 21:47:44,969 - AIEngine - INFO - Final response generated: The user is asking about the concept of a 'market....
2025-06-07 21:47:49,348 - STTEngine - INFO - 🎤 Heard: 'film hot hot'
2025-06-07 21:48:51,559 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:48:55,039 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:49:06,180 - AIEngine - INFO - LLM response generated successfully: The term "market" generally refers to a platform w...
2025-06-07 21:49:06,181 - AIEngine - INFO - LLM generated successful response: The term "market" generally refers to a platform w...
2025-06-07 21:49:06,184 - AIEngine - INFO - Final response generated: The term "market" generally refers to a platform w...
2025-06-07 21:49:13,387 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 21:49:13,389 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 21:49:13,389 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 21:49:13,389 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 21:49:13,390 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-07 21:49:13,390 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:49:13,391 - AIEngine - INFO - 🌍 Building prompt for language: en
2025-06-07 21:49:13,391 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:49:48,376 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:50:00,859 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 21:50:00,859 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 21:50:00,860 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 21:50:00,860 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 21:50:00,860 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 21:50:00,861 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 21:50:00,861 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 21:50:01,119 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 21:50:01,120 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 21:50:01,121 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 21:50:01,121 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 21:50:02,683 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:50:08,072 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:50:09,599 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 21:50:09,600 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 21:50:09,600 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 21:50:09,600 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 21:50:09,600 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-07 21:50:09,601 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:50:09,601 - AIEngine - INFO - 🌍 Building prompt for language: en
2025-06-07 21:50:09,601 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:50:12,593 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:50:16,881 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:50:22,579 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:50:26,864 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:50:29,513 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:50:32,800 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:50:36,987 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:50:42,046 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:50:44,689 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:50:54,531 - AIEngine - INFO - LLM response generated successfully: Hello there! It seems like you're trying to commun...
2025-06-07 21:50:54,531 - AIEngine - INFO - LLM generated successful response: Hello there! It seems like you're trying to commun...
2025-06-07 21:50:54,531 - AIEngine - INFO - Final response generated: Hello there! It seems like you're trying to commun...
2025-06-07 21:50:54,532 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 21:50:54,532 - AIEngine - INFO - Generating response for: 'السلام عليكم'
2025-06-07 21:50:54,532 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:50:54,532 - AIEngine - INFO - 🌍 Building prompt for language: en
2025-06-07 21:50:54,532 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:50:58,931 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:51:01,562 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:51:06,626 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:51:08,905 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:51:11,834 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:51:14,096 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:51:16,868 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:51:19,535 - AIEngine - INFO - LLM response generated successfully: Hello! If you have any questions or need assistanc...
2025-06-07 21:51:19,535 - AIEngine - INFO - LLM generated successful response: Hello! If you have any questions or need assistanc...
2025-06-07 21:51:19,535 - AIEngine - INFO - Final response generated: Hello! If you have any questions or need assistanc...
2025-06-07 21:51:19,536 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 21:51:19,536 - AIEngine - INFO - Generating response for: 'ما اسمك؟'
2025-06-07 21:51:19,536 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:51:19,536 - AIEngine - INFO - 🌍 Building prompt for language: en
2025-06-07 21:51:19,536 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:51:25,806 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:51:34,044 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:51:35,807 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:51:40,703 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:51:44,102 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:51:47,696 - AIEngine - INFO - LLM response generated successfully: My apologies, I am unable to understand your quest...
2025-06-07 21:51:47,696 - AIEngine - INFO - LLM generated successful response: My apologies, I am unable to understand your quest...
2025-06-07 21:51:47,697 - AIEngine - INFO - Final response generated: My apologies, I am unable to understand your quest...
2025-06-07 21:51:47,697 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 21:51:47,697 - AIEngine - INFO - Generating response for: 'كيف حالك؟'
2025-06-07 21:51:47,697 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:51:47,698 - AIEngine - INFO - 🌍 Building prompt for language: en
2025-06-07 21:51:47,698 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:51:55,149 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:51:58,297 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:52:06,949 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:52:09,958 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:52:13,656 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:52:15,695 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:52:18,551 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 21:52:18,552 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 21:52:18,552 - AIEngine - INFO - 🎯 Using set response language: ar
2025-06-07 21:52:18,552 - AIEngine - INFO - 🔧 Using config detected language: en
2025-06-07 21:52:18,552 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 21:52:18,552 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-07 21:52:18,552 - AIEngine - INFO - 🎯 Using set response language: ar
2025-06-07 21:52:18,552 - AIEngine - INFO - 🔧 Using config detected language: en
2025-06-07 21:52:18,553 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 21:52:44,587 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 21:52:44,587 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 21:52:44,587 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 21:52:44,587 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 21:52:44,588 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-07 21:52:44,588 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 21:52:44,588 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 21:52:47,658 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:52:50,361 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:53:02,554 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:53:06,933 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 21:53:06,933 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 21:53:06,933 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 21:53:06,933 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 21:53:06,934 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 21:53:06,934 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 21:53:06,934 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 21:53:07,186 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 21:53:07,188 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 21:53:07,188 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 21:53:07,188 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 21:53:09,134 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:53:13,781 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:53:16,479 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:53:22,573 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:53:22,668 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 21:53:22,668 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 21:53:22,668 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 21:53:22,668 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 21:53:22,669 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-07 21:53:22,669 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:53:22,669 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 21:53:22,669 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 21:53:22,670 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 21:53:22,670 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:53:25,671 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:53:29,656 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:53:36,339 - AIEngine - INFO - LLM response generated successfully: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 21:53:36,339 - AIEngine - INFO - LLM generated successful response: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 21:53:36,339 - AIEngine - INFO - Final response generated: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 21:53:36,340 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-07 21:53:36,340 - AIEngine - INFO - Generating response for: 'hello'
2025-06-07 21:53:36,340 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:53:36,340 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-07 21:53:36,340 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-07 21:53:36,340 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 21:53:36,340 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:53:36,476 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:53:44,508 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:53:46,437 - AIEngine - INFO - LLM response generated successfully: Hello! How can I help you today?...
2025-06-07 21:53:46,437 - AIEngine - INFO - LLM generated successful response: Hello! How can I help you today?...
2025-06-07 21:53:46,437 - AIEngine - INFO - Final response generated: Hello! How can I help you today?...
2025-06-07 21:53:49,735 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 21:53:49,760 - STTEngine - INFO - Stopped continuous listening
2025-06-07 21:53:50,247 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 21:53:50,337 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 21:53:57,801 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 21:53:57,802 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 21:53:57,807 - MemorySystem - INFO - Loaded 15 recent conversations
2025-06-07 21:53:57,807 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 21:53:57,807 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 21:53:57,809 - ModelManager - INFO - No existing models config found
2025-06-07 21:53:57,809 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 21:53:57,810 - AIEngine - INFO - Loaded 27 learned response patterns
2025-06-07 21:53:57,810 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 21:53:57,811 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 21:53:57,811 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 21:53:57,811 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 21:53:57,811 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 21:53:57,811 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 21:53:58,034 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 21:53:58,035 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 21:53:58,036 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 21:53:58,036 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 21:54:05,282 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 21:54:05,282 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 21:54:05,282 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 21:54:05,400 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 21:54:05,453 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 21:54:05,571 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 21:54:05,571 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 21:54:08,569 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.025454140131881
2025-06-07 21:54:08,581 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 21:54:08,581 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 21:54:08,581 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 21:54:08,581 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 21:54:08,581 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 21:54:08,581 - STTEngine - INFO - 🌍 Fixed language mode: en
2025-06-07 21:54:08,581 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 21:54:08,582 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 21:54:08,582 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 21:54:08,776 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 21:54:08,777 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 21:54:08,777 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 21:54:12,025 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 21:54:17,494 - STTEngine - INFO - Microphone test successful. Heard: hello
2025-06-07 21:54:19,616 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 21:54:19,617 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 21:54:19,617 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 21:54:25,901 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:54:30,355 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:54:31,670 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 21:54:31,670 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 21:54:31,670 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:54:31,670 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:54:31,671 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 21:54:31,671 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 21:54:31,671 - AIEngine - INFO - 🔧 Using config detected language: en
2025-06-07 21:54:31,671 - AIEngine - INFO - 🔧 Using config detected language: en
2025-06-07 21:54:31,671 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 21:54:31,671 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 21:54:31,671 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:54:31,671 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:54:31,787 - TTSEngine - ERROR - Error during speech: run loop already started
2025-06-07 21:54:33,275 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:54:37,849 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:54:41,634 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:54:42,691 - AIEngine - INFO - LLM response generated successfully: Hello there! How can I assist you today?...
2025-06-07 21:54:42,691 - AIEngine - INFO - LLM generated successful response: Hello there! How can I assist you today?...
2025-06-07 21:54:42,695 - AIEngine - INFO - Final response generated: Hello there! How can I assist you today?...
2025-06-07 21:54:44,616 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:54:50,109 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:54:54,709 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:00,033 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:04,077 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:07,856 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:09,114 - AIEngine - INFO - LLM response generated successfully: Hello there! It seems like you've asked a question...
2025-06-07 21:55:09,114 - AIEngine - INFO - LLM generated successful response: Hello there! It seems like you've asked a question...
2025-06-07 21:55:09,118 - AIEngine - INFO - Final response generated: Hello there! It seems like you've asked a question...
2025-06-07 21:55:14,344 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:17,827 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:20,616 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:27,030 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:29,844 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:34,257 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:38,004 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:40,567 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:44,873 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:48,393 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:52,257 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:55:55,070 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:56:04,622 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:56:06,787 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:56:29,460 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 21:56:29,462 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 21:56:29,467 - MemorySystem - INFO - Loaded 15 recent conversations
2025-06-07 21:56:29,467 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 21:56:29,467 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 21:56:29,469 - ModelManager - INFO - No existing models config found
2025-06-07 21:56:29,469 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 21:56:29,470 - AIEngine - INFO - Loaded 27 learned response patterns
2025-06-07 21:56:29,470 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 21:56:29,471 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 21:56:29,471 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 21:56:29,471 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 21:56:29,471 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 21:56:29,471 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 21:56:29,733 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 21:56:29,734 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 21:56:29,734 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 21:56:29,734 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 21:56:55,535 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 21:56:55,537 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 21:56:55,537 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 21:56:55,674 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 21:56:55,728 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 21:56:55,860 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 21:56:55,860 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 21:56:58,851 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 2.013286537731107
2025-06-07 21:56:58,865 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 21:56:58,866 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 21:56:58,866 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 21:56:58,866 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 21:56:58,866 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 21:56:58,866 - STTEngine - INFO - 🌍 Fixed language mode: en
2025-06-07 21:56:58,866 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 21:56:58,867 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 21:56:58,867 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 21:56:59,108 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 21:56:59,108 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 21:56:59,109 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 21:57:02,357 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 21:57:06,014 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 21:57:10,200 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 21:57:10,200 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 21:57:10,200 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 21:57:15,497 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:57:19,214 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:57:20,815 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 21:57:20,815 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 21:57:20,815 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:57:20,815 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 21:57:20,816 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 21:57:20,816 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 21:57:20,816 - AIEngine - INFO - 🔧 Using config detected language: en
2025-06-07 21:57:20,816 - AIEngine - INFO - 🔧 Using config detected language: en
2025-06-07 21:57:20,816 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 21:57:20,816 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 21:57:20,816 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:57:20,816 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 21:57:22,106 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:57:41,060 - AIEngine - INFO - LLM response generated successfully: Hello there! I'm Gideon, your AI assistant. How ca...
2025-06-07 21:57:41,060 - AIEngine - INFO - LLM generated successful response: Hello there! I'm Gideon, your AI assistant. How ca...
2025-06-07 21:57:41,070 - AIEngine - INFO - Final response generated: Hello there! I'm Gideon, your AI assistant. How ca...
2025-06-07 21:57:47,954 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:57:52,799 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:58:01,031 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:58:02,468 - AIEngine - INFO - LLM response generated successfully: Hello there! How can I assist you today? Please fe...
2025-06-07 21:58:02,468 - AIEngine - INFO - LLM generated successful response: Hello there! How can I assist you today? Please fe...
2025-06-07 21:58:02,472 - AIEngine - INFO - Final response generated: Hello there! How can I assist you today? Please fe...
2025-06-07 21:58:04,222 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:58:10,892 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:58:16,700 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:58:21,757 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:58:28,618 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:58:42,741 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:58:46,603 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:58:50,654 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:58:58,256 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:59:01,211 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:59:05,396 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:59:08,362 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:59:13,344 - STTEngine - INFO - 🎤 Heard: 'Nike'
2025-06-07 21:59:18,809 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:59:22,483 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:59:26,240 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:59:31,135 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:59:36,063 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:59:41,439 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:59:44,612 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:59:50,494 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:59:53,950 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 21:59:58,665 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 22:00:07,107 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 22:00:13,524 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 22:00:48,439 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 22:00:52,359 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 22:01:02,565 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 22:01:03,023 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 22:01:03,077 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 22:01:03,190 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 22:01:03,190 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 22:01:06,186 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-07 22:01:06,198 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 22:01:06,199 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 22:01:06,199 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 22:01:06,199 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 22:01:06,199 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 22:01:06,199 - STTEngine - INFO - 🌍 Fixed language mode: en
2025-06-07 22:01:06,199 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 22:01:06,199 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 22:01:06,199 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 22:01:06,418 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:01:06,418 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:01:06,561 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:01:06,561 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:01:09,795 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:01:09,795 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:01:09,906 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:01:09,906 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:01:12,617 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 22:01:12,732 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:01:12,733 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:01:12,843 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:01:12,843 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:01:16,560 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 22:01:16,564 - MemorySystem - INFO - Loaded 15 recent conversations
2025-06-07 22:01:16,564 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 22:01:16,564 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 22:01:16,566 - ModelManager - INFO - No existing models config found
2025-06-07 22:01:16,566 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:01:16,567 - AIEngine - INFO - Loaded 27 learned response patterns
2025-06-07 22:01:16,567 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:01:16,568 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:01:16,568 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:01:16,568 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:01:16,568 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:01:16,568 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:01:16,803 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:01:16,805 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:01:16,805 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:01:16,805 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:01:25,559 - STTEngine - ERROR - Offline recognition error: missing PocketSphinx module: ensure that PocketSphinx is set up correctly.
2025-06-07 22:01:27,152 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 22:01:27,538 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 22:01:27,628 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 22:01:37,658 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:01:37,658 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:01:37,658 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:01:37,698 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 22:01:37,731 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 22:01:37,800 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 22:01:37,800 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 22:01:40,797 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-07 22:01:40,809 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 22:01:40,809 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 22:01:40,810 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 22:01:40,810 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 22:01:40,810 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 22:01:40,810 - STTEngine - INFO - 🌍 Fixed language mode: en
2025-06-07 22:01:40,810 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 22:01:40,810 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 22:01:40,811 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 22:01:40,813 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:01:40,813 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:01:40,813 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 22:01:44,032 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 22:01:47,040 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 22:01:51,224 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 22:01:51,224 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 22:01:51,225 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 22:02:15,881 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 22:02:15,882 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 22:02:15,884 - MemorySystem - INFO - Loaded 15 recent conversations
2025-06-07 22:02:15,884 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 22:02:15,884 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 22:02:15,885 - ModelManager - INFO - No existing models config found
2025-06-07 22:02:15,886 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:02:15,887 - AIEngine - INFO - Loaded 27 learned response patterns
2025-06-07 22:02:15,887 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:02:15,887 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:02:15,887 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:02:15,887 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:02:15,888 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:02:15,888 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:02:16,112 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:02:16,113 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:02:16,113 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:02:16,113 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:02:26,385 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:02:26,385 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:02:26,385 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:02:26,494 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 22:02:26,552 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 22:02:26,681 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 22:02:26,681 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 22:02:29,678 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 8.310473553040291
2025-06-07 22:02:29,690 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 22:02:29,691 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 22:02:29,691 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 22:02:29,691 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 22:02:29,691 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 22:02:29,691 - STTEngine - INFO - 🌍 Fixed language mode: en
2025-06-07 22:02:29,691 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 22:02:29,691 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 22:02:29,691 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 22:02:29,858 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:02:29,858 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:02:29,859 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 22:02:33,106 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 22:02:35,945 - STTEngine - INFO - Microphone test successful. Heard: hello
2025-06-07 22:02:38,070 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 22:02:38,071 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 22:02:38,071 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 22:03:24,294 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 22:03:24,295 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 22:03:24,296 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:03:24,296 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:03:24,296 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:03:24,296 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:03:24,297 - AIEngine - INFO - 🔧 Using config detected language: en
2025-06-07 22:03:24,297 - AIEngine - INFO - 🔧 Using config detected language: en
2025-06-07 22:03:24,297 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 22:03:24,297 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 22:03:24,297 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:03:24,297 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:04:13,050 - AIEngine - INFO - LLM response generated successfully: Hello there! My apologies for any confusion, but I...
2025-06-07 22:04:13,050 - AIEngine - INFO - LLM generated successful response: Hello there! My apologies for any confusion, but I...
2025-06-07 22:04:13,061 - AIEngine - INFO - Final response generated: Hello there! My apologies for any confusion, but I...
2025-06-07 22:04:19,827 - AIEngine - INFO - LLM response generated successfully: Hello there! How can I assist you today?...
2025-06-07 22:04:19,828 - AIEngine - INFO - LLM generated successful response: Hello there! How can I assist you today?...
2025-06-07 22:04:19,831 - AIEngine - INFO - Final response generated: Hello there! How can I assist you today?...
2025-06-07 22:11:55,919 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:11:55,919 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:11:55,920 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:11:55,920 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:11:55,920 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:11:55,920 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:11:55,920 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:11:56,165 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:11:56,166 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:11:56,166 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:11:56,166 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:13:19,009 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:13:19,015 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:13:19,015 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:13:19,017 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:13:19,018 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:13:19,018 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:13:19,018 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:13:19,023 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:13:19,023 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-07 22:13:19,024 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:13:19,024 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:13:19,025 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:13:19,025 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:13:19,025 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:13:31,098 - AIEngine - INFO - LLM response generated successfully: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:13:31,098 - AIEngine - INFO - LLM generated successful response: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:13:31,098 - AIEngine - INFO - Final response generated: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:13:31,099 - AIEngine - INFO - 🌍 Rule-based response for language: en
2025-06-07 22:13:31,112 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:13:31,112 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:13:31,113 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:13:31,113 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:13:31,113 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:13:31,113 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:13:31,113 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:13:31,371 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:13:31,372 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:13:31,372 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:13:31,372 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:13:38,545 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:13:38,545 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:13:38,546 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:13:38,546 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:13:38,546 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-07 22:13:38,546 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:13:38,546 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:13:38,546 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:13:38,547 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:13:38,547 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:13:49,889 - AIEngine - INFO - LLM response generated successfully: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:13:49,889 - AIEngine - INFO - LLM generated successful response: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:13:49,889 - AIEngine - INFO - Final response generated: مرحباً! كيف يمكنني مساعدتك اليوم؟ I'm always here ...
2025-06-07 22:13:49,892 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:13:49,893 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:13:49,893 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:13:49,893 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:13:49,893 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:13:49,893 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:13:49,894 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:13:50,114 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:13:50,115 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:13:50,115 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:13:50,115 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:13:57,053 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:13:57,054 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:13:57,054 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:13:57,054 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:13:57,054 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-07 22:13:57,054 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:13:57,054 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:13:57,055 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:13:57,055 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:13:57,055 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:14:09,614 - AIEngine - INFO - LLM response generated successfully: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:14:09,614 - AIEngine - INFO - LLM generated successful response: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:14:09,614 - AIEngine - INFO - Final response generated: مرحباً! كيف يمكنني مساعدتك اليوم؟ I'm always here ...
2025-06-07 22:14:09,615 - AIEngine - INFO - Generating response for: 'ما اسمك؟'
2025-06-07 22:14:09,615 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:14:09,615 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:14:09,615 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:14:09,615 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:14:09,615 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:14:26,778 - AIEngine - INFO - LLM response generated successfully: اسمي جيديون، وأنا مساعد ذكي هنا لمساعدتك!...
2025-06-07 22:14:26,778 - AIEngine - INFO - LLM generated successful response: اسمي جيديون، وأنا مساعد ذكي هنا لمساعدتك!...
2025-06-07 22:14:26,778 - AIEngine - INFO - Final response generated: اسمي جيديون، وأنا مساعد ذكي هنا لمساعدتك!...
2025-06-07 22:14:26,779 - AIEngine - INFO - Generating response for: 'كيف حالك؟'
2025-06-07 22:14:26,779 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:14:26,779 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:14:26,779 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:14:26,779 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:14:26,779 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:14:44,124 - AIEngine - INFO - LLM response generated successfully: الحمد لله، أنا بخير! كما حالك، كيف حالك أنت؟...
2025-06-07 22:14:44,124 - AIEngine - INFO - LLM generated successful response: الحمد لله، أنا بخير! كما حالك، كيف حالك أنت؟...
2025-06-07 22:14:44,125 - AIEngine - INFO - Final response generated: الحمد لله، أنا بخير! كما حالك، كيف حالك أنت؟ Feel ...
2025-06-07 22:14:44,125 - AIEngine - INFO - Generating response for: 'من أنت؟'
2025-06-07 22:14:44,125 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:14:44,125 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:14:44,125 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:14:44,125 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:14:44,125 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:15:40,706 - STTEngine - INFO - 🎤 Heard: 'I love my daddy'
2025-06-07 22:18:24,182 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:18:24,182 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:18:24,183 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:18:24,183 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:18:24,183 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:18:24,183 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:18:24,183 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:18:24,427 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:18:24,429 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:18:24,429 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:18:24,429 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:18:33,717 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:18:33,717 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:18:33,717 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:18:33,718 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:18:33,718 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-07 22:18:33,718 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:18:33,718 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:18:33,719 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:18:33,719 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:18:33,719 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:18:48,069 - AIEngine - INFO - LLM response generated successfully: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:18:48,069 - AIEngine - INFO - LLM generated successful response: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:18:48,070 - AIEngine - INFO - Final response generated: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:18:48,070 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:18:48,070 - AIEngine - INFO - Generating response for: 'ما اسمك؟'
2025-06-07 22:18:48,070 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:18:48,071 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:18:48,071 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:18:48,071 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:18:48,071 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:19:21,751 - AIEngine - INFO - LLM response generated successfully: مرحبا! اسمي جيديون، وأنا مساعد ذكي هنا لمساعدتك في...
2025-06-07 22:19:21,751 - AIEngine - INFO - LLM generated successful response: مرحبا! اسمي جيديون، وأنا مساعد ذكي هنا لمساعدتك في...
2025-06-07 22:19:21,752 - AIEngine - INFO - Final response generated: مرحبا! اسمي جيديون، وأنا مساعد ذكي هنا لمساعدتك في...
2025-06-07 22:19:21,752 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:19:21,752 - AIEngine - INFO - Generating response for: 'كيف حالك؟'
2025-06-07 22:19:21,752 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:19:21,752 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:19:21,752 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:19:21,753 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:19:21,753 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:20:03,641 - AIEngine - INFO - LLM response generated successfully: الحمد لله، أنا بخير! شكرًا على اهتمامك، كيف حالك أ...
2025-06-07 22:20:03,641 - AIEngine - INFO - LLM generated successful response: الحمد لله، أنا بخير! شكرًا على اهتمامك، كيف حالك أ...
2025-06-07 22:20:03,641 - AIEngine - INFO - Final response generated: الحمد لله، أنا بخير! شكرًا على اهتمامك، كيف حالك أ...
2025-06-07 22:20:03,641 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:20:03,642 - AIEngine - INFO - Generating response for: 'من أنت؟'
2025-06-07 22:20:03,642 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:20:03,642 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:20:03,642 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:20:03,642 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:20:03,642 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:20:42,229 - AIEngine - INFO - LLM response generated successfully: مرحباً، أنا جيديون، مساعد ذكيف يتواصل باللغة العرب...
2025-06-07 22:20:42,229 - AIEngine - INFO - LLM generated successful response: مرحباً، أنا جيديون، مساعد ذكيف يتواصل باللغة العرب...
2025-06-07 22:20:42,230 - AIEngine - INFO - Final response generated: مرحباً، أنا جيديون، مساعد ذكيف يتواصل باللغة العرب...
2025-06-07 22:20:42,230 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:20:42,230 - AIEngine - INFO - Generating response for: 'أخبرني عن نفسك'
2025-06-07 22:20:42,230 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:20:42,230 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:20:42,230 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:20:42,230 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:20:42,231 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:21:32,535 - AIEngine - INFO - LLM response generated successfully: مرحبا! أنا جيدون، مساعد ذكيف يتقدم الخدمة باللغة ا...
2025-06-07 22:21:32,536 - AIEngine - INFO - LLM generated successful response: مرحبا! أنا جيدون، مساعد ذكيف يتقدم الخدمة باللغة ا...
2025-06-07 22:21:32,536 - AIEngine - INFO - Final response generated: مرحبا! أنا جيدون، مساعد ذكيف يتقدم الخدمة باللغة ا...
2025-06-07 22:21:32,542 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:21:32,542 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:21:32,543 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:21:32,543 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:21:32,543 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:21:32,543 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:21:32,544 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:21:32,782 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:21:32,784 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:21:32,784 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:21:32,784 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:21:40,064 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:21:40,064 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:21:40,064 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:21:40,064 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-07 22:21:40,064 - AIEngine - INFO - Generating response for: 'hello'
2025-06-07 22:21:40,064 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:21:40,065 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-07 22:21:40,065 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-07 22:21:40,065 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 22:21:40,065 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:21:50,380 - AIEngine - INFO - LLM response generated successfully: Hello! How can I help you today?...
2025-06-07 22:21:50,380 - AIEngine - INFO - LLM generated successful response: Hello! How can I help you today?...
2025-06-07 22:21:50,380 - AIEngine - INFO - Final response generated: Hello! How can I help you today?...
2025-06-07 22:21:50,380 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-07 22:21:50,380 - AIEngine - INFO - Generating response for: 'what is your name?'
2025-06-07 22:21:50,381 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:21:50,381 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-07 22:21:50,381 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-07 22:21:50,381 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 22:21:50,381 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 22:22:02,296 - AIEngine - INFO - LLM response generated successfully: I'm Gideon, your AI assistant here to help you....
2025-06-07 22:22:02,297 - AIEngine - INFO - LLM generated successful response: I'm Gideon, your AI assistant here to help you....
2025-06-07 22:22:02,297 - AIEngine - INFO - Final response generated: I'm Gideon, your AI assistant here to help you....
2025-06-07 22:22:02,297 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-07 22:22:02,297 - AIEngine - INFO - Generating response for: 'how are you?'
2025-06-07 22:22:02,297 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:22:02,297 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-07 22:22:02,297 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-07 22:22:02,298 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 22:22:02,298 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:22:12,446 - AIEngine - INFO - LLM response generated successfully: I'm doing great! How can I help you today?...
2025-06-07 22:22:12,446 - AIEngine - INFO - LLM generated successful response: I'm doing great! How can I help you today?...
2025-06-07 22:22:12,446 - AIEngine - INFO - Final response generated: I'm doing great! How can I help you today? Is ther...
2025-06-07 22:22:12,447 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-07 22:22:12,447 - AIEngine - INFO - Generating response for: 'who are you?'
2025-06-07 22:22:12,447 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:22:12,447 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-07 22:22:12,447 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-07 22:22:12,447 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 22:22:12,447 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 22:22:48,526 - AIEngine - INFO - LLM response generated successfully: Hello! I'm Gideon, your AI assistant here to help ...
2025-06-07 22:22:48,527 - AIEngine - INFO - LLM generated successful response: Hello! I'm Gideon, your AI assistant here to help ...
2025-06-07 22:22:48,527 - AIEngine - INFO - Final response generated: Hello! I'm Gideon, your AI assistant here to help ...
2025-06-07 22:22:48,527 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-07 22:22:48,527 - AIEngine - INFO - Generating response for: 'tell me about yourself'
2025-06-07 22:22:48,527 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:22:48,528 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-07 22:22:48,528 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-07 22:22:48,528 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 22:22:48,528 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 22:23:32,759 - AIEngine - INFO - LLM response generated successfully: Hello! I'm Gideon, a female AI assistant designed ...
2025-06-07 22:23:32,760 - AIEngine - INFO - LLM generated successful response: Hello! I'm Gideon, a female AI assistant designed ...
2025-06-07 22:23:32,760 - AIEngine - INFO - Final response generated: Hello! I'm Gideon, a female AI assistant designed ...
2025-06-07 22:23:32,765 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:23:32,766 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:23:32,767 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:23:32,767 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:23:32,767 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:23:32,767 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:23:32,767 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:23:33,021 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:23:33,022 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:23:33,023 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:23:33,023 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:23:40,202 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:23:40,202 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:23:40,203 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:23:40,203 - AIEngine - INFO - 🌍 Response language set to: auto
2025-06-07 22:23:40,203 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-07 22:23:40,203 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:23:40,203 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:23:40,204 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:23:40,204 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:23:40,204 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:24:00,404 - AIEngine - INFO - LLM response generated successfully: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:24:00,405 - AIEngine - INFO - LLM generated successful response: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:24:00,405 - AIEngine - INFO - Final response generated: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:24:00,406 - AIEngine - INFO - Generating response for: 'hello'
2025-06-07 22:24:00,406 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:24:00,406 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-07 22:24:00,406 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:24:00,406 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:24:00,406 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:24:13,384 - AIEngine - INFO - LLM response generated successfully: Hello! How can I help you today?...
2025-06-07 22:24:13,385 - AIEngine - INFO - LLM generated successful response: Hello! How can I help you today?...
2025-06-07 22:24:13,385 - AIEngine - INFO - Final response generated: Hello! How can I help you today?...
2025-06-07 22:24:13,385 - AIEngine - INFO - Generating response for: 'ما اسمك؟'
2025-06-07 22:24:13,385 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:24:13,385 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:24:13,386 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:24:13,386 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:24:13,386 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:25:00,942 - AIEngine - INFO - LLM response generated successfully: مرحباً، اسمي جديون، وأنا مساعد ذكي هنا لمساعدتك في...
2025-06-07 22:25:00,942 - AIEngine - INFO - LLM generated successful response: مرحباً، اسمي جديون، وأنا مساعد ذكي هنا لمساعدتك في...
2025-06-07 22:25:00,942 - AIEngine - INFO - Final response generated: مرحباً، اسمي جديون، وأنا مساعد ذكي هنا لمساعدتك في...
2025-06-07 22:25:00,942 - AIEngine - INFO - Generating response for: 'what is your name?'
2025-06-07 22:25:00,943 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:25:00,943 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-07 22:25:00,943 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:25:00,943 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:25:00,943 - AIEngine - INFO - Generating LLM response with max_tokens=200, temperature=0.7
2025-06-07 22:26:06,809 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:26:06,810 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:26:06,810 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:26:06,810 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:26:06,810 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:26:06,810 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:26:06,811 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:26:07,059 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:26:07,061 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:26:07,061 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:26:07,061 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:26:15,270 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:26:15,270 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:26:15,270 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:26:15,270 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:26:15,270 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-07 22:26:15,271 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:26:15,271 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:26:15,271 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:26:15,271 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:26:15,272 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:26:28,264 - AIEngine - INFO - LLM response generated successfully: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:26:28,265 - AIEngine - INFO - LLM generated successful response: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:26:28,265 - AIEngine - INFO - Final response generated: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:26:28,265 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:26:28,265 - AIEngine - INFO - Generating response for: 'ما اسمك؟'
2025-06-07 22:26:28,266 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:26:28,266 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:26:28,266 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:26:28,266 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:26:28,266 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:27:05,458 - AIEngine - INFO - LLM response generated successfully: مرحباً،اسمي جديون. أنا مساعد ذكي وودود، وعلي أن أك...
2025-06-07 22:27:05,458 - AIEngine - INFO - LLM generated successful response: مرحباً،اسمي جديون. أنا مساعد ذكي وودود، وعلي أن أك...
2025-06-07 22:27:05,458 - AIEngine - INFO - Final response generated: مرحباً،اسمي جديون. أنا مساعد ذكي وودود، وعلي أن أك...
2025-06-07 22:27:05,459 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:27:05,459 - AIEngine - INFO - Generating response for: 'كيف حالك؟'
2025-06-07 22:27:05,459 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:27:05,459 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:27:05,459 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:27:05,459 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:27:05,459 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:27:26,992 - AIEngine - INFO - LLM response generated successfully: الحمد لله، أنا بخير! شكرًا على السؤال. فيم يمكنني ...
2025-06-07 22:27:26,993 - AIEngine - INFO - LLM generated successful response: الحمد لله، أنا بخير! شكرًا على السؤال. فيم يمكنني ...
2025-06-07 22:27:26,993 - AIEngine - INFO - Final response generated: الحمد لله، أنا بخير! شكرًا على السؤال. فيم يمكنني ...
2025-06-07 22:27:26,993 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:27:26,993 - AIEngine - INFO - Generating response for: 'من أنت؟'
2025-06-07 22:27:26,993 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:27:26,994 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:27:26,994 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:27:26,994 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:27:26,994 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:27:35,984 - STTEngine - INFO - 🎤 Heard: 'Mohabbat Nibha Dena ji'
2025-06-07 22:27:50,935 - STTEngine - INFO - 🎤 Heard: 'chalali'
2025-06-07 22:27:55,621 - STTEngine - INFO - 🎤 Heard: 'Nisha'
2025-06-07 22:27:59,430 - STTEngine - INFO - 🎤 Heard: 'Machhali marva'
2025-06-07 22:27:59,704 - AIEngine - INFO - LLM response generated successfully: مرحبا! أنا جيدون، مساعد ذكيف يفي خدمتك. سأقوم بتقد...
2025-06-07 22:27:59,705 - AIEngine - INFO - LLM generated successful response: مرحبا! أنا جيدون، مساعد ذكيف يفي خدمتك. سأقوم بتقد...
2025-06-07 22:27:59,705 - AIEngine - INFO - Final response generated: مرحبا! أنا جيدون، مساعد ذكيف يفي خدمتك. سأقوم بتقد...
2025-06-07 22:27:59,705 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:27:59,705 - AIEngine - INFO - Generating response for: 'أخبرني عن نفسك'
2025-06-07 22:27:59,706 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:27:59,706 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:27:59,706 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:27:59,706 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:27:59,706 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:28:05,820 - STTEngine - INFO - 🎤 Heard: '16 sal'
2025-06-07 22:28:50,748 - AIEngine - INFO - LLM response generated successfully: مرحبا! أنا جيدون، مساعد ذكيفه متقدم ووديع. مهمتي ه...
2025-06-07 22:28:50,750 - AIEngine - INFO - LLM generated successful response: مرحبا! أنا جيدون، مساعد ذكيفه متقدم ووديع. مهمتي ه...
2025-06-07 22:28:50,750 - AIEngine - INFO - Final response generated: مرحبا! أنا جيدون، مساعد ذكيفه متقدم ووديع. مهمتي ه...
2025-06-07 22:28:50,756 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:28:50,757 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:28:50,757 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:28:50,757 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:28:50,758 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:28:50,758 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:28:50,758 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:28:51,029 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:28:51,031 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:28:51,031 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:28:51,031 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:28:58,540 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:28:58,541 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:28:58,541 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:28:58,541 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:28:58,541 - AIEngine - INFO - Generating response for: 'مرحبا جيديون'
2025-06-07 22:28:58,541 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:28:58,541 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:28:58,541 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:28:58,542 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:28:58,542 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:29:13,334 - AIEngine - INFO - LLM response generated successfully: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:29:13,334 - AIEngine - INFO - LLM generated successful response: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:29:13,334 - AIEngine - INFO - Final response generated: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:29:13,335 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:29:13,335 - AIEngine - INFO - Generating response for: 'ما اسمك؟'
2025-06-07 22:29:13,335 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:29:13,335 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:29:13,335 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-07 22:29:13,335 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:29:13,335 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:29:37,887 - STTEngine - INFO - 🎤 Heard: 'fishes Mahadev temple'
2025-06-07 22:29:41,815 - AIEngine - INFO - LLM response generated successfully: مرحبا! اسمي جيديون وأنا مساعد ذكي هنا لمساعدتك في ...
2025-06-07 22:29:41,815 - AIEngine - INFO - LLM generated successful response: مرحبا! اسمي جيديون وأنا مساعد ذكي هنا لمساعدتك في ...
2025-06-07 22:29:41,816 - AIEngine - INFO - Final response generated: مرحبا! اسمي جيديون وأنا مساعد ذكي هنا لمساعدتك في ...
2025-06-07 22:29:41,816 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-07 22:29:41,817 - AIEngine - INFO - Generating response for: 'hello'
2025-06-07 22:29:41,817 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:29:41,817 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-07 22:29:41,817 - AIEngine - INFO - 🎯 Using explicitly set response language: en
2025-06-07 22:29:41,817 - AIEngine - INFO - 🌍 Final prompt language: en
2025-06-07 22:29:41,817 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:29:51,553 - STTEngine - INFO - 🎤 Heard: 'reception'
2025-06-07 22:30:02,093 - STTEngine - INFO - 🎤 Heard: 'Star Utsav ka'
2025-06-07 22:30:04,923 - STTEngine - INFO - 🎤 Heard: 'vitamin'
2025-06-07 22:30:09,024 - STTEngine - INFO - 🎤 Heard: 'Yes Boss'
2025-06-07 22:30:18,035 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 22:30:18,036 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 22:30:18,041 - MemorySystem - INFO - Loaded 15 recent conversations
2025-06-07 22:30:18,041 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 22:30:18,041 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 22:30:18,043 - ModelManager - INFO - No existing models config found
2025-06-07 22:30:18,043 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:30:18,044 - AIEngine - INFO - Loaded 27 learned response patterns
2025-06-07 22:30:18,044 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:30:18,045 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:30:18,045 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:30:18,045 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:30:18,045 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:30:18,045 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:30:18,273 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:30:18,275 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:30:18,275 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:30:18,275 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:30:22,268 - STTEngine - INFO - 🎤 Heard: 'Anand Resort'
2025-06-07 22:30:26,240 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:30:26,240 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:30:26,240 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:30:26,352 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 22:30:26,411 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 22:30:26,539 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 22:30:26,539 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 22:30:29,530 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 558.6318861132708
2025-06-07 22:30:29,542 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 22:30:29,543 - STTEngine - INFO -    Energy threshold: 558.6318861132708
2025-06-07 22:30:29,543 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 22:30:29,543 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 22:30:29,543 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 22:30:29,543 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 22:30:29,543 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 22:30:29,544 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 22:30:29,544 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 22:30:29,751 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:30:29,751 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:30:29,751 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 22:30:30,833 - STTEngine - INFO - 🎤 Heard: 'set alarm in the'
2025-06-07 22:30:32,998 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 22:30:36,166 - STTEngine - INFO - 🎤 Heard: 'Robert bill Avatar'
2025-06-07 22:30:37,744 - STTEngine - INFO - Microphone test successful. Heard: كلها حديده الحديده يوصلها
2025-06-07 22:30:39,865 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 22:30:39,865 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 22:30:39,866 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 22:30:45,459 - STTEngine - INFO - 🎤 Heard: 'lovely lovely'
2025-06-07 22:30:49,802 - STTEngine - INFO - 🎤 Heard: 'يتجاوز'
2025-06-07 22:30:50,122 - STTEngine - INFO - 🎤 Heard: 'hello tune recharge kab'
2025-06-07 22:31:00,334 - STTEngine - INFO - 🎤 Heard: 'Bhagalpur'
2025-06-07 22:31:07,559 - STTEngine - INFO - 🎤 Heard: 'غبي'
2025-06-07 22:31:10,167 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 22:31:10,167 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 22:31:10,168 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:31:10,168 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:31:10,168 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:31:10,168 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:31:10,168 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:31:10,168 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:31:10,168 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:31:10,169 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:31:10,169 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:31:10,169 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:31:58,559 - AIEngine - INFO - LLM response generated successfully: مرحبا! أنا جديون، مساعد ذكي متعاون. سأقوم بنصحك وإ...
2025-06-07 22:31:58,560 - AIEngine - INFO - LLM generated successful response: مرحبا! أنا جديون، مساعد ذكي متعاون. سأقوم بنصحك وإ...
2025-06-07 22:31:58,570 - AIEngine - INFO - Final response generated: مرحبا! أنا جديون، مساعد ذكي متعاون. سأقوم بنصحك وإ...
2025-06-07 22:32:06,725 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 22:32:06,801 - AIEngine - INFO - LLM response generated successfully: هلا! لمن ساعدك في شيء ما?...
2025-06-07 22:32:06,801 - AIEngine - INFO - LLM generated successful response: هلا! لمن ساعدك في شيء ما?...
2025-06-07 22:32:06,804 - AIEngine - INFO - Final response generated: هلا! لمن ساعدك في شيء ما?...
2025-06-07 22:32:06,874 - STTEngine - INFO - Stopped continuous listening
2025-06-07 22:32:07,737 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 22:32:07,809 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 22:32:10,914 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 22:32:11,034 - STTEngine - INFO - Stopped continuous listening
2025-06-07 22:32:11,628 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 22:32:11,723 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 22:34:58,296 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 22:34:58,301 - MemorySystem - INFO - Loaded 15 recent conversations
2025-06-07 22:34:58,301 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 22:34:58,301 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 22:34:58,302 - ModelManager - INFO - No existing models config found
2025-06-07 22:34:58,303 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:34:58,304 - AIEngine - INFO - Loaded 27 learned response patterns
2025-06-07 22:34:58,304 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:34:58,305 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:34:58,305 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:34:58,305 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:34:58,305 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:34:58,305 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:34:58,546 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:34:58,548 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:34:58,548 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:34:58,548 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:35:21,083 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:35:21,087 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:35:21,087 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:35:21,249 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 22:35:21,304 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 22:35:21,430 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 22:35:21,430 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 22:35:24,425 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0247738843526626
2025-06-07 22:35:24,443 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 22:35:24,443 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 22:35:24,443 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 22:35:24,443 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 22:35:24,443 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 22:35:24,444 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 22:35:24,444 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 22:35:24,444 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 22:35:24,444 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 22:35:24,658 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:35:24,658 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:35:24,658 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 22:35:27,909 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 22:35:32,064 - STTEngine - INFO - Microphone test successful. Heard: هلا
2025-06-07 22:35:34,185 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 22:35:34,185 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 22:35:34,185 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 22:35:34,186 - GideonCore - INFO - Processing text: مرحبا
2025-06-07 22:35:34,186 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-07 22:35:34,186 - GideonCore - INFO - 🌍 Set AI engine response language directly to: ar
2025-06-07 22:35:34,186 - GideonCore - INFO - 🌍 Detected input language: ar
2025-06-07 22:35:34,194 - GideonCore - INFO - CONVERSATION - User: مرحبا
2025-06-07 22:35:34,194 - GideonCore - INFO - CONVERSATION - AI: مرحباً! أهلاً وسهلاً بك
2025-06-07 22:35:35,443 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 22:35:35,445 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 22:35:35,446 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 22:35:35,446 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 22:35:35,447 - ModelManager - INFO - No existing models config found
2025-06-07 22:35:35,448 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:35:35,449 - AIEngine - INFO - Loaded 27 learned response patterns
2025-06-07 22:35:35,449 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:35:35,449 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:35:35,450 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:35:35,450 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:35:35,450 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:35:35,450 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:35:35,708 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:35:35,709 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:35:35,710 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:35:35,710 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:35:42,115 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:35:42,115 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:35:42,115 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:35:42,116 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 22:35:42,116 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 22:35:42,120 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 22:35:42,120 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 22:35:45,116 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.210777312328982
2025-06-07 22:35:45,127 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 22:35:45,127 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 22:35:45,127 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 22:35:45,127 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 22:35:45,127 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 22:35:45,127 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 22:35:45,127 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 22:35:45,128 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 22:35:45,128 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 22:35:45,129 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:35:45,130 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:35:45,130 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 22:35:48,292 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 22:35:52,254 - STTEngine - INFO - Microphone test successful. Heard: هلو
2025-06-07 22:35:54,376 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 22:35:54,377 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 22:35:54,377 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 22:35:54,414 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 22:35:54,415 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 22:35:54,415 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 22:35:54,416 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 22:35:54,417 - ModelManager - INFO - No existing models config found
2025-06-07 22:35:54,417 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:35:54,418 - AIEngine - INFO - Loaded 27 learned response patterns
2025-06-07 22:35:54,418 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:35:54,419 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:35:54,419 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:35:54,419 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:35:54,419 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:35:54,419 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:35:54,648 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:35:54,649 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:35:54,649 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:35:54,649 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:36:01,887 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:36:01,887 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:36:01,887 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:36:01,888 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 22:36:01,888 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 22:36:01,892 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 22:36:01,892 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 22:36:04,886 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.6804727373763089
2025-06-07 22:36:04,897 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 22:36:04,897 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 22:36:04,897 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 22:36:04,897 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 22:36:04,897 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 22:36:04,898 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 22:36:04,898 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 22:36:04,898 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 22:36:04,898 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 22:36:04,900 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:36:04,900 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:36:04,900 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 22:36:08,029 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 22:36:11,847 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 22:36:16,033 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 22:36:16,033 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 22:36:16,033 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 22:37:58,949 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 22:37:58,954 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 22:37:58,954 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 22:37:58,954 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 22:37:58,955 - ModelManager - INFO - No existing models config found
2025-06-07 22:37:58,956 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:37:58,957 - AIEngine - INFO - Loaded 27 learned response patterns
2025-06-07 22:37:58,957 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:37:58,957 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:37:58,958 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:37:58,958 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:37:58,958 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:37:58,958 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:37:59,212 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:37:59,214 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:37:59,214 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:37:59,214 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:38:21,221 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:38:21,222 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:38:21,222 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:38:21,346 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 22:38:21,400 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 22:38:21,519 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 22:38:21,520 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 22:38:24,519 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 4.734921258203518
2025-06-07 22:38:24,531 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 22:38:24,531 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 22:38:24,531 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 22:38:24,531 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 22:38:24,531 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 22:38:24,531 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 22:38:24,532 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 22:38:24,532 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 22:38:24,532 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 22:38:24,724 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:38:24,725 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:38:24,725 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 22:38:27,972 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 22:38:31,629 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-07 22:38:35,816 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 22:38:35,816 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 22:38:35,816 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 22:38:37,729 - GideonCore - ERROR - Error processing text request: main thread is not in main loop
2025-06-07 22:38:38,813 - GideonCore - ERROR - Worker loop error: main thread is not in main loop
2025-06-07 22:38:54,438 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 22:38:54,440 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 22:38:54,440 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 22:38:54,440 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 22:38:54,441 - ModelManager - INFO - No existing models config found
2025-06-07 22:38:54,442 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:38:54,442 - AIEngine - INFO - Loaded 27 learned response patterns
2025-06-07 22:38:54,443 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:38:54,443 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:38:54,443 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:38:54,443 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:38:54,443 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:38:54,444 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:38:54,687 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:38:54,689 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:38:54,689 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:38:54,689 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:39:01,316 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:39:01,316 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:39:01,316 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:39:01,317 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 22:39:01,317 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 22:39:01,321 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 22:39:01,321 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 22:39:04,319 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 18.162527352269716
2025-06-07 22:39:04,330 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 22:39:04,331 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 22:39:04,331 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 22:39:04,331 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 22:39:04,331 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 22:39:04,331 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 22:39:04,331 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 22:39:04,331 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 22:39:04,331 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 22:39:04,333 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:39:04,333 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:39:04,333 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 22:39:07,496 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 22:39:10,940 - STTEngine - INFO - Microphone test successful. Heard: خلاص
2025-06-07 22:39:13,061 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 22:39:13,061 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 22:39:13,061 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 22:39:14,457 - UltraProfessionalInterface - ERROR - Optimized processing error: main thread is not in main loop
2025-06-07 22:39:14,458 - AIEngine - INFO - Generating response for: 'مرحبا'
2025-06-07 22:39:14,458 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:39:14,458 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:39:14,458 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:39:14,459 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:39:14,459 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:42:48,172 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 22:42:48,179 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 22:42:48,180 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 22:42:48,180 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 22:42:48,181 - ModelManager - INFO - No existing models config found
2025-06-07 22:42:48,181 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:42:48,182 - AIEngine - INFO - Loaded 27 learned response patterns
2025-06-07 22:42:48,182 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:42:48,183 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:42:48,183 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:42:48,183 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:42:48,183 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:42:48,183 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:42:48,435 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:42:48,437 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:42:48,437 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:42:48,437 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:43:08,219 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:43:08,220 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:43:08,220 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:43:08,377 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 22:43:08,435 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 22:43:08,569 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 22:43:08,569 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 22:43:11,564 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 2.795466871806833
2025-06-07 22:43:11,578 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 22:43:11,578 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 22:43:11,578 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 22:43:11,579 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 22:43:11,579 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 22:43:11,579 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 22:43:11,579 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 22:43:11,579 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 22:43:11,579 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 22:43:11,792 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:43:11,793 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:43:11,793 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 22:43:15,045 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 22:43:20,620 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-07 22:43:24,811 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 22:43:24,811 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 22:43:24,811 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 22:43:26,651 - GideonCore - ERROR - Error processing text request: main thread is not in main loop
2025-06-07 22:43:27,742 - GideonCore - ERROR - Worker loop error: main thread is not in main loop
2025-06-07 22:43:40,395 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 22:43:40,395 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 22:43:40,395 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:43:40,396 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:43:40,396 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:43:40,396 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:43:40,396 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:43:40,396 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:43:40,397 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:43:40,397 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:43:40,397 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:43:40,397 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:43:41,600 - UltraProfessionalInterface - ERROR - Optimized processing error: main thread is not in main loop
2025-06-07 22:43:41,600 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 22:43:41,600 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:43:41,600 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:43:41,600 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:43:41,600 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:43:41,601 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:43:54,273 - AIEngine - INFO - LLM response generated successfully: مرحبة، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:43:54,274 - AIEngine - INFO - LLM generated successful response: مرحبة، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:43:54,278 - AIEngine - INFO - Final response generated: مرحبة، كيف يمكنني مساعدتك اليوم؟ Is there anything...
2025-06-07 22:43:58,458 - GideonCore - ERROR - Error processing text request: main thread is not in main loop
2025-06-07 22:43:59,551 - GideonCore - ERROR - Worker loop error: main thread is not in main loop
2025-06-07 22:44:16,352 - AIEngine - INFO - LLM response generated successfully: مرحباً، هل يمكنني مساعدتك في أي شيء؟ يرجى تذكيرني ...
2025-06-07 22:44:16,352 - AIEngine - INFO - LLM generated successful response: مرحباً، هل يمكنني مساعدتك في أي شيء؟ يرجى تذكيرني ...
2025-06-07 22:44:16,356 - AIEngine - INFO - Final response generated: مرحباً، هل يمكنني مساعدتك في أي شيء؟ يرجى تذكيرني ...
2025-06-07 22:44:27,001 - AIEngine - INFO - LLM response generated successfully: مرحباً، هل يمكنني مساعدتك في أي شيء معين؟...
2025-06-07 22:44:27,002 - AIEngine - INFO - LLM generated successful response: مرحباً، هل يمكنني مساعدتك في أي شيء معين؟...
2025-06-07 22:44:27,005 - AIEngine - INFO - Final response generated: مرحباً، هل يمكنني مساعدتك في أي شيء معين؟ I'm alwa...
2025-06-07 22:44:28,092 - UltraProfessionalInterface - ERROR - Direct AI processing error: main thread is not in main loop
2025-06-07 22:44:29,186 - GideonCore - ERROR - Error processing text request: main thread is not in main loop
2025-06-07 22:44:30,281 - GideonCore - ERROR - Worker loop error: main thread is not in main loop
2025-06-07 22:44:51,552 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 22:44:51,557 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 22:44:51,557 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 22:44:51,557 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 22:44:51,558 - ModelManager - INFO - No existing models config found
2025-06-07 22:44:51,559 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:44:51,560 - AIEngine - INFO - Loaded 27 learned response patterns
2025-06-07 22:44:51,560 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:44:51,561 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:44:51,561 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:44:51,561 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:44:51,561 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:44:51,561 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:44:51,793 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:44:51,794 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:44:51,794 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:44:51,794 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:44:59,478 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:44:59,478 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:44:59,478 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:44:59,586 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 22:44:59,640 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 22:44:59,756 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 22:44:59,757 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 22:45:02,756 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 14.322805348439871
2025-06-07 22:45:02,769 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 22:45:02,769 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 22:45:02,770 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 22:45:02,770 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 22:45:02,770 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 22:45:02,770 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 22:45:02,770 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 22:45:02,770 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 22:45:02,770 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 22:45:02,945 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:45:02,946 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:45:02,946 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 22:45:06,195 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 22:45:09,128 - STTEngine - INFO - Microphone test successful. Heard: هلو
2025-06-07 22:45:11,252 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 22:45:11,252 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 22:45:11,252 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 22:45:49,765 - AIEngine - INFO - Generating response for: 'اهلا'
2025-06-07 22:45:49,766 - AIEngine - INFO - Generating response for: 'اهلا'
2025-06-07 22:45:49,766 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:45:49,766 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:45:49,766 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:45:49,767 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:45:49,767 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:45:49,767 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:45:49,767 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:45:49,767 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:45:49,767 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:45:49,768 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:46:22,115 - AIEngine - INFO - LLM response generated successfully: مرحباً! لمن سعدني بالتواصل معك. إذا كان لديك أي أس...
2025-06-07 22:46:22,116 - AIEngine - INFO - LLM generated successful response: مرحباً! لمن سعدني بالتواصل معك. إذا كان لديك أي أس...
2025-06-07 22:46:22,121 - AIEngine - INFO - Final response generated: مرحباً! لمن سعدني بالتواصل معك. إذا كان لديك أي أس...
2025-06-07 22:46:33,698 - AIEngine - INFO - LLM response generated successfully: أهلاً وسهلاً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:46:33,699 - AIEngine - INFO - LLM generated successful response: أهلاً وسهلاً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:46:33,702 - AIEngine - INFO - Final response generated: أهلاً وسهلاً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:46:56,264 - STTEngine - INFO - 🎤 Heard: 'صاحبي'
2025-06-07 22:47:01,815 - STTEngine - INFO - 🎤 Heard: 'اليوم عرفت قصه غزوه احد'
2025-06-07 22:47:06,381 - STTEngine - INFO - 🎤 Heard: 'ربي ما يحب كل مختال فخور'
2025-06-07 22:47:11,871 - STTEngine - INFO - 🎤 Heard: 'الا'
2025-06-07 22:47:26,647 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 22:47:26,648 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 22:47:26,650 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 22:47:26,650 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 22:47:26,650 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 22:47:26,651 - ModelManager - INFO - No existing models config found
2025-06-07 22:47:26,652 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:47:26,653 - AIEngine - INFO - Loaded 28 learned response patterns
2025-06-07 22:47:26,653 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:47:26,653 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:47:26,653 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:47:26,653 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:47:26,654 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:47:26,654 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:47:26,901 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:47:26,902 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:47:26,903 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:47:26,903 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:47:36,331 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:47:36,331 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:47:36,332 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:47:36,442 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 22:47:36,498 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 22:47:36,615 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 22:47:36,616 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 22:47:39,609 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 527.0378690463679
2025-06-07 22:47:39,621 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 22:47:39,622 - STTEngine - INFO -    Energy threshold: 527.0378690463679
2025-06-07 22:47:39,622 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 22:47:39,622 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 22:47:39,622 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 22:47:39,622 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 22:47:39,622 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 22:47:39,622 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 22:47:39,623 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 22:47:39,793 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:47:39,793 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:47:39,793 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 22:47:43,037 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 22:47:48,168 - STTEngine - INFO - Microphone test successful. Heard: راح رسول الله ينفذ في اصحابه
2025-06-07 22:47:50,289 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 22:47:50,289 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 22:47:50,289 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 22:47:56,564 - STTEngine - INFO - 🎤 Heard: 'جره سيفا باكرا فنادى'
2025-06-07 22:48:01,125 - STTEngine - INFO - 🎤 Heard: 'فقام اليه الرجال يا'
2025-06-07 22:48:07,141 - STTEngine - INFO - 🎤 Heard: 'عوام حتى قام اليه ابو دج'
2025-06-07 22:48:11,491 - STTEngine - INFO - 🎤 Heard: 'وما حقه يا رسول الله'
2025-06-07 22:48:15,676 - STTEngine - INFO - 🎤 Heard: 'وجوه العدو حتى ين'
2025-06-07 22:48:20,128 - STTEngine - INFO - 🎤 Heard: 'ابو دجانه وعصب على'
2025-06-07 22:48:24,237 - STTEngine - INFO - 🎤 Heard: 'وكان اذا اعتصب بها'
2025-06-07 22:48:29,085 - STTEngine - INFO - 🎤 Heard: 'سيقاتل حتى الموت ثم خرج'
2025-06-07 22:48:34,365 - STTEngine - INFO - 🎤 Heard: 'في مشي ثعبان الصفين فقال رسول الله'
2025-06-07 22:48:38,797 - STTEngine - INFO - 🎤 Heard: 'يبغضها الله الا'
2025-06-07 22:48:44,631 - STTEngine - INFO - 🎤 Heard: 'الذين من قصه والله'
2025-06-07 22:49:01,827 - AIEngine - INFO - Generating response for: 'اهلا'
2025-06-07 22:49:01,828 - AIEngine - INFO - Generating response for: 'اهلا'
2025-06-07 22:49:01,828 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:49:01,828 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:49:01,828 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:49:01,829 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:49:01,829 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:49:01,829 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:49:01,829 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:49:01,829 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:49:01,829 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:49:01,829 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:49:12,707 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:49:14,502 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:49:15,461 - AIEngine - INFO - LLM response generated successfully: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:49:15,461 - AIEngine - INFO - LLM generated successful response: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:49:15,465 - AIEngine - INFO - Final response generated: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:49:20,732 - AIEngine - INFO - Generating response for: 'اهلا'
2025-06-07 22:49:20,733 - AIEngine - INFO - Generating response for: 'اهلا'
2025-06-07 22:49:20,733 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:49:20,733 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 22:49:20,734 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:49:20,734 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 22:49:20,734 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:49:20,734 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 22:49:20,734 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:49:20,734 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 22:49:20,735 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:49:20,735 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 22:49:24,050 - AIEngine - INFO - LLM response generated successfully: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:49:24,051 - AIEngine - INFO - LLM generated successful response: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:49:24,055 - AIEngine - INFO - Final response generated: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:49:33,920 - AIEngine - INFO - LLM response generated successfully: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:49:33,920 - AIEngine - INFO - LLM generated successful response: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:49:33,924 - AIEngine - INFO - Final response generated: مرحباً! كيف يمكنني مساعدتك اليوم؟...
2025-06-07 22:50:00,284 - AIEngine - INFO - LLM response generated successfully: هل لديك أي أسئلة أو potřebت مساعدة في شيء معين؟...
2025-06-07 22:50:00,285 - AIEngine - INFO - LLM generated successful response: هل لديك أي أسئلة أو potřebت مساعدة في شيء معين؟...
2025-06-07 22:50:00,290 - AIEngine - INFO - Final response generated: هل لديك أي أسئلة أو potřebت مساعدة في شيء معين؟...
2025-06-07 22:57:32,327 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 22:57:32,332 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 22:57:32,333 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 22:57:32,333 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 22:57:32,334 - ModelManager - INFO - No existing models config found
2025-06-07 22:57:32,335 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:57:32,335 - AIEngine - INFO - Loaded 28 learned response patterns
2025-06-07 22:57:32,336 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:57:32,336 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:57:32,336 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:57:32,336 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:57:32,336 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:57:32,336 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:57:32,594 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:57:32,595 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:57:32,595 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:57:32,595 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:58:48,760 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:58:48,765 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:58:48,765 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:58:48,899 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 22:58:48,953 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 22:58:49,070 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 22:58:49,070 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 22:58:52,062 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 2.3518341276705947
2025-06-07 22:58:52,074 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 22:58:52,074 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 22:58:52,074 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 22:58:52,074 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 22:58:52,074 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 22:58:52,075 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 22:58:52,075 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 22:58:52,075 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 22:58:52,075 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 22:58:52,333 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:58:52,334 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:58:52,334 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 22:58:55,579 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 22:58:57,323 - STTEngine - INFO - 🎤 Heard: 'الو'
2025-06-07 22:58:58,584 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 22:59:02,767 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 22:59:02,768 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 22:59:02,768 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 22:59:03,509 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-07 22:59:03,509 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-07 22:59:03,549 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧪 Testing chat widget...
2025-06-07 22:59:03,549 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [22:59:03] ⚙️ System: 🧪 Testin...
2025-06-07 22:59:03,563 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 22:59:03,563 - UltraProfessionalInterface - INFO - 📝 Adding message: Test -> مرحبا! هذا اختبار للعربية...
2025-06-07 22:59:03,566 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﺔﻴﺑﺮﻌﻠﻟ ﺭﺎﺒﺘﺧﺍ ﺍﺬﻫ !ﺎﺒﺣﺮﻣ :Tes...
2025-06-07 22:59:03,570 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 22:59:03,570 - UltraProfessionalInterface - INFO - 📝 Adding message: Test -> Hello! This is an English test...
2025-06-07 22:59:03,570 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [22:59:03] 💬 Test: Hello! This...
2025-06-07 22:59:03,571 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 22:59:04,837 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 22:59:04,845 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 22:59:04,845 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 22:59:04,845 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 22:59:04,847 - ModelManager - INFO - No existing models config found
2025-06-07 22:59:04,848 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:59:04,848 - AIEngine - INFO - Loaded 28 learned response patterns
2025-06-07 22:59:04,849 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:59:04,849 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:59:04,849 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:59:04,849 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:59:04,849 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:59:04,850 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:59:05,118 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:59:05,119 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:59:05,119 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:59:05,119 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 22:59:11,733 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 22:59:11,734 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 22:59:11,734 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 22:59:11,770 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 22:59:11,802 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 22:59:11,872 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 22:59:11,872 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 22:59:14,863 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 9.358277049651248
2025-06-07 22:59:14,874 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 22:59:14,874 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 22:59:14,874 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 22:59:14,874 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 22:59:14,874 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 22:59:14,875 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 22:59:14,875 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 22:59:14,875 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 22:59:14,875 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 22:59:14,877 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 22:59:14,877 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 22:59:14,877 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 22:59:18,008 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 22:59:22,638 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-07 22:59:26,820 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 22:59:26,820 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 22:59:26,820 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 22:59:27,045 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-07 22:59:27,045 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-07 22:59:28,152 - GideonCore - ERROR - Error processing text request: main thread is not in main loop
2025-06-07 22:59:29,242 - GideonCore - ERROR - Worker loop error: main thread is not in main loop
2025-06-07 22:59:53,193 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 22:59:53,194 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 22:59:53,195 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 22:59:53,195 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 22:59:53,196 - ModelManager - INFO - No existing models config found
2025-06-07 22:59:53,197 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 22:59:53,197 - AIEngine - INFO - Loaded 28 learned response patterns
2025-06-07 22:59:53,197 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 22:59:53,198 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 22:59:53,198 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 22:59:53,198 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 22:59:53,198 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 22:59:53,198 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 22:59:53,427 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 22:59:53,428 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 22:59:53,429 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 22:59:53,429 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 23:00:00,081 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 23:00:00,082 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 23:00:00,082 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 23:00:00,178 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 23:00:00,233 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 23:00:00,345 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 23:00:00,345 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 23:00:03,343 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 3.9714040723744994
2025-06-07 23:00:03,355 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 23:00:03,356 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 23:00:03,356 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 23:00:03,356 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 23:00:03,356 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 23:00:03,356 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 23:00:03,356 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 23:00:03,356 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 23:00:03,356 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 23:00:03,505 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 23:00:03,505 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 23:00:03,505 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 23:00:06,756 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 23:00:09,766 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 23:00:09,890 - STTEngine - INFO - 🎤 Heard: 'هلو'
2025-06-07 23:00:13,953 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 23:00:13,953 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 23:00:13,953 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 23:00:14,608 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-07 23:00:14,609 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-07 23:00:14,649 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧪 Manual Chat Interface Test - Arabic Primary...
2025-06-07 23:00:14,650 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:00:14] ⚙️ System: 🧪 Manual...
2025-06-07 23:00:14,663 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:00:14,663 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ✅ Chat display functionality test ready...
2025-06-07 23:00:14,664 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:00:14] ⚙️ System: ✅ Chat d...
2025-06-07 23:00:14,664 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:00:14,664 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🇸🇦 اللغة الأساسية: العربية - اكتب: مرحبا، ما اسمك؟...
2025-06-07 23:00:14,667 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: 🇸🇦 ؟ﻚﻟﺎﺣ ﻒﻴﻛ ،؟ﻚﻤﺳﺍ ﺎﻣ ،ﺎﺒﺣﺮﻣ ...
2025-06-07 23:00:14,681 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:00:14,681 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🇺🇸 Secondary Language: English - Type: hello, what...
2025-06-07 23:00:14,682 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:00:14] ⚙️ System: 🇺🇸 Secon...
2025-06-07 23:00:14,685 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:00:14,685 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> مرحباً! أنا جيديون، مساعدك الذكي. كيف يمكنني مساعد...
2025-06-07 23:00:14,686 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ؟ﻡﻮﻴﻟﺍ ﻚﺗﺪﻋﺎﺴﻣ ﻲﻨﻨﻜﻤﻳ ﻒﻴﻛ .ﻲﻛﺬ...
2025-06-07 23:00:14,690 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:00:14,691 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-07 23:00:14,691 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:00:14] ⚙️ System: 🚀 Gideon...
2025-06-07 23:00:14,699 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:00:14,699 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-07 23:00:14,699 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:00:14] ⚙️ System: 💼 Ultra-...
2025-06-07 23:00:14,702 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:00:14,702 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-07 23:00:14,702 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:00:14] ⚙️ System: 🎯 Real-t...
2025-06-07 23:00:14,703 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:00:14,703 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-07 23:00:14,703 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:00:14] ⚙️ System: 🧠 Enterp...
2025-06-07 23:00:14,703 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:00:14,703 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-07 23:00:14,704 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:00:14] ⚙️ System: 💡 Advanc...
2025-06-07 23:00:14,706 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:00:14,707 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-07 23:00:14,707 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:00:14] ⚙️ System: 💬 Say 'G...
2025-06-07 23:00:14,707 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:00:16,712 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-07 23:00:16,713 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:00:16] 🤖 Gideon: Hello! I'...
2025-06-07 23:00:16,713 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:03:33,536 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 23:03:33,537 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 23:03:33,542 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 23:03:33,542 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 23:03:33,542 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 23:03:33,544 - ModelManager - INFO - No existing models config found
2025-06-07 23:03:33,544 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 23:03:33,545 - AIEngine - INFO - Loaded 28 learned response patterns
2025-06-07 23:03:33,545 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 23:03:33,546 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 23:03:33,546 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 23:03:33,546 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 23:03:33,546 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 23:03:33,546 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 23:03:33,794 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 23:03:33,795 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 23:03:33,795 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 23:03:33,796 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 23:03:44,701 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 23:03:44,701 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 23:03:44,702 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 23:03:44,809 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 23:03:44,867 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 23:03:44,983 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 23:03:44,983 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 23:03:47,978 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 53.10561376515884
2025-06-07 23:03:47,989 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 23:03:47,989 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 23:03:47,990 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 23:03:47,990 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 23:03:47,990 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 23:03:47,990 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 23:03:47,990 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 23:03:47,990 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 23:03:47,990 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 23:03:48,181 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 23:03:48,181 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 23:03:48,181 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 23:03:51,426 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 23:03:54,004 - STTEngine - INFO - Microphone test successful. Heard: هلو
2025-06-07 23:03:54,322 - STTEngine - INFO - 🎤 Heard: 'هلو'
2025-06-07 23:03:56,127 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 23:03:56,128 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 23:03:56,128 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 23:03:56,858 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-07 23:03:56,858 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-07 23:03:56,907 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition started!...
2025-06-07 23:03:56,907 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:03:56] ⚙️ System: 🚀 Gideon...
2025-06-07 23:03:56,926 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:03:56,926 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Welcome to the ultimate professional AI assistan...
2025-06-07 23:03:56,927 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:03:56] ⚙️ System: 💼 Welcom...
2025-06-07 23:03:56,931 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:03:56,931 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 All enterprise features are active and ready...
2025-06-07 23:03:56,931 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:03:56] ⚙️ System: 🎯 All en...
2025-06-07 23:03:56,935 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:03:56,935 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model active: dolphin-llama3:70b...
2025-06-07 23:03:56,935 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:03:56] ⚙️ System: 🧠 Enterp...
2025-06-07 23:03:56,935 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:03:56,935 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex tasks...
2025-06-07 23:03:56,936 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:03:56] ⚙️ System: 💡 Advanc...
2025-06-07 23:03:56,939 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:03:56,939 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-07 23:03:56,939 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:03:56] ⚙️ System: 🚀 Gideon...
2025-06-07 23:03:56,940 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:03:56,940 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-07 23:03:56,940 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:03:56] ⚙️ System: 💼 Ultra-...
2025-06-07 23:03:56,940 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:03:56,941 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-07 23:03:56,941 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:03:56] ⚙️ System: 🎯 Real-t...
2025-06-07 23:03:56,941 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:03:56,941 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-07 23:03:56,941 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:03:56] ⚙️ System: 🧠 Enterp...
2025-06-07 23:03:56,942 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:03:56,942 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-07 23:03:56,942 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:03:56] ⚙️ System: 💡 Advanc...
2025-06-07 23:03:56,942 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:03:56,942 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-07 23:03:56,942 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:03:56] ⚙️ System: 💬 Say 'G...
2025-06-07 23:03:56,943 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:03:58,947 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-07 23:03:58,947 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:03:58] 🤖 Gideon: Hello! I'...
2025-06-07 23:03:58,947 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:03:59,994 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 23:04:00,270 - STTEngine - INFO - Stopped continuous listening
2025-06-07 23:04:00,712 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 23:04:00,795 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 23:05:36,946 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> هلا...
2025-06-07 23:05:36,948 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫ :You 👤 [23:05:36]
...
2025-06-07 23:05:36,957 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:05:36,965 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 23:05:36,965 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 23:05:36,965 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 23:05:36,966 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 23:05:36,966 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 23:05:36,966 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 23:05:36,967 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 23:05:36,967 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 23:05:36,967 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 23:05:36,967 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 23:05:36,967 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 23:05:36,974 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 23:05:37,083 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'I'm processing that......'
2025-06-07 23:05:37,092 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-07 23:05:37,092 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-07 23:05:37,093 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:05:37] 🤖 Gideon: I'm proce...
2025-06-07 23:05:37,093 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:06:10,033 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:06:10,033 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:06:10,047 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:06:18,594 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:06:18,594 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:06:18,598 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:06:20,897 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> شلونك...
2025-06-07 23:06:20,898 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻚﻧﻮﻠﺷ :You 👤 [23:06:20]
...
2025-06-07 23:06:20,899 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:06:20,900 - AIEngine - INFO - Generating response for: 'شلونك'
2025-06-07 23:06:20,900 - AIEngine - INFO - Generating response for: 'شلونك'
2025-06-07 23:06:20,900 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 23:06:20,900 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 23:06:20,901 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 23:06:20,901 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 23:06:20,901 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 23:06:20,901 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 23:06:20,901 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 23:06:20,901 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 23:06:20,901 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 23:06:20,902 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 23:06:21,007 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'شلونك' -> 'I'm processing that......'
2025-06-07 23:06:21,009 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-07 23:06:21,009 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-07 23:06:21,010 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:06:21] 🤖 Gideon: I'm proce...
2025-06-07 23:06:21,010 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:06:44,174 - AIEngine - INFO - LLM response generated successfully: مرحباً، شلونك! سعدت بمعرفتك. هل يمكنني مساعدتك في ...
2025-06-07 23:06:44,174 - AIEngine - INFO - LLM generated successful response: مرحباً، شلونك! سعدت بمعرفتك. هل يمكنني مساعدتك في ...
2025-06-07 23:06:44,178 - AIEngine - INFO - Final response generated: مرحباً، شلونك! سعدت بمعرفتك. هل يمكنني مساعدتك في ...
2025-06-07 23:07:19,335 - AIEngine - INFO - LLM response generated successfully: مرحباً! من السعادة أنني لا أستطيع مساعدتك إذا لم ت...
2025-06-07 23:07:19,335 - AIEngine - INFO - LLM generated successful response: مرحباً! من السعادة أنني لا أستطيع مساعدتك إذا لم ت...
2025-06-07 23:07:19,340 - AIEngine - INFO - Final response generated: مرحباً! من السعادة أنني لا أستطيع مساعدتك إذا لم ت...
2025-06-07 23:10:50,918 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 23:10:50,924 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 23:10:50,924 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 23:10:50,924 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 23:10:50,925 - ModelManager - INFO - No existing models config found
2025-06-07 23:10:50,926 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 23:10:50,927 - AIEngine - INFO - Loaded 28 learned response patterns
2025-06-07 23:10:50,927 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 23:10:50,928 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 23:10:50,928 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 23:10:50,928 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 23:10:50,928 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 23:10:50,928 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 23:10:51,180 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 23:10:51,182 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 23:10:51,182 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 23:10:51,183 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 23:11:14,837 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 23:11:14,840 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 23:11:14,840 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 23:11:14,956 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 23:11:15,011 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 23:11:15,124 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 23:11:15,124 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 23:11:18,116 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-07 23:11:18,128 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 23:11:18,128 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 23:11:18,128 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 23:11:18,128 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 23:11:18,129 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 23:11:18,129 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 23:11:18,129 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 23:11:18,129 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 23:11:18,129 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 23:11:18,341 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 23:11:18,341 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 23:11:18,342 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 23:11:21,586 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 23:11:24,588 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 23:11:28,773 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 23:11:28,774 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 23:11:28,774 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 23:11:29,458 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-07 23:11:29,459 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-07 23:11:34,833 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 23:11:34,840 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 23:11:34,840 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 23:11:34,840 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 23:11:34,841 - ModelManager - INFO - No existing models config found
2025-06-07 23:11:34,842 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 23:11:34,843 - AIEngine - INFO - Loaded 28 learned response patterns
2025-06-07 23:11:34,843 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 23:11:34,843 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 23:11:34,844 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 23:11:34,844 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 23:11:34,844 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 23:11:34,844 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 23:11:35,104 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 23:11:35,106 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 23:11:35,106 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 23:11:35,106 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 23:11:41,962 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 23:11:41,962 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 23:11:41,962 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 23:11:41,964 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 23:11:41,964 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 23:11:41,968 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 23:11:41,968 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 23:11:42,020 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 23:11:42,890 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 23:11:42,963 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 23:11:44,967 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.024051169232843
2025-06-07 23:11:44,978 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 23:11:44,978 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 23:11:44,978 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 23:11:44,978 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 23:11:44,978 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 23:11:44,979 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 23:11:44,979 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 23:11:44,979 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 23:11:44,979 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 23:11:44,981 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 23:11:44,981 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 23:11:44,981 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 23:11:48,109 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 23:11:51,118 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 23:11:55,301 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 23:11:55,301 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 23:11:55,301 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 23:11:55,521 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-07 23:11:55,521 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-07 23:11:55,542 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧪 Testing _add_message method...
2025-06-07 23:11:55,543 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:11:55] ⚙️ System: 🧪 Testin...
2025-06-07 23:11:55,563 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:11:55,563 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> Hello, this is a test message...
2025-06-07 23:11:55,563 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:11:55] 👤 You: Hello, this ...
2025-06-07 23:11:55,571 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:11:55,572 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant....
2025-06-07 23:11:55,572 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:11:55] 🤖 Gideon: Hello! I'...
2025-06-07 23:11:55,575 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:11:55,575 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> مرحباً! أنا جيديون، مساعدك الذكي....
2025-06-07 23:11:55,577 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: .ﻲﻛﺬﻟﺍ ﻙﺪﻋﺎﺴﻣ ،ﻥﻮﻳﺪﻴﺟ ﺎﻧﺃ !ﺎﺒﺣ...
2025-06-07 23:11:55,578 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:11:55,579 - UltraProfessionalInterface - INFO - 📝 Adding message: Test -> This is a very long message to test text wrapping ...
2025-06-07 23:11:55,579 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:11:55] 💬 Test: This is a v...
2025-06-07 23:11:55,579 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:12:05,989 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 23:12:05,990 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 23:12:05,990 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 23:12:05,991 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 23:12:05,992 - ModelManager - INFO - No existing models config found
2025-06-07 23:12:05,992 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 23:12:05,993 - AIEngine - INFO - Loaded 28 learned response patterns
2025-06-07 23:12:05,993 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 23:12:05,994 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 23:12:05,994 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 23:12:05,994 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 23:12:05,994 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 23:12:05,994 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 23:12:06,224 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 23:12:06,225 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 23:12:06,225 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 23:12:06,225 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 23:12:12,606 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 23:12:12,607 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 23:12:12,607 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 23:12:12,656 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver']
2025-06-07 23:13:46,705 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 23:13:46,712 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 23:13:46,712 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 23:13:46,712 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 23:13:46,714 - ModelManager - INFO - No existing models config found
2025-06-07 23:13:46,714 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 23:13:46,715 - AIEngine - INFO - Loaded 28 learned response patterns
2025-06-07 23:13:46,715 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 23:13:46,715 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 23:13:46,716 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 23:13:46,716 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 23:13:46,716 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 23:13:46,716 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 23:13:46,943 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 23:13:46,944 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 23:13:46,945 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 23:13:46,945 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 23:14:06,355 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 23:14:06,357 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 23:14:06,357 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 23:14:06,528 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 23:14:06,589 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 23:14:06,720 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 23:14:06,720 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 23:14:09,720 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 45.41075330171953
2025-06-07 23:14:09,732 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 23:14:09,732 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 23:14:09,732 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 23:14:09,733 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 23:14:09,733 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 23:14:09,733 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 23:14:09,733 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 23:14:09,733 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 23:14:09,733 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 23:14:09,954 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 23:14:09,954 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 23:14:09,954 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 23:14:13,204 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 23:14:16,213 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 23:14:20,398 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 23:14:20,398 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 23:14:20,398 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 23:14:21,098 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-07 23:14:21,099 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-07 23:14:21,137 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-07 23:14:21,139 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧪 Manual Interaction Test - Fixed Threading...
2025-06-07 23:14:21,140 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:14:21] ⚙️ System: 🧪 Manual...
2025-06-07 23:14:21,159 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:14:21,159 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ✅ Chat display working - animations disabled...
2025-06-07 23:14:21,159 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:14:21] ⚙️ System: ✅ Chat d...
2025-06-07 23:14:21,160 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:14:21,160 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🇸🇦 اكتب: مرحبا، ما اسمك؟، كيف حالك؟...
2025-06-07 23:14:21,163 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: 🇸🇦 ؟ﻚﻟﺎﺣ ﻒﻴﻛ ،؟ﻚﻤﺳﺍ ﺎﻣ ،ﺎﺒﺣﺮﻣ ...
2025-06-07 23:14:21,176 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:14:21,176 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🇺🇸 Type: hello, what is your name?, how are you?...
2025-06-07 23:14:21,177 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:14:21] ⚙️ System: 🇺🇸 Type:...
2025-06-07 23:14:21,180 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:14:21,180 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> مرحباً! أنا جيديون. اكتب رسالتك وسأرد عليك....
2025-06-07 23:14:21,182 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: .ﻚﻴﻠﻋ ﺩﺭﺄﺳﻭ ﻚﺘﻟﺎﺳﺭ ﺐﺘﻛﺍ .ﻥﻮﻳﺪﻴ...
2025-06-07 23:14:21,185 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:14:21,185 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-07 23:14:21,185 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:14:21] ⚙️ System: 🚀 Gideon...
2025-06-07 23:14:21,192 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:14:21,192 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-07 23:14:21,192 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:14:21] ⚙️ System: 💼 Ultra-...
2025-06-07 23:14:21,196 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:14:21,196 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-07 23:14:21,196 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:14:21] ⚙️ System: 🎯 Real-t...
2025-06-07 23:14:21,196 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:14:21,196 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-07 23:14:21,197 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:14:21] ⚙️ System: 🧠 Enterp...
2025-06-07 23:14:21,197 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:14:21,197 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-07 23:14:21,197 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:14:21] ⚙️ System: 💡 Advanc...
2025-06-07 23:14:21,200 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:14:21,200 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-07 23:14:21,200 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:14:21] ⚙️ System: 💬 Say 'G...
2025-06-07 23:14:21,201 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:14:23,201 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-07 23:14:23,201 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:14:23] 🤖 Gideon: Hello! I'...
2025-06-07 23:14:23,202 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:15:50,517 - STTEngine - INFO - 🎤 Heard: '‏update'
2025-06-07 23:16:09,165 - STTEngine - INFO - 🎤 Heard: 'اربع دقايق خمس دقايق'
2025-06-07 23:17:03,247 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> هلا...
2025-06-07 23:17:03,248 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫ :You 👤 [23:17:03]
...
2025-06-07 23:17:03,263 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:17:03,271 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 23:17:03,272 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-07 23:17:03,272 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 23:17:03,272 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 23:17:03,273 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 23:17:03,273 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 23:17:03,273 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 23:17:03,273 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 23:17:03,273 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 23:17:03,273 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 23:17:03,274 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 23:17:03,274 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 23:17:03,380 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'I'm processing that......'
2025-06-07 23:17:03,389 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-07 23:17:05,172 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-07 23:17:05,172 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:17:05] 🤖 Gideon: I'm proce...
2025-06-07 23:17:05,234 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message inserted successfully
2025-06-07 23:17:32,110 - AIEngine - INFO - LLM response generated successfully: مرحبا، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:17:32,110 - AIEngine - INFO - LLM generated successful response: مرحبا، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:17:32,120 - AIEngine - INFO - Final response generated: مرحبا، كيف يمكنني مساعدتك اليوم؟ Is there anything...
2025-06-07 23:17:56,729 - AIEngine - INFO - LLM response generated successfully: مرحباً! سعدني مساعدتك. إذا كان لديك أي أسئلة أو po...
2025-06-07 23:17:56,730 - AIEngine - INFO - LLM generated successful response: مرحباً! سعدني مساعدتك. إذا كان لديك أي أسئلة أو po...
2025-06-07 23:17:56,734 - AIEngine - INFO - Final response generated: مرحباً! سعدني مساعدتك. إذا كان لديك أي أسئلة أو po...
2025-06-07 23:22:00,278 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 23:22:00,283 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 23:22:00,283 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 23:22:00,283 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 23:22:00,285 - ModelManager - INFO - No existing models config found
2025-06-07 23:22:00,285 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 23:22:00,286 - AIEngine - INFO - Loaded 28 learned response patterns
2025-06-07 23:22:00,286 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 23:22:00,287 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 23:22:00,287 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 23:22:00,287 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 23:22:00,287 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 23:22:00,288 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 23:22:00,527 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 23:22:00,529 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 23:22:00,529 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 23:22:00,529 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 23:22:21,921 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 23:22:21,922 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 23:22:21,922 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 23:22:22,030 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 23:22:22,084 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 23:22:22,196 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 23:22:22,196 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 23:22:25,189 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-07 23:22:25,201 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 23:22:25,201 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 23:22:25,201 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 23:22:25,201 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 23:22:25,201 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 23:22:25,202 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 23:22:25,202 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 23:22:25,202 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 23:22:25,202 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 23:22:25,389 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 23:22:25,389 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 23:22:25,389 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 23:22:28,634 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 23:22:31,641 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-07 23:22:35,827 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 23:22:35,827 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 23:22:35,827 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 23:22:36,530 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-07 23:22:36,530 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-07 23:22:36,570 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-07 23:22:36,572 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 MANUAL TEST - FIXED CHAT DISPLAY...
2025-06-07 23:22:36,573 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:22:36] ⚙️ System: 🚀 MANUAL...
2025-06-07 23:22:36,748 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:22:36,748 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:22:36,748 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ✅ Direct callback system active...
2025-06-07 23:22:36,748 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:22:36] ⚙️ System: ✅ Direct...
2025-06-07 23:22:36,749 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:22:36,749 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:22:36,749 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 AI responses should now appear immediately...
2025-06-07 23:22:36,749 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:22:36] ⚙️ System: 🎯 AI res...
2025-06-07 23:22:36,753 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:22:36,753 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:22:36,753 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> مرحباً! اكتب رسالتك وسترى الرد في النافذة!...
2025-06-07 23:22:36,756 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: !ﺓﺬﻓﺎﻨﻟﺍ ﻲﻓ ﺩﺮﻟﺍ ﻯﺮﺘﺳﻭ ﻚﺘﻟﺎﺳﺭ ...
2025-06-07 23:22:36,758 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:22:36,758 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:22:36,758 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! Type your message and you'll see the respon...
2025-06-07 23:22:36,758 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:22:36] 🤖 Gideon: Hello! Ty...
2025-06-07 23:22:36,759 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:22:36,759 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:22:36,759 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-07 23:22:36,759 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:22:36] ⚙️ System: 🚀 Gideon...
2025-06-07 23:22:36,759 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:22:36,760 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:22:36,760 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-07 23:22:36,760 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:22:36] ⚙️ System: 💼 Ultra-...
2025-06-07 23:22:36,764 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:22:36,764 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:22:36,764 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-07 23:22:36,765 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:22:36] ⚙️ System: 🎯 Real-t...
2025-06-07 23:22:36,765 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:22:36,765 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:22:36,765 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-07 23:22:36,766 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:22:36] ⚙️ System: 🧠 Enterp...
2025-06-07 23:22:36,767 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:22:36,767 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:22:36,767 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-07 23:22:36,767 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:22:36] ⚙️ System: 💡 Advanc...
2025-06-07 23:22:36,770 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:22:36,771 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:22:36,771 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-07 23:22:36,771 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:22:36] ⚙️ System: 💬 Say 'G...
2025-06-07 23:22:36,771 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:22:36,771 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:22:38,785 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-07 23:22:38,786 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:22:38] 🤖 Gideon: Hello! I'...
2025-06-07 23:22:38,788 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:22:38,788 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:23:30,168 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 23:23:30,362 - STTEngine - INFO - Stopped continuous listening
2025-06-07 23:23:30,645 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 23:31:01,924 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> اهلا...
2025-06-07 23:31:01,926 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫﺍ :You 👤 [23:31:01]
...
2025-06-07 23:31:01,941 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:31:01,942 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:31:01,950 - AIEngine - INFO - Generating response for: 'اهلا'
2025-06-07 23:31:01,950 - AIEngine - INFO - Generating response for: 'اهلا'
2025-06-07 23:31:01,951 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 23:31:01,951 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 23:31:01,951 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 23:31:01,951 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 23:31:01,951 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 23:31:01,951 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 23:31:01,951 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 23:31:01,952 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 23:31:01,952 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 23:31:01,952 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 23:31:02,061 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-07 23:31:02,061 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'اهلا' -> 'I'm processing that......'
2025-06-07 23:31:02,083 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-07 23:31:02,084 - UltraProfessionalInterface - INFO - 🚀 FORCING MESSAGE DISPLAY: 'I'm processing that......'
2025-06-07 23:31:02,084 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-07 23:31:02,084 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:31:02] 🤖 Gideon: I'm proce...
2025-06-07 23:31:02,107 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:31:02,107 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:32:01,941 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> سلام...
2025-06-07 23:32:01,953 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻡﻼﺳ :You 👤 [23:32:01]
...
2025-06-07 23:32:01,961 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:32:01,962 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:32:01,968 - AIEngine - INFO - Generating response for: 'سلام'
2025-06-07 23:32:01,971 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 23:32:01,971 - AIEngine - INFO - Generating response for: 'سلام'
2025-06-07 23:32:01,972 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 23:32:01,972 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 23:32:01,973 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 23:32:01,973 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 23:32:01,973 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 23:32:01,973 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 23:32:01,973 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 23:32:01,974 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 23:32:01,996 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 23:32:02,082 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-07 23:32:02,083 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'سلام' -> 'I'm processing that......'
2025-06-07 23:32:02,097 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-07 23:32:02,098 - UltraProfessionalInterface - INFO - 🚀 FORCING MESSAGE DISPLAY: 'I'm processing that......'
2025-06-07 23:32:02,098 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-07 23:32:02,098 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:32:02] 🤖 Gideon: I'm proce...
2025-06-07 23:32:02,118 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:32:02,118 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:32:23,730 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 23:32:24,647 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 23:32:24,773 - STTEngine - INFO - Stopped continuous listening
2025-06-07 23:32:32,691 - AIEngine - INFO - LLM response generated successfully: مرحباً، يسعدني أن أقدم لك المساعدة. كيف يمكنني ان ...
2025-06-07 23:32:32,691 - AIEngine - INFO - LLM generated successful response: مرحباً، يسعدني أن أقدم لك المساعدة. كيف يمكنني ان ...
2025-06-07 23:32:32,707 - AIEngine - INFO - Final response generated: مرحباً، يسعدني أن أقدم لك المساعدة. كيف يمكنني ان ...
2025-06-07 23:32:44,331 - AIEngine - INFO - LLM response generated successfully: أهلاً وسهلاً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:32:44,332 - AIEngine - INFO - LLM generated successful response: أهلاً وسهلاً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:32:44,336 - AIEngine - INFO - Final response generated: أهلاً وسهلاً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:32:47,000 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-07 23:32:47,001 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-07 23:32:47,005 - MemorySystem - INFO - Loaded 16 recent conversations
2025-06-07 23:32:47,005 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-07 23:32:47,005 - MemorySystem - INFO - Memory system initialized successfully
2025-06-07 23:32:47,007 - ModelManager - INFO - No existing models config found
2025-06-07 23:32:47,008 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 23:32:47,009 - AIEngine - INFO - Loaded 28 learned response patterns
2025-06-07 23:32:47,009 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 23:32:47,010 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 23:32:47,010 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-07 23:32:47,010 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-07 23:32:47,011 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 23:32:47,011 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 23:32:47,298 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 23:32:47,300 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 23:32:47,300 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 23:32:47,300 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 23:33:05,125 - AIEngine - INFO - LLM response generated successfully: مرحباً، أنا جديون، مساعد ذكيفود. كيف يمكنني مساعدت...
2025-06-07 23:33:05,125 - AIEngine - INFO - LLM generated successful response: مرحباً، أنا جديون، مساعد ذكيفود. كيف يمكنني مساعدت...
2025-06-07 23:33:05,129 - AIEngine - INFO - Final response generated: مرحباً، أنا جديون، مساعد ذكيفود. كيف يمكنني مساعدت...
2025-06-07 23:33:13,377 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:33:13,377 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:33:13,381 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:33:21,513 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 23:33:21,513 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 23:33:21,513 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 23:33:21,619 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-07 23:33:21,675 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-07 23:33:21,795 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-07 23:33:21,795 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-07 23:33:24,792 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-07 23:33:24,804 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-07 23:33:24,804 - STTEngine - INFO -    Energy threshold: 150
2025-06-07 23:33:24,805 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-07 23:33:24,805 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-07 23:33:24,805 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-07 23:33:24,805 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-07 23:33:24,805 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-07 23:33:24,806 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-07 23:33:24,806 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-07 23:33:25,022 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-07 23:33:25,023 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-07 23:33:25,023 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-07 23:33:28,270 - STTEngine - INFO - Testing microphone... Say something!
2025-06-07 23:33:30,436 - STTEngine - INFO - Microphone test successful. Heard: خلاص
2025-06-07 23:33:32,558 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-07 23:33:32,558 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-07 23:33:32,559 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-07 23:33:33,255 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-07 23:33:33,255 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-07 23:33:33,293 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-07 23:33:33,299 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition started!...
2025-06-07 23:33:33,299 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:33:33] ⚙️ System: 🚀 Gideon...
2025-06-07 23:33:33,476 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:33:33,476 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:33:33,477 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Welcome to the ultimate professional AI assistan...
2025-06-07 23:33:33,477 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:33:33] ⚙️ System: 💼 Welcom...
2025-06-07 23:33:33,481 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:33:33,481 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:33:33,481 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 All enterprise features are active and ready...
2025-06-07 23:33:33,481 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:33:33] ⚙️ System: 🎯 All en...
2025-06-07 23:33:33,485 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:33:33,485 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:33:33,485 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model active: dolphin-llama3:70b...
2025-06-07 23:33:33,485 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:33:33] ⚙️ System: 🧠 Enterp...
2025-06-07 23:33:33,486 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:33:33,486 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:33:33,486 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex tasks...
2025-06-07 23:33:33,486 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:33:33] ⚙️ System: 💡 Advanc...
2025-06-07 23:33:33,490 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:33:33,490 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:33:33,490 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-07 23:33:33,490 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:33:33] ⚙️ System: 🚀 Gideon...
2025-06-07 23:33:33,492 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:33:33,492 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:33:33,492 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-07 23:33:33,492 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:33:33] ⚙️ System: 💼 Ultra-...
2025-06-07 23:33:33,493 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:33:33,493 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:33:33,493 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-07 23:33:33,493 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:33:33] ⚙️ System: 🎯 Real-t...
2025-06-07 23:33:33,494 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:33:33,494 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:33:33,494 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-07 23:33:33,495 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:33:33] ⚙️ System: 🧠 Enterp...
2025-06-07 23:33:33,495 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:33:33,495 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:33:33,496 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-07 23:33:33,496 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:33:33] ⚙️ System: 💡 Advanc...
2025-06-07 23:33:33,497 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:33:33,497 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:33:33,497 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-07 23:33:33,497 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:33:33] ⚙️ System: 💬 Say 'G...
2025-06-07 23:33:33,498 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:33:33,498 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:33:35,498 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-07 23:33:35,498 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:33:35] 🤖 Gideon: Hello! I'...
2025-06-07 23:33:35,499 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:33:35,499 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:33:39,093 - STTEngine - INFO - 🎤 Heard: 'انا قلت له ما قلت خلاص بس يلا'
2025-06-07 23:33:59,714 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> سلام...
2025-06-07 23:33:59,717 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻡﻼﺳ :You 👤 [23:33:59]
...
2025-06-07 23:33:59,730 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:33:59,731 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:33:59,739 - AIEngine - INFO - Generating response for: 'سلام'
2025-06-07 23:33:59,739 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 23:33:59,740 - AIEngine - INFO - Generating response for: 'سلام'
2025-06-07 23:33:59,740 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 23:33:59,740 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 23:33:59,740 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 23:33:59,741 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-07 23:33:59,741 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 23:33:59,741 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 23:33:59,741 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 23:33:59,741 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 23:33:59,750 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 23:33:59,852 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-07 23:33:59,852 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'سلام' -> 'I'm processing that......'
2025-06-07 23:33:59,873 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-07 23:33:59,874 - UltraProfessionalInterface - INFO - 🚀 FORCING MESSAGE DISPLAY: 'I'm processing that......'
2025-06-07 23:33:59,874 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-07 23:33:59,874 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [23:33:59] 🤖 Gideon: I'm proce...
2025-06-07 23:33:59,894 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-07 23:33:59,894 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-07 23:34:27,103 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:34:27,103 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:34:27,114 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:34:35,220 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:34:35,221 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:34:35,224 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-07 23:36:00,296 - STTEngine - INFO - 🎤 Heard: 'الواي فاي'
2025-06-07 23:36:08,355 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-07 23:36:08,903 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-07 23:36:08,997 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-07 23:51:33,654 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-07 23:51:33,654 - AIEngine - INFO - Initializing LLM backends...
2025-06-07 23:51:33,654 - AIEngine - INFO - ✅ Ollama backend available
2025-06-07 23:51:33,654 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-07 23:51:33,655 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-07 23:51:33,655 - AIEngine - INFO - ℹ️ Transformers backend not available
2025-06-07 23:51:33,655 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-07 23:51:33,655 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-07 23:51:33,897 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-07 23:51:33,898 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-07 23:51:33,898 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-07 23:51:33,898 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-07 23:52:16,584 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-07 23:52:16,589 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-07 23:52:16,589 - AIEngine - INFO - AI Engine initialized successfully
2025-06-07 23:52:16,589 - AIEngine - INFO - Generating response for: 'Hello'
2025-06-07 23:52:16,590 - AIEngine - INFO - Attempting LLM response generation...
2025-06-07 23:52:16,590 - AIEngine - INFO - 🔍 Initial language detection: en
2025-06-07 23:52:16,590 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-07 23:52:16,590 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-07 23:52:16,591 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-07 23:52:27,343 - AIEngine - INFO - LLM response generated successfully: Hello! How can I help you today?...
2025-06-07 23:52:27,343 - AIEngine - INFO - LLM generated successful response: Hello! How can I help you today?...
2025-06-07 23:52:27,343 - AIEngine - INFO - Final response generated: Hello! How can I help you today?...
