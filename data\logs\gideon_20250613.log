2025-06-13 01:38:55,762 - <PERSON><PERSON>ain - INFO - 🤖 Starting Gideon AI Assistant - Enterprise Edition
2025-06-13 01:38:55,763 - <PERSON><PERSON><PERSON> - INFO - Initializing Gideon Core System...
2025-06-13 01:38:55,769 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-13 01:38:55,769 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-13 01:38:55,769 - MemorySystem - INFO - Memory system initialized successfully
2025-06-13 01:38:55,770 - ModelManager - INFO - No existing models config found
2025-06-13 01:38:55,771 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-13 01:38:55,772 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-13 01:38:55,772 - AIEngine - INFO - Initializing LLM backends...
2025-06-13 01:38:55,772 - AIEngine - INFO - ✅ Ollama backend available
2025-06-13 01:38:55,773 - <PERSON><PERSON><PERSON><PERSON> - INFO - ℹ️ llama.cpp backend not available
2025-06-13 01:38:55,773 - <PERSON>Engine - INFO - ℹ️ CTransformers backend not available
2025-06-13 01:38:55,773 - AIEngine - INFO - ✅ Transformers backend available
2025-06-13 01:38:55,773 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-13 01:38:55,774 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-13 01:38:56,044 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-13 01:38:56,045 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-13 01:38:56,045 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-13 01:38:56,046 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-13 01:40:24,465 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-13 01:40:24,501 - TerminalManager - INFO - Shutting down terminal manager...
2025-06-13 01:40:24,507 - TerminalManager - INFO - Created default terminal session: session_1
2025-06-13 01:40:24,507 - TerminalManager - INFO - Closed session: session_0
2025-06-13 01:40:24,507 - TerminalManager - INFO - Terminal manager shutdown complete
2025-06-13 02:54:29,920 - EnterpriseMain - INFO - 🤖 Starting Gideon AI Assistant - Enterprise Edition
2025-06-13 02:54:29,923 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-13 02:54:29,928 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-13 02:54:29,928 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-13 02:54:29,928 - MemorySystem - INFO - Memory system initialized successfully
2025-06-13 02:54:29,930 - ModelManager - INFO - No existing models config found
2025-06-13 02:54:29,931 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-13 02:54:29,932 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-13 02:54:29,932 - AIEngine - INFO - Initializing LLM backends...
2025-06-13 02:54:29,933 - AIEngine - INFO - ✅ Ollama backend available
2025-06-13 02:54:29,933 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-13 02:54:29,933 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-13 02:54:29,934 - AIEngine - INFO - ✅ Transformers backend available
2025-06-13 02:54:29,934 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-13 02:54:29,935 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-13 02:54:30,190 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-13 02:54:30,191 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-13 02:54:30,191 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-13 02:54:30,192 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-13 02:56:46,722 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-13 02:56:46,738 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-13 02:56:46,739 - AIEngine - INFO - AI Engine initialized successfully
2025-06-13 02:56:47,169 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (DualSense Wireless Co', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (DualSense Wireless Controller)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (DualSense Wireless Controller)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-13 02:56:47,255 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-13 02:56:47,506 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-13 02:56:47,507 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-13 02:56:50,516 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 65.84451153274114
2025-06-13 02:56:50,535 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-13 02:56:50,536 - STTEngine - INFO -    Energy threshold: 150
2025-06-13 02:56:50,536 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-13 02:56:50,536 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-13 02:56:50,536 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-13 02:56:50,538 - STTEngine - INFO - 🌍 Fixed language mode: ar
2025-06-13 02:56:50,538 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-13 02:56:50,538 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-13 02:56:50,538 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-13 02:56:51,345 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-13 02:56:51,346 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-13 02:56:51,346 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-13 02:56:54,625 - STTEngine - INFO - Testing microphone... Say something!
2025-06-13 02:56:57,653 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-13 02:57:01,865 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-13 02:57:01,869 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-13 02:57:01,870 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-13 02:57:01,870 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-13 02:57:03,208 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-13 02:57:03,436 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-13 02:57:03,770 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-13 02:57:03,771 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-13 02:57:03,821 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-13 02:57:03,831 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition - READY!...
2025-06-13 02:57:03,831 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:03] ⚙️ System: 🚀 Gideon...
2025-06-13 02:57:04,183 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:04,184 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:04,184 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional AI assistant with Flash-inspi...
2025-06-13 02:57:04,184 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:04] ⚙️ System: 💼 Ultra-...
2025-06-13 02:57:04,188 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:04,188 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:04,188 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> � Bilingual support active: Arabic (primary) / Eng...
2025-06-13 02:57:04,189 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:04] ⚙️ System: � Biling...
2025-06-13 02:57:04,193 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:04,193 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:04,193 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎤 Voice interaction ready - Say 'Gideon' + your qu...
2025-06-13 02:57:04,193 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:04] ⚙️ System: 🎤 Voice ...
2025-06-13 02:57:04,200 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:04,200 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:04,201 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ⚡ Ultra-low latency mode active for instant respon...
2025-06-13 02:57:04,201 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:04] ⚙️ System: ⚡ Ultra-...
2025-06-13 02:57:04,202 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:04,202 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:04,202 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 AI Model loaded: dolphin-llama3:70b...
2025-06-13 02:57:04,202 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:04] ⚙️ System: 🧠 AI Mod...
2025-06-13 02:57:04,203 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:04,203 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:04,203 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready - Multi-backend s...
2025-06-13 02:57:04,203 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:04] ⚙️ System: 💡 Advanc...
2025-06-13 02:57:04,206 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:04,206 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:04,207 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🔄 Drag & drop new models to expand AI capabilities...
2025-06-13 02:57:04,207 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:04] ⚙️ System: 🔄 Drag &...
2025-06-13 02:57:04,208 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:04,208 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:04,208 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-13 02:57:04,209 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:04] ⚙️ System: 🚀 Gideon...
2025-06-13 02:57:04,209 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:04,209 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:04,210 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-13 02:57:04,210 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:04] ⚙️ System: 💼 Ultra-...
2025-06-13 02:57:04,218 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:04,219 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:04,219 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-13 02:57:04,219 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:04] ⚙️ System: 🎯 Real-t...
2025-06-13 02:57:04,220 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:04,220 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:04,220 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-13 02:57:04,221 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:04] ⚙️ System: 🧠 Enterp...
2025-06-13 02:57:04,221 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:04,221 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:04,222 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-13 02:57:04,222 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:04] ⚙️ System: 💡 Advanc...
2025-06-13 02:57:04,222 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:04,223 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:04,223 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-13 02:57:04,223 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:04] ⚙️ System: 💬 Say 'G...
2025-06-13 02:57:04,224 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:04,224 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:06,231 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-13 02:57:06,232 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:06] 🤖 Gideon: Hello! I'...
2025-06-13 02:57:06,232 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:06,233 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:25,882 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> هلا...
2025-06-13 02:57:25,892 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫ :You 👤 [02:57:25]
...
2025-06-13 02:57:25,918 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:25,919 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:25,927 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-13 02:57:25,930 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-13 02:57:25,930 - AIEngine - INFO - Attempting LLM response generation...
2025-06-13 02:57:25,931 - AIEngine - INFO - Attempting LLM response generation...
2025-06-13 02:57:25,931 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-13 02:57:25,931 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-13 02:57:25,932 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-13 02:57:25,932 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-13 02:57:25,932 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-13 02:57:25,932 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-13 02:57:25,933 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-13 02:57:25,933 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-13 02:57:26,037 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-13 02:57:26,037 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'I'm processing that......'
2025-06-13 02:57:26,066 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-13 02:57:26,066 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'I'm processing that......'
2025-06-13 02:57:26,066 - UltraProfessionalInterface - INFO - 📝 Direct display for short response
2025-06-13 02:57:26,067 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-13 02:57:26,067 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [02:57:26] 🤖 Gideon: I'm proce...
2025-06-13 02:57:26,099 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 02:57:26,099 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 02:57:53,645 - AIEngine - INFO - LLM response generated successfully: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-13 02:57:53,645 - AIEngine - INFO - LLM generated successful response: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-13 02:57:53,660 - AIEngine - INFO - Final response generated: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-13 02:58:15,369 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-13 02:58:15,376 - TerminalManager - INFO - Shutting down terminal manager...
2025-06-13 02:58:15,424 - TerminalManager - INFO - Created default terminal session: session_1
2025-06-13 02:58:15,424 - TerminalManager - INFO - Closed session: session_0
2025-06-13 02:58:15,425 - TerminalManager - INFO - Terminal manager shutdown complete
2025-06-13 02:58:15,537 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-13 02:58:15,635 - STTEngine - INFO - Stopped continuous listening
2025-06-13 02:58:15,706 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-13 02:58:56,143 - AIEngine - INFO - LLM response generated successfully: مرحباً! أهلاً وسهلاً بك، كيف يمكنني مساعدتك اليوم؟...
2025-06-13 02:58:56,161 - AIEngine - INFO - LLM generated successful response: مرحباً! أهلاً وسهلاً بك، كيف يمكنني مساعدتك اليوم؟...
2025-06-13 02:58:56,183 - AIEngine - INFO - Final response generated: مرحباً! أهلاً وسهلاً بك، كيف يمكنني مساعدتك اليوم؟...
2025-06-13 03:09:31,082 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-13 03:09:31,084 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-13 03:09:31,089 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-13 03:09:31,089 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-13 03:09:31,090 - MemorySystem - INFO - Memory system initialized successfully
2025-06-13 03:09:31,092 - ModelManager - INFO - No existing models config found
2025-06-13 03:09:31,092 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-13 03:09:31,094 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-13 03:09:31,094 - AIEngine - INFO - Initializing LLM backends...
2025-06-13 03:09:31,094 - AIEngine - INFO - ✅ Ollama backend available
2025-06-13 03:09:31,095 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-13 03:09:31,095 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-13 03:09:31,095 - AIEngine - INFO - ✅ Transformers backend available
2025-06-13 03:09:31,096 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-13 03:09:31,096 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-13 03:09:31,343 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-13 03:09:31,344 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-13 03:09:31,345 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-13 03:09:31,345 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-13 03:11:47,305 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-13 03:11:47,325 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-13 03:11:47,326 - AIEngine - INFO - AI Engine initialized successfully
2025-06-13 03:11:47,722 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (DualSense Wireless Co', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (DualSense Wireless Controller)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (DualSense Wireless Controller)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-13 03:11:47,796 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-13 03:11:47,995 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-13 03:11:47,996 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-13 03:11:51,006 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 77.68582057192891
2025-06-13 03:11:51,029 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-13 03:11:51,030 - STTEngine - INFO -    Energy threshold: 150
2025-06-13 03:11:51,030 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-13 03:11:51,030 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-13 03:11:51,031 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-13 03:11:51,031 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-13 03:11:51,031 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-13 03:11:51,032 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-13 03:11:51,032 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-13 03:11:51,854 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-13 03:11:51,855 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-13 03:11:51,855 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-13 03:11:55,136 - STTEngine - INFO - Testing microphone... Say something!
2025-06-13 03:11:58,162 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-13 03:12:02,379 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-13 03:12:02,384 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-13 03:12:02,384 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-13 03:12:02,384 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-13 03:12:03,427 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-13 03:12:03,642 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-13 03:12:03,835 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-13 03:12:03,835 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-13 03:12:03,875 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-13 03:12:03,882 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition - READY!...
2025-06-13 03:12:03,883 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:03] ⚙️ System: 🚀 Gideon...
2025-06-13 03:12:04,184 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:04,185 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:04,185 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional AI assistant with Flash-inspi...
2025-06-13 03:12:04,185 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:04] ⚙️ System: 💼 Ultra-...
2025-06-13 03:12:04,189 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:04,190 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:04,190 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> � Bilingual support active: Arabic (primary) / Eng...
2025-06-13 03:12:04,190 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:04] ⚙️ System: � Biling...
2025-06-13 03:12:04,193 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:04,194 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:04,194 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎤 Voice interaction ready - Say 'Gideon' + your qu...
2025-06-13 03:12:04,194 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:04] ⚙️ System: 🎤 Voice ...
2025-06-13 03:12:04,197 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:04,197 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:04,198 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> ⚡ Ultra-low latency mode active for instant respon...
2025-06-13 03:12:04,198 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:04] ⚙️ System: ⚡ Ultra-...
2025-06-13 03:12:04,198 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:04,198 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:04,199 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 AI Model loaded: dolphin-llama3:70b...
2025-06-13 03:12:04,199 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:04] ⚙️ System: 🧠 AI Mod...
2025-06-13 03:12:04,199 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:04,200 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:04,200 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready - Multi-backend s...
2025-06-13 03:12:04,200 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:04] ⚙️ System: 💡 Advanc...
2025-06-13 03:12:04,203 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:04,203 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:04,204 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🔄 Drag & drop new models to expand AI capabilities...
2025-06-13 03:12:04,204 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:04] ⚙️ System: 🔄 Drag &...
2025-06-13 03:12:04,204 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:04,205 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:04,205 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-13 03:12:04,205 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:04] ⚙️ System: 🚀 Gideon...
2025-06-13 03:12:04,206 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:04,206 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:04,206 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-13 03:12:04,206 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:04] ⚙️ System: 💼 Ultra-...
2025-06-13 03:12:04,212 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:04,212 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:04,213 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-13 03:12:04,213 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:04] ⚙️ System: 🎯 Real-t...
2025-06-13 03:12:04,214 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:04,214 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:04,215 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-13 03:12:04,215 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:04] ⚙️ System: 🧠 Enterp...
2025-06-13 03:12:04,216 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:04,216 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:04,216 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-13 03:12:04,216 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:04] ⚙️ System: 💡 Advanc...
2025-06-13 03:12:04,217 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:04,217 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:04,217 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-13 03:12:04,217 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:04] ⚙️ System: 💬 Say 'G...
2025-06-13 03:12:04,218 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:04,219 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:06,219 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-13 03:12:06,220 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:06] 🤖 Gideon: Hello! I'...
2025-06-13 03:12:06,225 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:06,226 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:18,564 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> هلا...
2025-06-13 03:12:18,571 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫ :You 👤 [03:12:18]
...
2025-06-13 03:12:18,586 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:18,587 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:12:18,595 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-13 03:12:18,597 - AIEngine - INFO - Attempting LLM response generation...
2025-06-13 03:12:18,598 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-13 03:12:18,598 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-13 03:12:18,598 - AIEngine - INFO - Attempting LLM response generation...
2025-06-13 03:12:18,599 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-13 03:12:18,599 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-13 03:12:18,599 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-13 03:12:18,599 - AIEngine - INFO - 🔧 Using config detected language: ar
2025-06-13 03:12:18,600 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-13 03:12:18,600 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-13 03:12:18,616 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-13 03:12:18,717 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-13 03:12:18,717 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'I'm processing that......'
2025-06-13 03:12:18,741 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-13 03:12:18,741 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'I'm processing that......'
2025-06-13 03:12:18,742 - UltraProfessionalInterface - INFO - 📝 Direct display for short response
2025-06-13 03:12:18,742 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-13 03:12:18,742 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:12:18] 🤖 Gideon: I'm proce...
2025-06-13 03:12:18,769 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:12:18,770 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:30:59,248 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-13 03:30:59,250 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-13 03:30:59,255 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-13 03:30:59,256 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-13 03:30:59,256 - MemorySystem - INFO - Memory system initialized successfully
2025-06-13 03:30:59,258 - ModelManager - INFO - No existing models config found
2025-06-13 03:30:59,259 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-13 03:30:59,259 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-13 03:30:59,260 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-13 03:30:59,260 - AIEngine - INFO - Initializing LLM backends...
2025-06-13 03:30:59,261 - AIEngine - INFO - ✅ Ollama backend available
2025-06-13 03:30:59,261 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-13 03:30:59,261 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-13 03:30:59,262 - AIEngine - INFO - ✅ Transformers backend available
2025-06-13 03:30:59,262 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-13 03:30:59,262 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-13 03:30:59,518 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-13 03:30:59,519 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-13 03:30:59,520 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-13 03:30:59,520 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-13 03:33:16,787 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-13 03:33:16,805 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-13 03:33:16,805 - AIEngine - INFO - AI Engine initialized successfully
2025-06-13 03:33:17,197 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (DualSense Wireless Co', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (DualSense Wireless Controller)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (DualSense Wireless Controller)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-13 03:33:17,279 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-13 03:33:17,486 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-13 03:33:17,488 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-13 03:33:20,497 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 86.4613167643072
2025-06-13 03:33:20,543 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-13 03:33:20,543 - STTEngine - INFO -    Energy threshold: 150
2025-06-13 03:33:20,543 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-13 03:33:20,544 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-13 03:33:20,545 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-13 03:33:20,546 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-13 03:33:20,546 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-13 03:33:20,547 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-13 03:33:20,547 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-13 03:33:21,470 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-13 03:33:21,471 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-13 03:33:21,471 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-13 03:33:24,765 - STTEngine - INFO - Testing microphone... Say something!
2025-06-13 03:33:27,792 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-13 03:33:32,011 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-13 03:33:32,015 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-13 03:33:32,016 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-13 03:33:32,016 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-13 03:33:33,027 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-13 03:33:33,242 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-13 03:33:33,472 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-13 03:33:33,472 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-13 03:33:33,514 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-13 03:33:33,526 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-13 03:33:33,526 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:33] ⚙️ System: Gideon A...
2025-06-13 03:33:33,851 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:33,851 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:33,851 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-13 03:33:33,851 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:33] ⚙️ System: Ultra-pr...
2025-06-13 03:33:33,852 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:33,852 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:33,853 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-13 03:33:33,853 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:33] ⚙️ System: Bilingua...
2025-06-13 03:33:33,853 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:33,853 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:33,854 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-13 03:33:33,854 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:33] ⚙️ System: Voice in...
2025-06-13 03:33:33,854 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:33,855 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:33,855 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-13 03:33:33,855 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:33] ⚙️ System: Ultra-lo...
2025-06-13 03:33:33,856 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:33,856 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:33,856 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-13 03:33:33,857 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:33] ⚙️ System: AI Model...
2025-06-13 03:33:33,857 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:33,857 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:33,858 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-13 03:33:33,858 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:33] ⚙️ System: Advanced...
2025-06-13 03:33:33,858 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:33,859 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:33,859 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-13 03:33:33,859 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:33] ⚙️ System: Drag & d...
2025-06-13 03:33:33,859 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:33,860 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:33,860 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-13 03:33:33,860 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:33] ⚙️ System: 🚀 Gideon...
2025-06-13 03:33:33,868 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:33,869 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:33,869 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-13 03:33:33,869 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:33] ⚙️ System: 💼 Ultra-...
2025-06-13 03:33:33,873 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:33,873 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:33,874 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-13 03:33:33,874 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:33] ⚙️ System: 🎯 Real-t...
2025-06-13 03:33:33,877 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:33,877 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:33,878 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-13 03:33:33,878 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:33] ⚙️ System: 🧠 Enterp...
2025-06-13 03:33:33,879 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:33,879 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:33,880 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-13 03:33:33,880 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:33] ⚙️ System: 💡 Advanc...
2025-06-13 03:33:33,884 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:33,884 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:33,884 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-13 03:33:33,885 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:33] ⚙️ System: 💬 Say 'G...
2025-06-13 03:33:33,885 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:33,886 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:35,893 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-13 03:33:35,893 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:35] 🤖 Gideon: Hello! I'...
2025-06-13 03:33:35,894 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:35,894 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:49,460 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> هلا...
2025-06-13 03:33:49,469 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫ :You 👤 [03:33:49]
...
2025-06-13 03:33:49,485 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:49,485 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:33:49,493 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-13 03:33:49,496 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-13 03:33:49,497 - AIEngine - INFO - Attempting LLM response generation...
2025-06-13 03:33:49,497 - AIEngine - INFO - Attempting LLM response generation...
2025-06-13 03:33:49,498 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-13 03:33:49,498 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-13 03:33:49,498 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-13 03:33:49,498 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-13 03:33:49,498 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-13 03:33:49,499 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-13 03:33:49,499 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-13 03:33:49,499 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-13 03:33:49,614 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-13 03:33:49,614 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'I'm processing that......'
2025-06-13 03:33:49,638 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-13 03:33:49,638 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'I'm processing that......'
2025-06-13 03:33:49,638 - UltraProfessionalInterface - INFO - 📝 Direct display for short response
2025-06-13 03:33:49,639 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-13 03:33:49,639 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:33:49] 🤖 Gideon: I'm proce...
2025-06-13 03:33:49,666 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:33:49,666 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:34:04,360 - AIEngine - INFO - 🌍 Response language set to: en
2025-06-13 03:34:04,361 - UltraProfessionalInterface - INFO - 🌍 AI engine response language set to English
2025-06-13 03:34:04,361 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Language changed to English 🇺🇸...
2025-06-13 03:34:04,361 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:34:04] ⚙️ System: Language...
2025-06-13 03:34:04,377 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:34:04,378 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:34:08,426 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-13 03:34:09,665 - AIEngine - INFO - 🌍 Response language set to: ar
2025-06-13 03:34:09,666 - UltraProfessionalInterface - INFO - 🌍 AI engine response language set to Arabic
2025-06-13 03:34:09,666 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> تم تغيير اللغة إلى العربية 🇸🇦...
2025-06-13 03:34:09,668 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: 🇸🇦 ﺔﻴﺑﺮﻌﻟﺍ ﻰﻟﺇ ﺔﻐﻠﻟﺍ ﺮﻴﻴﻐﺗ ﻢﺗ ...
2025-06-13 03:34:09,678 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:34:09,678 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:34:09,789 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-13 03:34:15,931 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> سلام...
2025-06-13 03:34:15,933 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻡﻼﺳ :You 👤 [03:34:15]
...
2025-06-13 03:34:15,942 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:34:15,943 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:34:15,945 - AIEngine - INFO - Generating response for: 'سلام'
2025-06-13 03:34:15,945 - AIEngine - INFO - Generating response for: 'سلام'
2025-06-13 03:34:15,945 - AIEngine - INFO - Attempting LLM response generation...
2025-06-13 03:34:15,946 - AIEngine - INFO - Attempting LLM response generation...
2025-06-13 03:34:15,946 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-13 03:34:15,946 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-13 03:34:15,947 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-13 03:34:15,947 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-13 03:34:15,947 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-13 03:34:15,947 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-13 03:34:15,948 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-13 03:34:15,948 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-13 03:34:16,053 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-13 03:34:16,053 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'سلام' -> 'I'm processing that......'
2025-06-13 03:34:16,065 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-13 03:34:16,065 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'I'm processing that......'
2025-06-13 03:34:16,066 - UltraProfessionalInterface - INFO - 📝 Direct display for short response
2025-06-13 03:34:16,066 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-13 03:34:16,066 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:34:16] 🤖 Gideon: I'm proce...
2025-06-13 03:34:16,097 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:34:16,098 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:34:28,940 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-13 03:34:28,941 - TerminalManager - INFO - Shutting down terminal manager...
2025-06-13 03:34:28,975 - TerminalManager - INFO - Created default terminal session: session_1
2025-06-13 03:34:28,975 - TerminalManager - INFO - Closed session: session_0
2025-06-13 03:34:28,976 - TerminalManager - INFO - Terminal manager shutdown complete
2025-06-13 03:34:29,244 - STTEngine - INFO - Stopped continuous listening
2025-06-13 03:34:29,531 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-13 03:34:29,664 - GideonMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-13 03:35:17,105 - AIEngine - INFO - LLM response generated successfully: مرحبا! اسمي جيديون، وana مساعد ذكي هنا لمساعدتك. إ...
2025-06-13 03:35:17,112 - AIEngine - INFO - LLM generated successful response: مرحبا! اسمي جيديون، وana مساعد ذكي هنا لمساعدتك. إ...
2025-06-13 03:35:17,128 - AIEngine - INFO - Final response generated: مرحبا! اسمي جيديون، وana مساعد ذكي هنا لمساعدتك. إ...
2025-06-13 03:35:26,420 - AIEngine - INFO - LLM response generated successfully: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-13 03:35:26,421 - AIEngine - INFO - LLM generated successful response: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-13 03:35:26,425 - AIEngine - INFO - Final response generated: مرحبا! كيف يمكنني مساعدتك اليوم؟...
2025-06-13 03:35:40,097 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-13 03:35:40,098 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-13 03:35:40,105 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-13 03:43:03,880 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-13 03:43:03,883 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-13 03:43:03,888 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-13 03:43:03,888 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-13 03:43:03,889 - MemorySystem - INFO - Memory system initialized successfully
2025-06-13 03:43:03,891 - ModelManager - INFO - No existing models config found
2025-06-13 03:43:03,891 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-13 03:43:03,892 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-13 03:43:03,893 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-13 03:43:03,893 - AIEngine - INFO - Initializing LLM backends...
2025-06-13 03:43:03,893 - AIEngine - INFO - ✅ Ollama backend available
2025-06-13 03:43:03,894 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-13 03:43:03,894 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-13 03:43:03,895 - AIEngine - INFO - ✅ Transformers backend available
2025-06-13 03:43:03,895 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-13 03:43:03,895 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-13 03:43:04,147 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-13 03:43:04,148 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-13 03:43:04,148 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-13 03:43:04,148 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-13 03:45:55,628 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-13 03:45:55,649 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-13 03:45:55,649 - AIEngine - INFO - AI Engine initialized successfully
2025-06-13 03:45:56,087 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (DualSense Wireless Co', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (DualSense Wireless Controller)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (DualSense Wireless Controller)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-13 03:45:56,157 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-13 03:45:56,386 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-13 03:45:56,388 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-13 03:45:59,397 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 158.1572750514722
2025-06-13 03:45:59,436 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-13 03:45:59,437 - STTEngine - INFO -    Energy threshold: 158.1572750514722
2025-06-13 03:45:59,437 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-13 03:45:59,438 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-13 03:45:59,439 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-13 03:45:59,440 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-13 03:45:59,440 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-13 03:45:59,440 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-13 03:45:59,441 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-13 03:46:00,434 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-13 03:46:00,434 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-13 03:46:00,435 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-13 03:46:03,738 - STTEngine - INFO - Testing microphone... Say something!
2025-06-13 03:46:07,744 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-13 03:46:11,951 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-13 03:46:11,952 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-13 03:46:11,953 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-13 03:46:11,953 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-13 03:46:12,926 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-13 03:46:13,291 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-13 03:46:13,874 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-13 03:46:13,874 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-13 03:46:13,915 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-13 03:46:13,928 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-13 03:46:13,928 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:13] ⚙️ System: Gideon A...
2025-06-13 03:46:14,258 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:14,258 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:14,258 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-13 03:46:14,258 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:14] ⚙️ System: Ultra-pr...
2025-06-13 03:46:14,259 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:14,259 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:14,259 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-13 03:46:14,260 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:14] ⚙️ System: Bilingua...
2025-06-13 03:46:14,260 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:14,260 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:14,260 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-13 03:46:14,261 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:14] ⚙️ System: Voice in...
2025-06-13 03:46:14,261 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:14,261 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:14,261 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-13 03:46:14,261 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:14] ⚙️ System: Ultra-lo...
2025-06-13 03:46:14,262 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:14,262 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:14,262 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-13 03:46:14,262 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:14] ⚙️ System: AI Model...
2025-06-13 03:46:14,263 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:14,263 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:14,264 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-13 03:46:14,264 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:14] ⚙️ System: Advanced...
2025-06-13 03:46:14,265 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:14,265 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:14,265 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-13 03:46:14,265 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:14] ⚙️ System: Drag & d...
2025-06-13 03:46:14,266 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:14,266 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:14,267 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-13 03:46:14,267 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:14] ⚙️ System: 🚀 Gideon...
2025-06-13 03:46:14,294 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:14,295 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:14,295 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-13 03:46:14,295 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:14] ⚙️ System: 💼 Ultra-...
2025-06-13 03:46:14,299 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:14,299 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:14,299 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-13 03:46:14,300 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:14] ⚙️ System: 🎯 Real-t...
2025-06-13 03:46:14,303 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:14,304 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:14,304 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-13 03:46:14,304 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:14] ⚙️ System: 🧠 Enterp...
2025-06-13 03:46:14,306 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:14,306 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:14,306 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-13 03:46:14,307 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:14] ⚙️ System: 💡 Advanc...
2025-06-13 03:46:14,310 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:14,311 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:14,311 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-13 03:46:14,311 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:14] ⚙️ System: 💬 Say 'G...
2025-06-13 03:46:14,312 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:14,312 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:16,320 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-13 03:46:16,321 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:16] 🤖 Gideon: Hello! I'...
2025-06-13 03:46:16,321 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:16,322 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:28,147 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> هلا...
2025-06-13 03:46:28,156 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫ :You 👤 [03:46:28]
...
2025-06-13 03:46:28,173 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:28,173 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:46:28,181 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-13 03:46:28,184 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-13 03:46:28,184 - AIEngine - INFO - Attempting LLM response generation...
2025-06-13 03:46:28,185 - AIEngine - INFO - Attempting LLM response generation...
2025-06-13 03:46:28,185 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-13 03:46:28,185 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-13 03:46:28,185 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-13 03:46:28,185 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-13 03:46:28,186 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-13 03:46:28,186 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-13 03:46:28,186 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-13 03:46:28,186 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-13 03:46:28,307 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'I'm processing that......'
2025-06-13 03:46:28,307 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'I'm processing that......'
2025-06-13 03:46:28,334 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'I'm processing that......'
2025-06-13 03:46:28,334 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'I'm processing that......'
2025-06-13 03:46:28,335 - UltraProfessionalInterface - INFO - 📝 Direct display for short response
2025-06-13 03:46:28,335 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> I'm processing that......
2025-06-13 03:46:28,335 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [03:46:28] 🤖 Gideon: I'm proce...
2025-06-13 03:46:28,364 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-13 03:46:28,364 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-13 03:53:11,734 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-13 03:53:11,781 - TerminalManager - INFO - Shutting down terminal manager...
2025-06-13 03:53:11,896 - TerminalManager - INFO - Created default terminal session: session_1
2025-06-13 03:53:11,896 - TerminalManager - INFO - Closed session: session_0
2025-06-13 03:53:11,902 - TerminalManager - INFO - Terminal manager shutdown complete
2025-06-13 03:53:11,905 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-13 03:53:12,123 - GideonMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
2025-06-13 03:53:13,362 - STTEngine - INFO - Stopped continuous listening
2025-06-13 03:53:51,485 - AIEngine - INFO - LLM response generated successfully: مرحبا! كيف يمكنني مساعدتك اليوم؟ لاحظ أنني سأجيب ب...
2025-06-13 03:53:51,502 - AIEngine - INFO - LLM generated successful response: مرحبا! كيف يمكنني مساعدتك اليوم؟ لاحظ أنني سأجيب ب...
2025-06-13 03:53:51,537 - AIEngine - INFO - Final response generated: مرحبا! كيف يمكنني مساعدتك اليوم؟ لاحظ أنني سأجيب ب...
2025-06-13 20:27:57,858 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-13 20:27:57,860 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-13 20:27:57,867 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-13 20:27:57,867 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-13 20:27:57,867 - MemorySystem - INFO - Memory system initialized successfully
2025-06-13 20:27:57,869 - ModelManager - INFO - No existing models config found
2025-06-13 20:27:57,870 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-13 20:27:57,870 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-13 20:27:57,871 - AIEngine - INFO - Loaded 29 learned response patterns
2025-06-13 20:27:57,871 - AIEngine - INFO - Initializing LLM backends...
2025-06-13 20:27:57,871 - AIEngine - INFO - ✅ Ollama backend available
2025-06-13 20:27:57,871 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-13 20:27:57,871 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-13 20:27:57,872 - AIEngine - INFO - ✅ Transformers backend available
2025-06-13 20:27:57,872 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-13 20:27:57,872 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-13 20:27:58,161 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-13 20:27:58,163 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-13 20:27:58,163 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-13 20:27:58,163 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-13 20:33:06,313 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-13 20:33:06,330 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-13 20:33:06,330 - AIEngine - INFO - AI Engine initialized successfully
2025-06-13 20:33:06,787 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Speakers (DualSense Wireless Co', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-13 20:33:06,861 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-13 20:33:07,093 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-13 20:33:07,094 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-13 20:33:10,103 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 88.61453796118009
2025-06-13 20:33:10,126 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-13 20:33:10,126 - STTEngine - INFO -    Energy threshold: 150
2025-06-13 20:33:10,126 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-13 20:33:10,126 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-13 20:33:10,127 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-13 20:33:10,127 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-13 20:33:10,127 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-13 20:33:10,129 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-13 20:33:10,129 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-13 20:33:11,635 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-13 20:33:11,636 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-13 20:33:11,637 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-13 20:33:14,929 - STTEngine - INFO - Testing microphone... Say something!
2025-06-13 20:33:17,956 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-13 20:33:22,141 - GideonCore - ERROR - Failed to initialize Gideon Core: 'VoiceCommandProcessor' object has no attribute '_cmd_switch_to_gemma3'
2025-06-13 20:33:22,148 - GideonMain - ERROR - Fatal error: 'VoiceCommandProcessor' object has no attribute '_cmd_switch_to_gemma3'
2025-06-13 20:33:22,163 - GideonMain - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\vrz\main_ultra_pro.py", line 178, in main
    gideon_core.initialize()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\vrz\src\core\gideon_core.py", line 101, in initialize
    self.voice_commands = VoiceCommandProcessor(self)
                          ~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\vrz\src\system\voice_commands.py", line 29, in __init__
    self._register_builtin_commands()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\augment-projects\vrz\src\system\voice_commands.py", line 291, in _register_builtin_commands
    self._cmd_switch_to_gemma3,
    ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'VoiceCommandProcessor' object has no attribute '_cmd_switch_to_gemma3'. Did you mean: '_cmd_switch_to_gemma'?

