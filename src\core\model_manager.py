"""
Multi-Model Manager for Gideon AI Assistant
Supports drag and drop functionality for adding AI models
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

from src.utils.logger import GideonLogger
from src.utils.config import Config


class ModelInfo:
    """Information about an AI model"""
    
    def __init__(self, name: str, path: str, model_type: str = "unknown", 
                 description: str = "", size: int = 0, capabilities: List[str] = None):
        self.name = name
        self.path = path
        self.model_type = model_type
        self.description = description
        self.size = size
        self.capabilities = capabilities or []
        self.added_date = datetime.now().isoformat()
        self.is_active = False
        self.load_time = 0.0
        self.performance_stats = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "name": self.name,
            "path": self.path,
            "model_type": self.model_type,
            "description": self.description,
            "size": self.size,
            "capabilities": self.capabilities,
            "added_date": self.added_date,
            "is_active": self.is_active,
            "load_time": self.load_time,
            "performance_stats": self.performance_stats
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelInfo':
        """Create from dictionary"""
        model = cls(
            name=data.get("name", ""),
            path=data.get("path", ""),
            model_type=data.get("model_type", "unknown"),
            description=data.get("description", ""),
            size=data.get("size", 0),
            capabilities=data.get("capabilities", [])
        )
        model.added_date = data.get("added_date", datetime.now().isoformat())
        model.is_active = data.get("is_active", False)
        model.load_time = data.get("load_time", 0.0)
        model.performance_stats = data.get("performance_stats", {})
        return model


class ModelManager:
    """Manages multiple AI models with drag and drop support"""
    
    def __init__(self):
        self.logger = GideonLogger("ModelManager")
        self.config = Config()
        
        # Model storage
        self.models_dir = Path("data/models")
        self.models_config_file = self.models_dir / "models.json"
        self.models: Dict[str, ModelInfo] = {}
        
        # Supported model formats
        self.supported_formats = {
            ".gguf": "GGUF",
            ".bin": "Binary",
            ".safetensors": "SafeTensors",
            ".pt": "PyTorch",
            ".pth": "PyTorch",
            ".onnx": "ONNX",
            ".tflite": "TensorFlow Lite",
            ".h5": "Keras/HDF5",
            ".pkl": "Pickle",
            ".joblib": "Joblib"
        }
        
        # Model capabilities detection
        self.capability_keywords = {
            "chat": ["chat", "conversation", "instruct", "dialogue"],
            "code": ["code", "programming", "python", "javascript", "coding"],
            "math": ["math", "mathematics", "calculation", "arithmetic"],
            "reasoning": ["reasoning", "logic", "thinking", "analysis"],
            "creative": ["creative", "writing", "story", "poetry", "art"],
            "multilingual": ["multilingual", "translate", "language", "arabic", "chinese"],
            "vision": ["vision", "image", "visual", "ocr", "multimodal"],
            "audio": ["audio", "speech", "sound", "music", "voice"]
        }
        
        # Initialize
        self._ensure_directories()
        self._load_models_config()
    
    def _ensure_directories(self):
        """Ensure model directories exist"""
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories for different model types
        for model_type in ["chat", "code", "vision", "audio", "custom"]:
            (self.models_dir / model_type).mkdir(exist_ok=True)
    
    def _load_models_config(self):
        """Load models configuration"""
        try:
            if self.models_config_file.exists():
                with open(self.models_config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for model_id, model_data in data.items():
                    self.models[model_id] = ModelInfo.from_dict(model_data)
                
                self.logger.info(f"Loaded {len(self.models)} models from config")
            else:
                self.logger.info("No existing models config found")
                
        except Exception as e:
            self.logger.error(f"Error loading models config: {e}")
    
    def _save_models_config(self):
        """Save models configuration"""
        try:
            data = {}
            for model_id, model_info in self.models.items():
                data[model_id] = model_info.to_dict()
            
            with open(self.models_config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug("Models config saved")
            
        except Exception as e:
            self.logger.error(f"Error saving models config: {e}")
    
    def add_model_from_file(self, file_path: str, model_name: str = None, 
                           model_type: str = None, description: str = "") -> Optional[str]:
        """Add a model from a file (supports drag and drop)"""
        try:
            source_path = Path(file_path)
            
            if not source_path.exists():
                self.logger.error(f"Model file not found: {file_path}")
                return None
            
            # Detect model format
            file_extension = source_path.suffix.lower()
            if file_extension not in self.supported_formats:
                self.logger.warning(f"Unsupported model format: {file_extension}")
                # Still allow it, but mark as unknown
                detected_type = "unknown"
            else:
                detected_type = self.supported_formats[file_extension]
            
            # Generate model name if not provided
            if not model_name:
                model_name = source_path.stem
            
            # Detect model type and capabilities
            if not model_type:
                model_type = self._detect_model_type(model_name, source_path)
            
            capabilities = self._detect_capabilities(model_name, description)
            
            # Create unique model ID
            model_id = self._generate_model_id(model_name)
            
            # Determine destination path
            dest_dir = self.models_dir / model_type
            dest_path = dest_dir / f"{model_id}{source_path.suffix}"
            
            # Copy model file
            self.logger.info(f"Copying model from {source_path} to {dest_path}")
            shutil.copy2(source_path, dest_path)
            
            # Get file size
            file_size = dest_path.stat().st_size
            
            # Create model info
            model_info = ModelInfo(
                name=model_name,
                path=str(dest_path),
                model_type=detected_type,
                description=description,
                size=file_size,
                capabilities=capabilities
            )
            
            # Add to models registry
            self.models[model_id] = model_info
            self._save_models_config()
            
            self.logger.info(f"Model '{model_name}' added successfully with ID: {model_id}")
            return model_id
            
        except Exception as e:
            self.logger.error(f"Error adding model from file: {e}")
            return None
    
    def _detect_model_type(self, model_name: str, file_path: Path) -> str:
        """Detect model type from name and path"""
        name_lower = model_name.lower()
        
        # Check for common model type indicators
        if any(keyword in name_lower for keyword in ["chat", "instruct", "conversation"]):
            return "chat"
        elif any(keyword in name_lower for keyword in ["code", "programming", "python"]):
            return "code"
        elif any(keyword in name_lower for keyword in ["vision", "image", "visual"]):
            return "vision"
        elif any(keyword in name_lower for keyword in ["audio", "speech", "whisper"]):
            return "audio"
        else:
            return "custom"
    
    def _detect_capabilities(self, model_name: str, description: str) -> List[str]:
        """Detect model capabilities from name and description"""
        text = f"{model_name} {description}".lower()
        capabilities = []
        
        for capability, keywords in self.capability_keywords.items():
            if any(keyword in text for keyword in keywords):
                capabilities.append(capability)
        
        return capabilities
    
    def _generate_model_id(self, model_name: str) -> str:
        """Generate unique model ID"""
        base_id = model_name.lower().replace(" ", "_").replace("-", "_")
        base_id = "".join(c for c in base_id if c.isalnum() or c == "_")
        
        # Ensure uniqueness
        counter = 1
        model_id = base_id
        while model_id in self.models:
            model_id = f"{base_id}_{counter}"
            counter += 1
        
        return model_id
    
    def remove_model(self, model_id: str) -> bool:
        """Remove a model"""
        try:
            if model_id not in self.models:
                self.logger.warning(f"Model not found: {model_id}")
                return False
            
            model_info = self.models[model_id]
            
            # Remove model file
            model_path = Path(model_info.path)
            if model_path.exists():
                model_path.unlink()
                self.logger.info(f"Removed model file: {model_path}")
            
            # Remove from registry
            del self.models[model_id]
            self._save_models_config()
            
            self.logger.info(f"Model '{model_info.name}' removed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error removing model: {e}")
            return False
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get model information"""
        return self.models.get(model_id)
    
    def list_models(self, model_type: str = None, capability: str = None) -> List[ModelInfo]:
        """List models with optional filtering"""
        models = list(self.models.values())
        
        if model_type:
            models = [m for m in models if m.model_type == model_type]
        
        if capability:
            models = [m for m in models if capability in m.capabilities]
        
        return models
    
    def get_active_models(self) -> List[ModelInfo]:
        """Get currently active models"""
        return [m for m in self.models.values() if m.is_active]
    
    def activate_model(self, model_id: str) -> bool:
        """Activate a model"""
        if model_id in self.models:
            self.models[model_id].is_active = True
            self._save_models_config()
            self.logger.info(f"Model {model_id} activated")
            return True
        return False
    
    def deactivate_model(self, model_id: str) -> bool:
        """Deactivate a model"""
        if model_id in self.models:
            self.models[model_id].is_active = False
            self._save_models_config()
            self.logger.info(f"Model {model_id} deactivated")
            return True
        return False
    
    def get_model_stats(self) -> Dict[str, Any]:
        """Get model statistics"""
        total_models = len(self.models)
        active_models = len(self.get_active_models())
        total_size = sum(m.size for m in self.models.values())
        
        type_counts = {}
        for model in self.models.values():
            type_counts[model.model_type] = type_counts.get(model.model_type, 0) + 1
        
        return {
            "total_models": total_models,
            "active_models": active_models,
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "types": type_counts,
            "supported_formats": list(self.supported_formats.keys())
        }
