#!/usr/bin/env python3
"""
Enterprise Data Management System for Gideon AI Assistant
Comprehensive data export/import, backup/restore, and persistence management
"""

import json
import csv
import sqlite3
import zipfile
import shutil
import hashlib
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from src.utils.logger import <PERSON><PERSON><PERSON><PERSON>

@dataclass
class ConversationRecord:
    """Data class for conversation records"""
    id: str
    timestamp: str
    user_message: str
    ai_response: str
    language: str
    model_used: str
    response_time: float
    metadata: Dict[str, Any]

@dataclass
class BackupMetadata:
    """Data class for backup metadata"""
    backup_id: str
    created_at: str
    version: str
    data_types: List[str]
    file_count: int
    total_size: int
    checksum: str

class EnterpriseDataManager:
    """Enterprise-grade data management system"""
    
    def __init__(self):
        self.logger = GideonLogger("EnterpriseDataManager")
        
        # Data directories
        self.data_dir = Path("data")
        self.conversations_dir = self.data_dir / "conversations"
        self.backups_dir = self.data_dir / "backups"
        self.exports_dir = self.data_dir / "exports"
        self.cache_dir = self.data_dir / "cache"
        
        # Create directories
        for directory in [self.conversations_dir, self.backups_dir, self.exports_dir, self.cache_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # Database setup
        self.db_path = self.data_dir / "gideon_enterprise.db"
        self._initialize_database()
        
        # Data retention settings
        self.max_conversation_age_days = 365
        self.max_backup_age_days = 90
        self.max_cache_size_mb = 500
        
        # Export formats
        self.supported_export_formats = ['json', 'csv', 'txt', 'xml', 'sqlite']
        
    def _initialize_database(self):
        """Initialize SQLite database for data storage"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Conversations table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS conversations (
                        id TEXT PRIMARY KEY,
                        timestamp TEXT NOT NULL,
                        user_message TEXT NOT NULL,
                        ai_response TEXT NOT NULL,
                        language TEXT,
                        model_used TEXT,
                        response_time REAL,
                        metadata TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # User preferences table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_preferences (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # System logs table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        level TEXT NOT NULL,
                        component TEXT,
                        message TEXT NOT NULL,
                        metadata TEXT
                    )
                ''')
                
                # Performance metrics table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS performance_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        metric_name TEXT NOT NULL,
                        metric_value REAL NOT NULL,
                        unit TEXT,
                        metadata TEXT
                    )
                ''')
                
                # Create indexes for better performance
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_conversations_timestamp ON conversations(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON performance_metrics(timestamp)')
                
                conn.commit()
                self.logger.info("Database initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Error initializing database: {e}")
    
    def save_conversation(self, conversation: ConversationRecord) -> bool:
        """Save a conversation record to the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO conversations 
                    (id, timestamp, user_message, ai_response, language, model_used, response_time, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    conversation.id,
                    conversation.timestamp,
                    conversation.user_message,
                    conversation.ai_response,
                    conversation.language,
                    conversation.model_used,
                    conversation.response_time,
                    json.dumps(conversation.metadata)
                ))
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error saving conversation: {e}")
            return False
    
    def get_conversations(self, limit: int = 100, offset: int = 0, 
                         start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[ConversationRecord]:
        """Retrieve conversations from the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = 'SELECT * FROM conversations'
                params = []
                
                if start_date or end_date:
                    query += ' WHERE'
                    conditions = []
                    if start_date:
                        conditions.append(' timestamp >= ?')
                        params.append(start_date)
                    if end_date:
                        conditions.append(' timestamp <= ?')
                        params.append(end_date)
                    query += ' AND'.join(conditions)
                
                query += ' ORDER BY timestamp DESC LIMIT ? OFFSET ?'
                params.extend([limit, offset])
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                conversations = []
                for row in rows:
                    metadata = json.loads(row[7]) if row[7] else {}
                    conversation = ConversationRecord(
                        id=row[0],
                        timestamp=row[1],
                        user_message=row[2],
                        ai_response=row[3],
                        language=row[4],
                        model_used=row[5],
                        response_time=row[6],
                        metadata=metadata
                    )
                    conversations.append(conversation)
                
                return conversations
                
        except Exception as e:
            self.logger.error(f"Error retrieving conversations: {e}")
            return []
    
    def export_conversations(self, format: str, output_path: str, 
                           start_date: Optional[str] = None, end_date: Optional[str] = None) -> bool:
        """Export conversations in specified format"""
        if format not in self.supported_export_formats:
            self.logger.error(f"Unsupported export format: {format}")
            return False
        
        try:
            conversations = self.get_conversations(limit=10000, start_date=start_date, end_date=end_date)
            
            if format == 'json':
                return self._export_to_json(conversations, output_path)
            elif format == 'csv':
                return self._export_to_csv(conversations, output_path)
            elif format == 'txt':
                return self._export_to_txt(conversations, output_path)
            elif format == 'xml':
                return self._export_to_xml(conversations, output_path)
            elif format == 'sqlite':
                return self._export_to_sqlite(conversations, output_path)
            
        except Exception as e:
            self.logger.error(f"Error exporting conversations: {e}")
            return False
    
    def _export_to_json(self, conversations: List[ConversationRecord], output_path: str) -> bool:
        """Export conversations to JSON format"""
        try:
            data = [asdict(conv) for conv in conversations]
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Exported {len(conversations)} conversations to JSON: {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error exporting to JSON: {e}")
            return False
    
    def _export_to_csv(self, conversations: List[ConversationRecord], output_path: str) -> bool:
        """Export conversations to CSV format"""
        try:
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                if conversations:
                    fieldnames = list(asdict(conversations[0]).keys())
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    for conv in conversations:
                        row = asdict(conv)
                        row['metadata'] = json.dumps(row['metadata'])
                        writer.writerow(row)
            self.logger.info(f"Exported {len(conversations)} conversations to CSV: {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error exporting to CSV: {e}")
            return False
    
    def _export_to_txt(self, conversations: List[ConversationRecord], output_path: str) -> bool:
        """Export conversations to readable text format"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("Gideon AI Assistant - Conversation Export\n")
                f.write("=" * 50 + "\n\n")
                
                for conv in conversations:
                    f.write(f"Timestamp: {conv.timestamp}\n")
                    f.write(f"Language: {conv.language}\n")
                    f.write(f"Model: {conv.model_used}\n")
                    f.write(f"Response Time: {conv.response_time:.2f}s\n")
                    f.write("-" * 30 + "\n")
                    f.write(f"User: {conv.user_message}\n")
                    f.write(f"Gideon: {conv.ai_response}\n")
                    f.write("\n" + "=" * 50 + "\n\n")
            
            self.logger.info(f"Exported {len(conversations)} conversations to TXT: {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error exporting to TXT: {e}")
            return False
    
    def _export_to_xml(self, conversations: List[ConversationRecord], output_path: str) -> bool:
        """Export conversations to XML format"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
                f.write('<conversations>\n')
                
                for conv in conversations:
                    f.write('  <conversation>\n')
                    f.write(f'    <id>{conv.id}</id>\n')
                    f.write(f'    <timestamp>{conv.timestamp}</timestamp>\n')
                    f.write(f'    <user_message><![CDATA[{conv.user_message}]]></user_message>\n')
                    f.write(f'    <ai_response><![CDATA[{conv.ai_response}]]></ai_response>\n')
                    f.write(f'    <language>{conv.language}</language>\n')
                    f.write(f'    <model_used>{conv.model_used}</model_used>\n')
                    f.write(f'    <response_time>{conv.response_time}</response_time>\n')
                    f.write('  </conversation>\n')
                
                f.write('</conversations>\n')
            
            self.logger.info(f"Exported {len(conversations)} conversations to XML: {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error exporting to XML: {e}")
            return False
    
    def _export_to_sqlite(self, conversations: List[ConversationRecord], output_path: str) -> bool:
        """Export conversations to SQLite database"""
        try:
            with sqlite3.connect(output_path) as conn:
                cursor = conn.cursor()
                
                # Create table
                cursor.execute('''
                    CREATE TABLE conversations (
                        id TEXT PRIMARY KEY,
                        timestamp TEXT,
                        user_message TEXT,
                        ai_response TEXT,
                        language TEXT,
                        model_used TEXT,
                        response_time REAL,
                        metadata TEXT
                    )
                ''')
                
                # Insert data
                for conv in conversations:
                    cursor.execute('''
                        INSERT INTO conversations VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        conv.id, conv.timestamp, conv.user_message, conv.ai_response,
                        conv.language, conv.model_used, conv.response_time,
                        json.dumps(conv.metadata)
                    ))
                
                conn.commit()
            
            self.logger.info(f"Exported {len(conversations)} conversations to SQLite: {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error exporting to SQLite: {e}")
            return False
    
    def create_backup(self, backup_name: Optional[str] = None) -> Optional[str]:
        """Create a comprehensive backup of all data"""
        try:
            if not backup_name:
                backup_name = f"gideon_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            backup_path = self.backups_dir / f"{backup_name}.zip"
            
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Backup database
                if self.db_path.exists():
                    zipf.write(self.db_path, "database/gideon_enterprise.db")
                
                # Backup configuration files
                config_files = [
                    "data/settings/user_preferences.json",
                    "data/settings/ui_preferences.json",
                    "data/settings/performance_settings.json"
                ]
                
                for config_file in config_files:
                    config_path = Path(config_file)
                    if config_path.exists():
                        zipf.write(config_path, f"config/{config_path.name}")
                
                # Backup conversation files
                if self.conversations_dir.exists():
                    for file_path in self.conversations_dir.rglob("*"):
                        if file_path.is_file():
                            arcname = f"conversations/{file_path.relative_to(self.conversations_dir)}"
                            zipf.write(file_path, arcname)
            
            # Create backup metadata
            metadata = BackupMetadata(
                backup_id=backup_name,
                created_at=datetime.now().isoformat(),
                version="2.0.0",
                data_types=["conversations", "settings", "database"],
                file_count=len(zipf.namelist()) if 'zipf' in locals() else 0,
                total_size=backup_path.stat().st_size,
                checksum=self._calculate_file_checksum(backup_path)
            )
            
            # Save metadata
            metadata_path = self.backups_dir / f"{backup_name}_metadata.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(asdict(metadata), f, indent=2)
            
            self.logger.info(f"Backup created successfully: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            self.logger.error(f"Error creating backup: {e}")
            return None
    
    def restore_backup(self, backup_path: str) -> bool:
        """Restore data from a backup file"""
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                self.logger.error(f"Backup file not found: {backup_path}")
                return False
            
            # Create restore directory
            restore_dir = self.data_dir / "restore_temp"
            restore_dir.mkdir(exist_ok=True)
            
            # Extract backup
            with zipfile.ZipFile(backup_file, 'r') as zipf:
                zipf.extractall(restore_dir)
            
            # Restore database
            db_backup = restore_dir / "database" / "gideon_enterprise.db"
            if db_backup.exists():
                shutil.copy2(db_backup, self.db_path)
                self.logger.info("Database restored successfully")
            
            # Restore configuration files
            config_backup_dir = restore_dir / "config"
            if config_backup_dir.exists():
                settings_dir = self.data_dir / "settings"
                settings_dir.mkdir(exist_ok=True)
                
                for config_file in config_backup_dir.iterdir():
                    if config_file.is_file():
                        shutil.copy2(config_file, settings_dir / config_file.name)
                
                self.logger.info("Configuration files restored successfully")
            
            # Restore conversations
            conv_backup_dir = restore_dir / "conversations"
            if conv_backup_dir.exists():
                if self.conversations_dir.exists():
                    shutil.rmtree(self.conversations_dir)
                shutil.copytree(conv_backup_dir, self.conversations_dir)
                self.logger.info("Conversations restored successfully")
            
            # Cleanup
            shutil.rmtree(restore_dir)
            
            self.logger.info(f"Backup restored successfully from: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error restoring backup: {e}")
            return False
    
    def _calculate_file_checksum(self, file_path: Path) -> str:
        """Calculate SHA-256 checksum of a file"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def cleanup_old_data(self):
        """Clean up old data based on retention policies"""
        try:
            # Clean old conversations
            cutoff_date = datetime.now() - timedelta(days=self.max_conversation_age_days)
            cutoff_str = cutoff_date.isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM conversations WHERE timestamp < ?', (cutoff_str,))
                deleted_conversations = cursor.rowcount
                conn.commit()
            
            if deleted_conversations > 0:
                self.logger.info(f"Cleaned up {deleted_conversations} old conversations")
            
            # Clean old backups
            backup_cutoff = datetime.now() - timedelta(days=self.max_backup_age_days)
            for backup_file in self.backups_dir.glob("*.zip"):
                if backup_file.stat().st_mtime < backup_cutoff.timestamp():
                    backup_file.unlink()
                    # Also remove metadata file
                    metadata_file = backup_file.with_suffix('.json')
                    if metadata_file.exists():
                        metadata_file.unlink()
                    self.logger.info(f"Removed old backup: {backup_file.name}")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def get_data_statistics(self) -> Dict[str, Any]:
        """Get comprehensive data statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Conversation statistics
                cursor.execute('SELECT COUNT(*) FROM conversations')
                total_conversations = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(DISTINCT language) FROM conversations')
                languages_used = cursor.fetchone()[0]
                
                cursor.execute('SELECT AVG(response_time) FROM conversations WHERE response_time > 0')
                avg_response_time = cursor.fetchone()[0] or 0
                
                # Database size
                db_size = self.db_path.stat().st_size if self.db_path.exists() else 0
                
                return {
                    'total_conversations': total_conversations,
                    'languages_used': languages_used,
                    'average_response_time': round(avg_response_time, 2),
                    'database_size_mb': round(db_size / (1024 * 1024), 2),
                    'backup_count': len(list(self.backups_dir.glob("*.zip"))),
                    'last_backup': self._get_last_backup_date()
                }
                
        except Exception as e:
            self.logger.error(f"Error getting data statistics: {e}")
            return {}
    
    def _get_last_backup_date(self) -> Optional[str]:
        """Get the date of the last backup"""
        try:
            backup_files = list(self.backups_dir.glob("*.zip"))
            if backup_files:
                latest_backup = max(backup_files, key=lambda f: f.stat().st_mtime)
                return datetime.fromtimestamp(latest_backup.stat().st_mtime).isoformat()
        except Exception:
            pass
        return None

# Global instance
enterprise_data_manager = EnterpriseDataManager()
