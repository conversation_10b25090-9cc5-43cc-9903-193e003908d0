#!/usr/bin/env python3
"""
Test Chat with Debug - Quick test to verify chat functionality
"""

import sys
import os
import threading
import time
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def test_chat_with_interface():
    """Test chat functionality with actual interface"""
    print("🧪 Testing Chat with Debug Interface")
    print("=" * 50)
    
    try:
        # Import modules
        from src.core.gideon_core import Gideon<PERSON>ore
        from src.ui.ultra_professional_interface import UltraProfessionalInterface
        
        print("✅ Modules imported")
        
        # Initialize core
        print("🔧 Initializing Gideon core...")
        gideon_core = GideonCore()
        gideon_core.initialize()
        print("✅ Gideon core initialized")
        
        # Create interface
        print("🎨 Creating interface...")
        interface = UltraProfessionalInterface(gideon_core)
        root = interface.create_window()
        print("✅ Interface created")
        
        # Add test message to verify display works
        print("📝 Adding test message...")
        interface._add_message("System", "🧪 Chat test interface ready! Type a message and press Enter.", interface.design_system['colors']['accent_success'])
        interface._add_message("System", "🔍 Debug mode active - check console for detailed logs", interface.design_system['colors']['text_secondary'])
        
        # Simulate a test message after a short delay
        def simulate_test_message():
            time.sleep(2)  # Wait 2 seconds
            print("\n🤖 Simulating test message...")
            
            # Simulate typing in the input field
            if hasattr(interface, 'input_entry') and interface.input_entry:
                interface.input_entry.insert(0, "hello test")
                print("✅ Test message inserted into input field")
                
                # Simulate pressing Enter (call _send_message directly)
                print("⌨️ Simulating Enter key press...")
                interface._send_message()
                print("✅ _send_message called")
            else:
                print("❌ Input entry not found")
        
        # Start simulation in background
        threading.Thread(target=simulate_test_message, daemon=True).start()
        
        print("\n" + "=" * 50)
        print("🚀 CHAT INTERFACE READY!")
        print("=" * 50)
        print("💡 Instructions:")
        print("1. Type a message in the input field")
        print("2. Press Enter or click Send")
        print("3. Watch the console for debug messages")
        print("4. Check if AI responds")
        print("\n🔍 Debug messages will show:")
        print("   - When _send_message is called")
        print("   - Message processing steps")
        print("   - AI response generation")
        print("   - Any errors that occur")
        print("\n⚠️ If you don't see debug messages when typing,")
        print("   the event binding might not be working.")
        print("\n🎯 Close the window when done testing.")
        print("=" * 50)
        
        # Start the interface
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_chat_with_interface()
