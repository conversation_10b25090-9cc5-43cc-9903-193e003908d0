=== DIAGNOSTIC SESSION STARTED ===
Start time: 2025-06-20 23:29:13

[23:29:13] [0.000s] STEP: Diagnostic Mode Start - Beginning comprehensive diagnostic
[23:29:13] [0.001s] STEP: PHASE 1 - System Resource Analysis
[23:29:13] [0.001s] STEP: System Resource Check - Starting resource analysis
[23:29:13] [0.117s] STEP: psutil import - Successfully imported psutil
[23:29:13] [0.118s] STEP: CPU Check - Getting CPU usage
[23:29:13] [0.219s] STEP: CPU Result - CPU Usage: 10.3%
[23:29:13] [0.219s] STEP: Memory Check - Getting memory information
[23:29:13] [0.222s] STEP: Memory Result - Available: 20.0 GB, Used: 35.7%
[23:29:13] [0.223s] STEP: Disk Check - Getting disk information
[23:29:13] [0.223s] STEP: Disk Result - Free: 131.1 GB
[23:29:13] [0.224s] STEP: Process Check - Getting process information
[23:29:13] [0.224s] STEP: Process Result - Current process memory: 18.9 MB
[23:29:13] [0.224s] ✅ SUCCESS: System Resource Check (CPU: 10.3%, RAM: 35.7%)
[23:29:13] [0.224s] STEP: Mode Selection - Resources adequate - Standard mode possible
[23:29:13] [0.225s] STEP: PHASE 2 - Dependency Verification
[23:29:13] [0.225s] STEP: Dependency Check - Checking dependencies for standard mode
[23:29:13] [0.225s] STEP: Checking customtkinter - Required: True
[23:29:16] [2.810s] ✅ SUCCESS: customtkinter import (2584.7ms)
[23:29:16] [2.811s] STEP: Checking Pillow - Required: True
[23:29:16] [2.811s] ✅ SUCCESS: Pillow import (0.0ms)
[23:29:16] [2.811s] STEP: Checking speech_recognition - Required: True
[23:29:16] [3.189s] ✅ SUCCESS: speech_recognition import (377.7ms)
[23:29:16] [3.190s] STEP: Checking pyttsx3 - Required: True
[23:29:16] [3.219s] ✅ SUCCESS: pyttsx3 import (29.0ms)
[23:29:16] [3.220s] STEP: Checking pyaudio - Required: True
[23:29:16] [3.256s] ✅ SUCCESS: pyaudio import (36.0ms)
[23:29:16] [3.256s] STEP: Checking psutil - Required: True
[23:29:16] [3.257s] ✅ SUCCESS: psutil import (0.0ms)
[23:29:16] [3.257s] STEP: Checking ollama - Required: True
[23:29:18] [4.378s] ✅ SUCCESS: ollama import (1120.4ms)
[23:29:18] [4.378s] STEP: Checking transformers - Required: True
[23:29:22] [8.870s] ✅ SUCCESS: transformers import (4491.6ms)
[23:29:22] [8.871s] STEP: Checking torch - Required: True
[23:29:22] [8.871s] ✅ SUCCESS: torch import (0.0ms)
[23:29:22] [8.871s] ✅ SUCCESS: Dependency Check (All required dependencies available for standard mode)
[23:29:22] [8.872s] STEP: PHASE 3 - Component Loading Test
[23:29:22] [8.872s] STEP: Component Loading Test - Testing individual component imports
[23:29:22] [8.872s] STEP: Testing GideonCore - Importing from src.core.gideon_core
[23:29:25] [11.959s] ✅ SUCCESS: GideonCore import (3086.9ms)
[23:29:25] [11.960s] STEP: Testing AIEngine - Importing from src.core.ai_engine
[23:29:25] [11.960s] ✅ SUCCESS: AIEngine import (0.0ms)
[23:29:25] [11.961s] STEP: Testing UltraProfessionalInterface - Importing from src.ui.ultra_professional_interface
[23:29:25] [11.994s] ✅ SUCCESS: UltraProfessionalInterface import (32.6ms)
[23:29:25] [11.994s] STEP: Testing STTEngine - Importing from src.speech.stt_engine
[23:29:25] [11.994s] ✅ SUCCESS: STTEngine import (0.0ms)
[23:29:25] [11.994s] STEP: Testing TTSEngine - Importing from src.speech.tts_engine
[23:29:25] [11.995s] ✅ SUCCESS: TTSEngine import (0.0ms)
[23:29:25] [11.995s] STEP: Testing MemorySystem - Importing from src.core.memory_system
[23:29:25] [11.995s] ✅ SUCCESS: MemorySystem import (0.0ms)
[23:29:25] [11.995s] STEP: Testing ModelManager - Importing from src.core.model_manager
[23:29:25] [11.996s] ✅ SUCCESS: ModelManager import (0.0ms)
[23:29:25] [11.996s] STEP: PHASE 4 - Memory Allocation Test
[23:29:25] [11.996s] STEP: Memory Allocation Test - Testing memory allocation patterns
[23:29:25] [11.997s] STEP: Initial Memory - 323.1 MB
[23:29:25] [11.997s] STEP: Small Allocation Test - Allocating 10MB
[23:29:25] [11.998s] STEP: Small Allocation Result - 333.1 MB (+10.0 MB)
[23:29:25] [11.999s] STEP: Medium Allocation Test - Allocating 100MB
[23:29:25] [12.010s] STEP: Medium Allocation Result - 433.2 MB (+100.0 MB)
[23:29:25] [12.093s] STEP: Memory Cleanup - 323.1 MB (freed 110.0 MB)
[23:29:25] [12.093s] ✅ SUCCESS: Memory Allocation Test (Memory allocation working normally)
[23:29:25] [12.094s] STEP: PHASE 5 - Threading Test
[23:29:25] [12.094s] STEP: Threading Test - Testing thread creation and management
[23:29:25] [12.094s] STEP: Simple Thread Test - Creating simple thread
[23:29:25] [12.195s] STEP: Thread Task - Simple thread completed
[23:29:25] [12.196s] ✅ SUCCESS: Simple Thread Test (Thread completed successfully)
[23:29:25] [12.196s] STEP: Multiple Thread Test - Creating 5 threads
[23:29:25] [12.247s] STEP: Thread 0 - Thread 0 completed
[23:29:25] [12.247s] STEP: Thread 4 - Thread 4 completed
[23:29:25] [12.248s] STEP: Thread 2 - Thread 2 completed
[23:29:25] [12.248s] STEP: Thread 3 - Thread 3 completed
[23:29:25] [12.248s] STEP: Thread 1 - Thread 1 completed
[23:29:25] [12.249s] ✅ SUCCESS: Multiple Thread Test (All threads completed successfully)
[23:29:25] [12.249s] STEP: DIAGNOSTIC SUMMARY - Generating final report
[23:29:25] [12.249s] ✅ SUCCESS: Diagnostic Summary (All tests passed - system should be stable)
