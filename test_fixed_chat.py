#!/usr/bin/env python3
"""
Test Fixed Chat Interface
Quick test to verify the event binding fix works
"""

import sys
import os
import threading
import time
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def test_fixed_chat():
    """Test the fixed chat interface"""
    print("🧪 Testing Fixed Chat Interface")
    print("=" * 50)
    
    try:
        # Import modules
        from src.core.gideon_core import GideonCore
        from src.ui.ultra_professional_interface import UltraProfessionalInterface
        
        print("✅ Modules imported")
        
        # Initialize core
        print("🔧 Initializing Gideon core...")
        gideon_core = GideonCore()
        gideon_core.initialize()
        print("✅ Gideon core initialized")
        
        # Create interface
        print("🎨 Creating interface...")
        interface = UltraProfessionalInterface(gideon_core)
        root = interface.create_window()
        print("✅ Interface created")
        
        # Add welcome message
        interface._add_message("System", "🎉 Chat interface fixed! Event bindings should now work.", interface.design_system['colors']['accent_success'])
        interface._add_message("System", "✅ Try typing a message and pressing Enter", interface.design_system['colors']['text_secondary'])
        interface._add_message("System", "✅ Or click the Send button", interface.design_system['colors']['text_secondary'])
        
        # Test automatic message after delay
        def test_automatic_message():
            time.sleep(3)
            print("\n🤖 Testing automatic message...")
            
            # Insert test message
            if hasattr(interface, 'input_entry') and interface.input_entry:
                interface.input_entry.insert(0, "test fixed chat")
                print("✅ Test message inserted")
                
                # Wait a moment then simulate Enter
                time.sleep(1)
                print("⌨️ Simulating Enter key...")
                interface.input_entry.event_generate('<Return>')
                print("✅ Enter key event generated")
        
        # Start test in background
        threading.Thread(target=test_automatic_message, daemon=True).start()
        
        print("\n" + "=" * 50)
        print("🚀 FIXED CHAT INTERFACE READY!")
        print("=" * 50)
        print("💡 The event binding issue has been fixed!")
        print("📝 Try typing a message and pressing Enter")
        print("🖱️ Or click the Send button")
        print("🔍 Watch console for debug messages:")
        print("   - '⌨️ Enter key pressed!' when you press Enter")
        print("   - '🖱️ Send button clicked!' when you click Send")
        print("   - '🔥 _send_message called!' when processing")
        print("\n🎯 Close window when done testing")
        print("=" * 50)
        
        # Start the interface
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_fixed_chat()
