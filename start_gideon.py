#!/usr/bin/env python3
"""
<PERSON> AI Assistant - Smart Startup Launcher

Automatically detects system issues and chooses the safest startup mode.
"""

import sys
import time
import subprocess
from pathlib import Path

print("🤖 GIDEON AI ASSISTANT - SMART LAUNCHER")
print("=" * 60)
print("Automatically selecting the safest startup mode for your system")
print("=" * 60)

def check_system_safety():
    """Check if system is safe for normal startup"""
    try:
        import psutil
        
        # Quick system check
        cpu_percent = psutil.cpu_percent(interval=0.5)
        memory = psutil.virtual_memory()
        available_memory_gb = memory.available / (1024**3)
        
        print(f"📊 System Check:")
        print(f"   CPU Usage: {cpu_percent:.1f}%")
        print(f"   Available Memory: {available_memory_gb:.1f} GB")
        print(f"   Memory Usage: {memory.percent:.1f}%")
        
        # Determine safety level
        if cpu_percent > 85 or memory.percent > 90:
            return "emergency"
        elif cpu_percent > 70 or memory.percent > 80 or available_memory_gb < 1:
            return "safe"
        elif available_memory_gb < 2:
            return "minimal"
        else:
            return "standard"
            
    except Exception as e:
        print(f"⚠️ System check failed: {e}")
        return "emergency"

def show_startup_menu():
    """Show startup mode selection menu"""
    print("\n🚀 STARTUP MODE SELECTION:")
    print("=" * 40)
    print("1. 🚨 Emergency Mode    - Ultra-safe, basic chat only")
    print("2. 🛡️ Safe Mode        - Resource limited, progressive loading")
    print("3. 🔍 Diagnostic Mode   - Identify freeze points")
    print("4. ⚡ Minimal Mode      - Lightweight startup")
    print("5. 🚀 Standard Mode     - Full features (may cause freezes)")
    print("6. ❌ Exit")
    print("=" * 40)

def run_mode(mode):
    """Run the selected mode"""
    scripts = {
        "emergency": "main_emergency.py",
        "safe": "main_safe.py", 
        "diagnostic": "main_diagnostic.py",
        "minimal": "main_ultra_pro.py",
        "standard": "main_ultra_pro.py"
    }
    
    script = scripts.get(mode)
    if not script:
        print(f"❌ Unknown mode: {mode}")
        return False
    
    if not Path(script).exists():
        print(f"❌ Script not found: {script}")
        return False
    
    try:
        print(f"🚀 Starting {mode} mode...")
        print(f"📄 Running: python {script}")
        print("=" * 60)
        
        # Run the script
        result = subprocess.run([sys.executable, script], cwd=Path.cwd())
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Failed to start {mode} mode: {e}")
        return False

def main():
    """Main launcher"""
    try:
        # Check system safety
        recommended_mode = check_system_safety()
        
        print(f"\n💡 RECOMMENDED MODE: {recommended_mode.upper()}")
        
        if recommended_mode == "emergency":
            print("🚨 HIGH SYSTEM LOAD DETECTED")
            print("   Your system is under heavy load. Emergency mode is strongly recommended.")
        elif recommended_mode == "safe":
            print("⚠️ MODERATE SYSTEM LOAD DETECTED") 
            print("   Your system has limited resources. Safe mode is recommended.")
        elif recommended_mode == "minimal":
            print("🪶 LOW MEMORY DETECTED")
            print("   Your system has low memory. Minimal mode is recommended.")
        else:
            print("✅ SYSTEM RESOURCES ADEQUATE")
            print("   Your system should handle standard mode.")
        
        # Auto-start recommended mode or show menu
        print(f"\n⏰ Auto-starting {recommended_mode} mode in 10 seconds...")
        print("Press any key to see manual selection menu...")
        
        # Wait for user input or timeout
        import select
        import tty
        import termios
        
        try:
            # Non-blocking input check (Unix/Linux/Mac)
            old_settings = termios.tcgetattr(sys.stdin)
            tty.setraw(sys.stdin.fileno())
            
            for i in range(10, 0, -1):
                print(f"\rStarting in {i} seconds... (press any key for menu)", end="", flush=True)
                
                if select.select([sys.stdin], [], [], 1) == ([sys.stdin], [], []):
                    sys.stdin.read(1)
                    termios.tcsetattr(sys.stdin, termios.TCSADRAIN, old_settings)
                    break
                    
            else:
                # Timeout - auto-start recommended mode
                termios.tcsetattr(sys.stdin, termios.TCSADRAIN, old_settings)
                print(f"\n🚀 Auto-starting {recommended_mode} mode...")
                return run_mode(recommended_mode)
                
        except:
            # Fallback for Windows or if termios not available
            try:
                import msvcrt
                for i in range(10, 0, -1):
                    print(f"\rStarting in {i} seconds... (press any key for menu)", end="", flush=True)
                    time.sleep(1)
                    if msvcrt.kbhit():
                        msvcrt.getch()
                        break
                else:
                    print(f"\n🚀 Auto-starting {recommended_mode} mode...")
                    return run_mode(recommended_mode)
            except:
                # Final fallback - just show menu
                pass
        
        # Show manual selection menu
        while True:
            show_startup_menu()
            
            try:
                choice = input("\nSelect mode (1-6): ").strip()
                
                if choice == "1":
                    return run_mode("emergency")
                elif choice == "2":
                    return run_mode("safe")
                elif choice == "3":
                    return run_mode("diagnostic")
                elif choice == "4":
                    return run_mode("minimal")
                elif choice == "5":
                    print("⚠️ WARNING: Standard mode may cause system freezes!")
                    confirm = input("Are you sure? (y/N): ").strip().lower()
                    if confirm == 'y':
                        return run_mode("standard")
                elif choice == "6":
                    print("👋 Goodbye!")
                    return True
                else:
                    print("❌ Invalid choice. Please select 1-6.")
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                return True
            except Exception as e:
                print(f"❌ Input error: {e}")
        
    except Exception as e:
        print(f"❌ Launcher error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
