"""
Gideon Core System - Main AI orchestration and management
"""

import threading
import time
import queue
from typing import Dict, Any, Optional, Callable
from datetime import datetime

from src.utils.logger import <PERSON><PERSON><PERSON><PERSON>
from src.utils.config import Config
from src.core.memory_system import MemorySystem
from src.core.ai_engine import AIEngine
from src.core.model_manager import ModelManager
from src.speech.stt_engine import STTEngine
from src.speech.tts_engine import TTSEngine
from src.system.screen_capture import ScreenCapture
from src.system.voice_commands import VoiceCommandProcessor
from src.system.terminal_manager import TerminalManager

# Import ultra-low latency system
try:
    from src.optimization.ultra_low_latency import ultra_low_latency_manager
    ULTRA_LOW_LATENCY_AVAILABLE = True
except ImportError:
    ULTRA_LOW_LATENCY_AVAILABLE = False


class GideonCore:
    """Main Gideon AI Core System"""
    
    def __init__(self):
        self.logger = GideonLogger("GideonCore")
        self.config = Config()
        
        # Core components
        self.memory_system = None
        self.ai_engine = None
        self.model_manager = None
        self.stt_engine = None
        self.tts_engine = None
        self.screen_capture = None
        self.voice_commands = None
        self.terminal_manager = None
        
        # State management
        self.is_initialized = False
        self.is_listening = False
        self.is_speaking = False
        self.is_processing = False
        self.always_listening = False
        self.wake_word = "gideon"
        
        # Threading
        self.processing_queue = queue.Queue()
        self.response_callbacks = {}
        self.worker_thread = None
        self.running = False
        
        # Event callbacks
        self.on_speech_detected = None
        self.on_response_ready = None
        self.on_error = None
        self.on_status_change = None
    
    def initialize_minimal(self):
        """Initialize only essential components for minimal mode"""
        try:
            self.logger.info("Initializing Gideon Core System (Minimal Mode)...")

            # Initialize only memory system (lightweight)
            self.memory_system = MemorySystem()
            self.memory_system.initialize()

            # Initialize AI engine without heavy models
            self.ai_engine = AIEngine(self.memory_system)
            self.ai_engine.initialize_minimal()

            # Skip speech engines, screen capture, and other heavy components
            self.is_initialized = True
            self.logger.info("Gideon Core System initialized (Minimal Mode)")

        except Exception as e:
            self.logger.error(f"Failed to initialize Gideon Core (Minimal): {e}")
            raise

    def initialize_lightweight(self):
        """Initialize core components for lightweight mode"""
        try:
            self.logger.info("Initializing Gideon Core System (Lightweight Mode)...")

            # Initialize memory system
            self.memory_system = MemorySystem()
            self.memory_system.initialize()

            # Initialize model manager
            self.model_manager = ModelManager()

            # Initialize AI engine with lightweight settings
            self.ai_engine = AIEngine(self.memory_system)
            self.ai_engine.initialize_lightweight()

            # Initialize speech engines (but don't start listening)
            self.stt_engine = STTEngine()
            self.tts_engine = TTSEngine()

            # Skip heavy components like screen capture
            self.is_initialized = True
            self.logger.info("Gideon Core System initialized (Lightweight Mode)")

        except Exception as e:
            self.logger.error(f"Failed to initialize Gideon Core (Lightweight): {e}")
            raise

    def initialize(self):
        """Initialize all core components"""
        try:
            self.logger.info("Initializing Gideon Core System...")

            # Initialize memory system
            self.memory_system = MemorySystem()
            self.memory_system.initialize()

            # Initialize model manager
            self.model_manager = ModelManager()

            # Initialize AI engine
            self.ai_engine = AIEngine(self.memory_system)
            self.ai_engine.initialize()

            # Initialize speech engines
            self.stt_engine = STTEngine()
            self.tts_engine = TTSEngine()

            # Test microphone if available
            if self.stt_engine.is_available():
                self.logger.info("🎤 Testing microphone...")
                self.tts_engine.speak("Testing microphone. Please say hello.")
                test_result = self.stt_engine.test_microphone()
                if test_result:
                    self.tts_engine.speak("Microphone test successful!")
                else:
                    self.tts_engine.speak("Microphone test failed. Please check your microphone settings.")
            else:
                self.logger.warning("Speech recognition not available")
            
            # Initialize system components
            self.screen_capture = ScreenCapture()
            self.voice_commands = VoiceCommandProcessor(self)
            self.terminal_manager = TerminalManager()
            
            # Start worker thread
            self.running = True
            self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()
            
            self.is_initialized = True
            self.logger.info("Gideon Core System initialized successfully")

            # Start always listening mode
            self.start_always_listening()

            # Send welcome message
            self._notify_status_change("ready")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Gideon Core: {e}")
            raise
    
    def shutdown(self):
        """Shutdown Gideon Core System"""
        self.logger.info("Shutting down Gideon Core System...")
        
        self.running = False
        self.is_listening = False
        
        # Stop components
        if self.stt_engine:
            self.stt_engine.stop_listening()

        if self.tts_engine:
            self.tts_engine.stop()

        if self.terminal_manager:
            self.terminal_manager.shutdown()

        # Wait for worker thread
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=2)

        self.logger.info("Gideon Core System shutdown complete")
    
    def process_text_input(self, text: str, callback: Callable = None) -> str:
        """Process text input and return response"""
        if not self.is_initialized:
            return "Gideon is not initialized"
        
        request_id = f"text_{int(time.time() * 1000)}"
        
        if callback:
            self.response_callbacks[request_id] = callback
        
        # Add to processing queue
        self.processing_queue.put({
            "type": "text",
            "id": request_id,
            "content": text,
            "timestamp": datetime.now()
        })
        
        return request_id
    
    def process_voice_input(self, callback: Callable = None):
        """Start voice input processing"""
        if not self.is_initialized or self.is_listening:
            return False
        
        try:
            self.is_listening = True
            self._notify_status_change("listening")
            
            # Start listening in a separate thread
            def listen_thread():
                try:
                    audio_text = self.stt_engine.listen_once()
                    if audio_text:
                        self.process_text_input(audio_text, callback)
                    else:
                        self.logger.warning("No speech detected")
                        if callback:
                            callback("", "No speech detected")
                except Exception as e:
                    self.logger.error(f"Voice input error: {e}")
                    if callback:
                        callback("", f"Voice input error: {e}")
                finally:
                    self.is_listening = False
                    self._notify_status_change("ready")
            
            threading.Thread(target=listen_thread, daemon=True).start()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start voice input: {e}")
            self.is_listening = False
            return False
    
    def speak_text(self, text: str, callback: Callable = None):
        """Speak text using TTS"""
        if not self.tts_engine:
            return False
        
        def speak_thread():
            try:
                self.is_speaking = True
                self._notify_status_change("speaking")
                
                self.tts_engine.speak(text)
                
                if callback:
                    callback(True)
                    
            except Exception as e:
                self.logger.error(f"TTS error: {e}")
                if callback:
                    callback(False)
            finally:
                self.is_speaking = False
                self._notify_status_change("ready")
        
        threading.Thread(target=speak_thread, daemon=True).start()
        return True
    
    def take_screenshot(self) -> Optional[str]:
        """Take a screenshot and return file path"""
        if self.screen_capture:
            return self.screen_capture.capture()
        return None

    def execute_terminal_command(self, command: str, session_id: str = None) -> Dict[str, Any]:
        """Execute a terminal command"""
        if self.terminal_manager:
            return self.terminal_manager.execute_command(command, session_id)
        else:
            return {
                'success': False,
                'error': 'Terminal manager not available',
                'output': '',
                'command': command
            }

    def get_terminal_status(self) -> Dict[str, Any]:
        """Get terminal manager status"""
        if self.terminal_manager:
            return self.terminal_manager.get_status()
        else:
            return {
                'available': False,
                'error': 'Terminal manager not initialized'
            }

    def start_always_listening(self):
        """Start always listening mode with wake word detection"""
        if not self.stt_engine or not self.stt_engine.is_available():
            self.logger.warning("Speech recognition not available for always listening")
            return False

        if self.always_listening:
            self.logger.info("Always listening already active")
            return True

        try:
            self.always_listening = True
            self.wake_word = self.config.get("speech.wake_word", "gideon")

            # Start continuous listening with wake word detection
            success = self.stt_engine.start_continuous_listening(
                callback=self._on_wake_word_detected,
                wake_word=self.wake_word
            )

            if success:
                self.logger.info(f"Always listening started with wake word: '{self.wake_word}'")
                self._notify_status_change("always_listening")
                return True
            else:
                self.always_listening = False
                self.logger.error("Failed to start always listening")
                return False

        except Exception as e:
            self.logger.error(f"Error starting always listening: {e}")
            self.always_listening = False
            return False

    def stop_always_listening(self):
        """Stop always listening mode"""
        if not self.always_listening:
            return

        try:
            self.always_listening = False
            if self.stt_engine:
                self.stt_engine.stop_listening()

            self.logger.info("Always listening stopped")
            self._notify_status_change("ready")

        except Exception as e:
            self.logger.error(f"Error stopping always listening: {e}")

    def _on_wake_word_detected(self, command_text: str):
        """Enhanced wake word detection handler with bilingual support"""
        try:
            self.logger.info(f"🎯 WAKE WORD DETECTED! Processing command: '{command_text}'")

            # Detect language of the command for appropriate response
            detected_language = self._detect_command_language(command_text)

            # Immediate acknowledgment in the detected language
            if detected_language == "ar":
                acknowledgment = "نعم؟"  # "Yes?" in Arabic
            else:
                acknowledgment = "Yes?"

            self.speak_text(acknowledgment)

            # Notify that Gideon is now active
            if self.on_speech_detected:
                self.on_speech_detected()

            # Update status
            self._notify_status_change("wake_word_detected")

            # Process the command
            if command_text.strip():
                self.logger.info(f"📝 Processing command: '{command_text}'")
                self.process_text_input(command_text, self._on_wake_word_response)
            else:
                # No command provided, just acknowledge and wait
                self.logger.info("👂 Wake word only, waiting for command...")
                response = "I'm listening. How can I help you?"
                self.speak_text(response)
                if self.on_response_ready:
                    self.on_response_ready("", response)

        except Exception as e:
            self.logger.error(f"❌ Error processing wake word command: {e}")

    def _on_wake_word_response(self, user_input: str, ai_response: str):
        """Handle response to wake word command"""
        try:
            self.logger.info(f"🗣️ Responding to wake word command: '{user_input}' -> '{ai_response}'")

            # Speak the response
            self.speak_text(ai_response)

            # Notify response ready
            if self.on_response_ready:
                self.on_response_ready(user_input, ai_response)

        except Exception as e:
            self.logger.error(f"❌ Error handling wake word response: {e}")

    def get_model_manager(self):
        """Get the model manager instance"""
        return self.model_manager

    def add_model_from_file(self, file_path: str, model_name: str = None,
                           model_type: str = None, description: str = "") -> Optional[str]:
        """Add a model from file using drag and drop"""
        if self.model_manager:
            return self.model_manager.add_model_from_file(file_path, model_name, model_type, description)
        return None

    def list_available_models(self):
        """List all available models"""
        if self.model_manager:
            return self.model_manager.list_models()
        return []

    def activate_model(self, model_id: str) -> bool:
        """Activate a specific model"""
        if self.model_manager:
            return self.model_manager.activate_model(model_id)
        return False

    def get_active_models(self):
        """Get currently active models"""
        if self.model_manager:
            return self.model_manager.get_active_models()
        return []

    def start_always_listening(self):
        """Start always listening mode with wake word detection"""
        if not self.stt_engine or not self.stt_engine.is_available():
            self.logger.warning("Speech recognition not available for always listening")
            return False

        if self.always_listening:
            self.logger.info("Always listening already active")
            return True

        try:
            self.always_listening = True
            self.wake_word = self.config.get("speech.wake_word", "gideon")

            # Start continuous listening with wake word detection
            success = self.stt_engine.start_continuous_listening(
                callback=self._on_wake_word_detected,
                wake_word=self.wake_word
            )

            if success:
                self.logger.info(f"Always listening started with wake word: '{self.wake_word}'")
                self._notify_status_change("always_listening")
                return True
            else:
                self.always_listening = False
                self.logger.error("Failed to start always listening")
                return False

        except Exception as e:
            self.logger.error(f"Error starting always listening: {e}")
            self.always_listening = False
            return False

    def stop_always_listening(self):
        """Stop always listening mode"""
        if not self.always_listening:
            return

        try:
            self.always_listening = False
            if self.stt_engine:
                self.stt_engine.stop_listening()

            self.logger.info("Always listening stopped")
            self._notify_status_change("ready")

        except Exception as e:
            self.logger.error(f"Error stopping always listening: {e}")

    def _on_wake_word_detected(self, command_text: str):
        """Enhanced wake word detection handler with bilingual support"""
        try:
            self.logger.info(f"🎯 WAKE WORD DETECTED! Processing command: '{command_text}'")

            # Detect language of the command for appropriate response
            detected_language = self._detect_command_language(command_text)
            self.logger.info(f"🌍 Detected language: {detected_language}")

            # Set the response language for AI engine
            self._set_response_language(detected_language)

            # Notify that Gideon is now active
            if self.on_speech_detected:
                self.on_speech_detected()

            # Process the command
            if command_text.strip():
                self.process_text_input(command_text, self._on_wake_word_response)
            else:
                # No command provided, acknowledge in detected language
                if detected_language == "ar":
                    response = "نعم؟ كيف يمكنني مساعدتك؟"  # "Yes? How can I help you?" in Arabic
                else:
                    response = "Yes? How can I help you?"

                self.speak_text(response)
                if self.on_response_ready:
                    self.on_response_ready("", response)

        except Exception as e:
            self.logger.error(f"❌ Error processing wake word command: {e}")

    def _detect_command_language(self, text: str) -> str:
        """Detect the language of a command"""
        if not text:
            return "en"

        # Count Arabic characters
        arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
        total_chars = len([char for char in text if char.isalpha()])

        if total_chars == 0:
            return "en"

        arabic_ratio = arabic_chars / total_chars

        # If more than 20% Arabic characters, consider it Arabic
        return "ar" if arabic_ratio > 0.2 else "en"

    def _set_response_language(self, language: str):
        """Set the preferred response language"""
        if hasattr(self, 'ai_engine') and self.ai_engine:
            # Store the detected language for AI response generation
            if hasattr(self.ai_engine, 'set_response_language'):
                self.ai_engine.set_response_language(language)
                self.logger.info(f"🌍 Set AI engine response language to: {language}")

        # Update the config temporarily for this conversation
        self.config.set("app.detected_language", language)
        self.logger.info(f"🔧 Updated config detected language to: {language}")

    def _on_wake_word_response(self, user_input: str, ai_response: str):
        """Handle response to wake word command"""
        try:
            # Speak the response
            self.speak_text(ai_response)

            # Notify response ready
            if self.on_response_ready:
                self.on_response_ready(user_input, ai_response)

        except Exception as e:
            self.logger.error(f"Error handling wake word response: {e}")
    
    def _worker_loop(self):
        """Main worker thread loop"""
        while self.running:
            try:
                # Get request from queue (with timeout)
                request = self.processing_queue.get(timeout=1)
                
                if request["type"] == "text":
                    self._process_text_request(request)
                
                self.processing_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"Worker loop error: {e}")
    
    def _process_text_request(self, request: Dict[str, Any]):
        """Process text request"""
        try:
            self.is_processing = True
            self._notify_status_change("processing")
            
            text = request["content"]
            request_id = request["id"]

            self.logger.info(f"Processing text: {text}")

            # Detect language and set response language for AI
            detected_language = self._detect_command_language(text)

            # Set response language in AI engine directly (bypass config override)
            if hasattr(self, 'ai_engine') and self.ai_engine:
                self.ai_engine.set_response_language(detected_language)
                self.logger.info(f"🌍 Set AI engine response language directly to: {detected_language}")

            self.logger.info(f"🌍 Detected input language: {detected_language}")

            # Check for voice commands first
            if self.voice_commands.is_command(text):
                response = self.voice_commands.execute_command(text)
                # If voice command returns None, it means it should go to AI
                if response is None:
                    # Use ultra-low latency processing if available
                    if ULTRA_LOW_LATENCY_AVAILABLE:
                        response = ultra_low_latency_manager.process_ultra_fast(text, self.ai_engine)
                    else:
                        response = self.ai_engine.generate_response(text)
            else:
                # Use ultra-low latency processing if available
                if ULTRA_LOW_LATENCY_AVAILABLE:
                    response = ultra_low_latency_manager.process_ultra_fast(text, self.ai_engine)
                else:
                    response = self.ai_engine.generate_response(text)
            
            # Store in memory
            self.memory_system.add_conversation(text, response)
            
            # Log conversation
            self.logger.log_conversation(text, response)
            
            # Call callback if exists
            if request_id in self.response_callbacks:
                callback = self.response_callbacks.pop(request_id)
                callback(text, response)

            # Notify response ready only if no specific callback was used
            # This prevents duplicate responses when both callback and global handler are set
            elif self.on_response_ready:
                self.on_response_ready(text, response)
            
        except Exception as e:
            self.logger.error(f"Error processing text request: {e}")
            error_response = "I encountered an error while processing your request."
            
            if request_id in self.response_callbacks:
                callback = self.response_callbacks.pop(request_id)
                callback(text, error_response)
            
            if self.on_error:
                self.on_error(str(e))
        
        finally:
            self.is_processing = False
            self._notify_status_change("ready")
    
    def _notify_status_change(self, status: str):
        """Notify status change"""
        if self.on_status_change:
            self.on_status_change(status)
    
    # Event callback setters
    def set_speech_detected_callback(self, callback: Callable):
        self.on_speech_detected = callback
    
    def set_response_ready_callback(self, callback: Callable):
        self.on_response_ready = callback
    
    def set_error_callback(self, callback: Callable):
        self.on_error = callback
    
    def set_status_change_callback(self, callback: Callable):
        self.on_status_change = callback
    
    # Status getters
    def get_status(self) -> Dict[str, Any]:
        """Get current system status"""
        return {
            "initialized": self.is_initialized,
            "listening": self.is_listening,
            "speaking": self.is_speaking,
            "processing": self.is_processing,
            "queue_size": self.processing_queue.qsize()
        }
