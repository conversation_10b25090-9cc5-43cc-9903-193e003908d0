2025-06-06 23:52:15,257 - <PERSON><PERSON><PERSON> - INFO - Initializing Gideon Core System...
2025-06-06 23:52:15,262 - MemorySystem - INFO - Loaded 8 recent conversations
2025-06-06 23:52:15,262 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-06 23:52:15,262 - MemorySystem - INFO - Memory system initialized successfully
2025-06-06 23:52:15,264 - Model<PERSON>anager - INFO - No existing models config found
2025-06-06 23:52:15,264 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-06 23:52:15,265 - AIEngine - INFO - Loaded 4 learned response patterns
2025-06-06 23:52:15,265 - AIEngine - INFO - Initializing LLM backends...
2025-06-06 23:52:15,265 - AIEngine - INFO - ✅ Ollama backend available
2025-06-06 23:52:15,266 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-06 23:52:15,266 - <PERSON><PERSON><PERSON>ine - INFO - ⚠️ Transformers backend not available
2025-06-06 23:52:15,266 - <PERSON><PERSON><PERSON>ine - INFO - Initialized 1 LLM backends
2025-06-06 23:52:15,266 - AIE<PERSON>ine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-06 23:52:15,510 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-06 23:52:15,511 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-06 23:52:15,511 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-06 23:52:15,512 - AIEngine - INFO - AI Engine initialized successfully
2025-06-06 23:52:15,637 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-06 23:52:15,752 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-06 23:52:15,752 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-06 23:52:17,748 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.797409123686801
2025-06-06 23:52:17,760 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-06 23:52:17,968 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-06 23:52:17,968 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-06 23:52:17,968 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-06 23:52:21,219 - STTEngine - INFO - Testing microphone... Say something!
2025-06-06 23:52:24,230 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-06 23:52:28,416 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-06 23:52:28,416 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-06 23:52:28,416 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-06 23:52:31,977 - STTEngine - INFO - 🎤 Heard: 'اذا دشيت ارض بالكلام'
2025-06-06 23:54:30,790 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-06 23:54:31,173 - STTEngine - INFO - Stopped continuous listening
2025-06-06 23:54:31,455 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-06 23:56:01,814 - EnterpriseMain - INFO - Starting Gideon AI Assistant - Enterprise Edition
2025-06-06 23:56:01,815 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-06 23:56:01,820 - MemorySystem - INFO - Loaded 8 recent conversations
2025-06-06 23:56:01,820 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-06 23:56:01,820 - MemorySystem - INFO - Memory system initialized successfully
2025-06-06 23:56:01,822 - ModelManager - INFO - No existing models config found
2025-06-06 23:56:01,823 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-06 23:56:01,823 - AIEngine - INFO - Loaded 4 learned response patterns
2025-06-06 23:56:01,823 - AIEngine - INFO - Initializing LLM backends...
2025-06-06 23:56:01,824 - AIEngine - INFO - ✅ Ollama backend available
2025-06-06 23:56:01,824 - AIEngine - INFO - ⚠️ llama.cpp backend not available
2025-06-06 23:56:01,824 - AIEngine - INFO - ⚠️ Transformers backend not available
2025-06-06 23:56:01,824 - AIEngine - INFO - Initialized 1 LLM backends
2025-06-06 23:56:01,824 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-06 23:56:02,074 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-06 23:56:02,076 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-06 23:56:02,076 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-06 23:56:02,076 - AIEngine - INFO - AI Engine initialized successfully
2025-06-06 23:56:02,196 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ul', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-06 23:56:02,317 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-06 23:56:02,317 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-06 23:56:04,313 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 6.789606816508446
2025-06-06 23:56:04,326 - STTEngine - INFO - Speech recognition initialized successfully
2025-06-06 23:56:04,527 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-06 23:56:04,528 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-06 23:56:04,528 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-06 23:56:07,773 - STTEngine - INFO - Testing microphone... Say something!
2025-06-06 23:56:09,612 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-06 23:56:13,798 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-06 23:56:13,799 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-06 23:56:13,799 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-06 23:56:20,622 - GideonCore - INFO - Processing text: hi
2025-06-06 23:56:20,627 - GideonCore - INFO - CONVERSATION - User: hi
2025-06-06 23:56:20,627 - GideonCore - INFO - CONVERSATION - AI: Unknown command: hi
2025-06-06 23:56:20,705 - GideonCore - ERROR - Error processing text request: UltraProfessionalInterface._on_response_ready() takes 2 positional arguments but 3 were given
2025-06-06 23:56:33,757 - GideonCore - INFO - Shutting down Gideon Core System...
2025-06-06 23:56:33,838 - GideonCore - INFO - Gideon Core System shutdown complete
2025-06-06 23:56:33,914 - EnterpriseMain - INFO - Gideon AI Assistant Enterprise Edition shutting down
