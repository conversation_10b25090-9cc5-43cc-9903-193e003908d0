"""
Terminal Manager for Gideon AI Assistant
Provides safe terminal/command prompt access with security controls
"""

import os
import subprocess
import threading
import time
import queue
import platform
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Callable, Tuple, Any
import shlex
import re

from src.utils.logger import <PERSON><PERSON>ogger
from src.utils.config import Config
from src.utils.i18n import get_i18n


class CommandSecurityValidator:
    """Security validator for terminal commands"""
    
    def __init__(self):
        self.logger = GideonLogger("CommandSecurity")
        self.config = Config()
        
        # Dangerous commands that require confirmation
        self.dangerous_commands = {
            'windows': [
                'del', 'rmdir', 'rd', 'format', 'fdisk', 'diskpart',
                'shutdown', 'restart', 'reboot', 'taskkill', 'net user',
                'reg delete', 'reg add', 'bcdedit', 'sfc', 'chkdsk',
                'powercfg', 'netsh', 'sc delete', 'wmic', 'cipher'
            ],
            'powershell': [
                'Remove-Item', 'Remove-Computer', 'Remove-ADUser', 'Clear-Host',
                'Stop-Computer', 'Restart-Computer', 'Stop-Process', 'Remove-Module',
                'Uninstall-Module', 'Set-ExecutionPolicy', 'Invoke-Expression',
                'Invoke-Command', 'Enter-PSSession', 'New-PSSession'
            ],
            'general': [
                'rm', 'rmdir', 'delete', 'kill', 'pkill', 'sudo', 'su',
                'chmod 777', 'chown', 'mount', 'umount', 'fdisk'
            ]
        }
        
        # Blocked commands (never allow)
        self.blocked_commands = [
            'format c:', 'del c:\\*', 'rm -rf /', 'dd if=', 'mkfs',
            ':(){ :|:& };:', 'sudo rm -rf', 'deltree', 'rd /s /q c:\\'
        ]
        
        # Safe commands (no confirmation needed)
        self.safe_commands = [
            'dir', 'ls', 'pwd', 'cd', 'echo', 'type', 'cat', 'more',
            'find', 'grep', 'ping', 'ipconfig', 'ifconfig', 'whoami',
            'date', 'time', 'ver', 'systeminfo', 'tasklist', 'ps',
            'help', 'man', 'which', 'where', 'tree', 'cls', 'clear'
        ]
    
    def validate_command(self, command: str) -> Tuple[str, bool, str]:
        """
        Validate command for security
        
        Returns:
            Tuple of (risk_level, requires_confirmation, reason)
            risk_level: 'safe', 'warning', 'dangerous', 'blocked'
        """
        command_lower = command.lower().strip()
        
        # Check for blocked commands
        for blocked in self.blocked_commands:
            if blocked in command_lower:
                return ('blocked', False, f"Command contains blocked pattern: {blocked}")
        
        # Check for safe commands
        first_word = command_lower.split()[0] if command_lower.split() else ""
        if first_word in self.safe_commands:
            return ('safe', False, "Command is considered safe")
        
        # Check for dangerous commands
        system_type = 'windows' if platform.system().lower() == 'windows' else 'general'
        dangerous_list = self.dangerous_commands.get(system_type, []) + self.dangerous_commands.get('general', [])
        
        for dangerous in dangerous_list:
            if dangerous in command_lower:
                return ('dangerous', True, f"Command contains potentially dangerous operation: {dangerous}")
        
        # Check for PowerShell specific commands
        if any(ps_cmd in command_lower for ps_cmd in self.dangerous_commands.get('powershell', [])):
            return ('dangerous', True, "PowerShell command requires confirmation")
        
        # Default to warning for unknown commands
        return ('warning', True, "Unknown command - please confirm execution")


class TerminalSession:
    """Represents a terminal session"""
    
    def __init__(self, session_id: str, shell_type: str = "cmd"):
        self.session_id = session_id
        self.shell_type = shell_type
        self.process = None
        self.output_queue = queue.Queue()
        self.error_queue = queue.Queue()
        self.is_active = False
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.working_directory = os.getcwd()
        
    def start(self):
        """Start the terminal session"""
        try:
            if platform.system().lower() == 'windows':
                if self.shell_type.lower() == 'powershell':
                    cmd = ['powershell.exe', '-NoExit', '-Command', '-']
                else:
                    cmd = ['cmd.exe', '/k']
            else:
                cmd = ['/bin/bash']
            
            self.process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.working_directory,
                bufsize=0
            )
            self.is_active = True
            return True
            
        except Exception as e:
            return False
    
    def execute_command(self, command: str) -> bool:
        """Execute command in this session"""
        if not self.is_active or not self.process:
            return False
        
        try:
            self.process.stdin.write(command + '\n')
            self.process.stdin.flush()
            self.last_activity = datetime.now()
            return True
        except Exception:
            return False
    
    def terminate(self):
        """Terminate the session"""
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except:
                try:
                    self.process.kill()
                except:
                    pass
        self.is_active = False


class TerminalManager:
    """Terminal Manager for Gideon AI Assistant"""
    
    def __init__(self):
        self.logger = GideonLogger("TerminalManager")
        self.config = Config()
        self.i18n = get_i18n()
        self.security_validator = CommandSecurityValidator()
        
        # Terminal sessions
        self.sessions = {}
        self.active_session_id = None
        self.session_counter = 0
        
        # Callbacks
        self.on_output_received = None
        self.on_command_executed = None
        self.on_error_occurred = None
        self.on_confirmation_required = None
        
        # Settings
        self.max_sessions = 5
        self.session_timeout = 3600  # 1 hour
        self.max_output_length = 10000
        
        # Command history
        self.command_history = []
        self.max_history = 100
        
        # Initialize default session
        self._create_default_session()
    
    def _create_default_session(self):
        """Create default terminal session"""
        try:
            session_id = f"session_{self.session_counter}"
            self.session_counter += 1

            # Determine default shell
            shell_type = "powershell" if platform.system().lower() == 'windows' else "bash"

            session = TerminalSession(session_id, shell_type)
            if session.start():
                self.sessions[session_id] = session
                self.active_session_id = session_id
                self.logger.info(f"Created default terminal session: {session_id}")
                return session_id
            else:
                self.logger.error("Failed to create default terminal session")
                return None

        except Exception as e:
            self.logger.error(f"Error creating default session: {e}")
            return None

    def execute_command(self, command: str, session_id: str = None, require_confirmation: bool = True) -> Dict[str, Any]:
        """
        Execute a terminal command with security validation

        Args:
            command: Command to execute
            session_id: Optional session ID (uses active session if None)
            require_confirmation: Whether to require confirmation for dangerous commands

        Returns:
            Dict with execution results
        """
        try:
            # Use active session if none specified
            if session_id is None:
                session_id = self.active_session_id

            if session_id not in self.sessions:
                return {
                    'success': False,
                    'error': 'Invalid session ID',
                    'output': '',
                    'command': command
                }

            session = self.sessions[session_id]

            # Validate command security
            risk_level, needs_confirmation, reason = self.security_validator.validate_command(command)

            # Handle blocked commands
            if risk_level == 'blocked':
                self.logger.warning(f"Blocked dangerous command: {command}")
                return {
                    'success': False,
                    'error': f'Command blocked for security: {reason}',
                    'output': '',
                    'command': command,
                    'risk_level': risk_level
                }

            # Handle commands requiring confirmation
            if needs_confirmation and require_confirmation:
                if self.on_confirmation_required:
                    confirmed = self.on_confirmation_required(command, risk_level, reason)
                    if not confirmed:
                        return {
                            'success': False,
                            'error': 'Command execution cancelled by user',
                            'output': '',
                            'command': command,
                            'risk_level': risk_level
                        }
                else:
                    # No confirmation handler available
                    return {
                        'success': False,
                        'error': f'Command requires confirmation: {reason}',
                        'output': '',
                        'command': command,
                        'risk_level': risk_level,
                        'requires_confirmation': True
                    }

            # Execute the command
            result = self._execute_command_in_session(session, command)

            # Add to history
            self._add_to_history(command, result)

            # Notify callbacks
            if self.on_command_executed:
                self.on_command_executed(command, result)

            return result

        except Exception as e:
            self.logger.error(f"Error executing command '{command}': {e}")
            return {
                'success': False,
                'error': str(e),
                'output': '',
                'command': command
            }

    def _execute_command_in_session(self, session: TerminalSession, command: str) -> Dict[str, Any]:
        """Execute command in specific session"""
        try:
            # Execute command
            if not session.execute_command(command):
                return {
                    'success': False,
                    'error': 'Failed to send command to session',
                    'output': '',
                    'command': command
                }

            # Wait for output with timeout
            output_lines = []
            error_lines = []
            start_time = time.time()
            timeout = 30  # 30 seconds timeout

            while time.time() - start_time < timeout:
                try:
                    # Simple approach: execute command and capture output
                    result = subprocess.run(
                        command,
                        shell=True,
                        capture_output=True,
                        text=True,
                        timeout=timeout,
                        cwd=session.working_directory
                    )

                    output = result.stdout
                    error = result.stderr
                    return_code = result.returncode

                    # Limit output length
                    if len(output) > self.max_output_length:
                        output = output[:self.max_output_length] + "\n... (output truncated)"

                    if len(error) > self.max_output_length:
                        error = error[:self.max_output_length] + "\n... (error truncated)"

                    return {
                        'success': return_code == 0,
                        'output': output,
                        'error': error,
                        'return_code': return_code,
                        'command': command,
                        'session_id': session.session_id
                    }

                except subprocess.TimeoutExpired:
                    return {
                        'success': False,
                        'error': 'Command execution timed out',
                        'output': '',
                        'command': command
                    }
                except Exception as e:
                    return {
                        'success': False,
                        'error': f'Command execution failed: {str(e)}',
                        'output': '',
                        'command': command
                    }

            return {
                'success': False,
                'error': 'Command execution timed out',
                'output': '',
                'command': command
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'output': '',
                'command': command
            }

    def _add_to_history(self, command: str, result: Dict[str, Any]):
        """Add command to history"""
        history_entry = {
            'command': command,
            'timestamp': datetime.now(),
            'success': result.get('success', False),
            'output_length': len(result.get('output', '')),
            'session_id': result.get('session_id', self.active_session_id)
        }

        self.command_history.append(history_entry)

        # Limit history size
        if len(self.command_history) > self.max_history:
            self.command_history = self.command_history[-self.max_history:]

    def get_command_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent command history"""
        return self.command_history[-limit:] if limit else self.command_history

    def create_session(self, shell_type: str = None) -> Optional[str]:
        """Create a new terminal session"""
        try:
            if len(self.sessions) >= self.max_sessions:
                # Clean up old sessions
                self._cleanup_old_sessions()

                if len(self.sessions) >= self.max_sessions:
                    self.logger.warning("Maximum number of sessions reached")
                    return None

            session_id = f"session_{self.session_counter}"
            self.session_counter += 1

            if shell_type is None:
                shell_type = "powershell" if platform.system().lower() == 'windows' else "bash"

            session = TerminalSession(session_id, shell_type)
            if session.start():
                self.sessions[session_id] = session
                self.logger.info(f"Created new terminal session: {session_id}")
                return session_id
            else:
                self.logger.error(f"Failed to create session: {session_id}")
                return None

        except Exception as e:
            self.logger.error(f"Error creating session: {e}")
            return None

    def switch_session(self, session_id: str) -> bool:
        """Switch to a different session"""
        if session_id in self.sessions and self.sessions[session_id].is_active:
            self.active_session_id = session_id
            self.logger.info(f"Switched to session: {session_id}")
            return True
        return False

    def close_session(self, session_id: str) -> bool:
        """Close a terminal session"""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            session.terminate()
            del self.sessions[session_id]

            # Switch to another session if this was active
            if self.active_session_id == session_id:
                if self.sessions:
                    self.active_session_id = next(iter(self.sessions.keys()))
                else:
                    self.active_session_id = None
                    # Create new default session
                    self._create_default_session()

            self.logger.info(f"Closed session: {session_id}")
            return True
        return False

    def get_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all sessions"""
        session_info = {}
        for session_id, session in self.sessions.items():
            session_info[session_id] = {
                'id': session_id,
                'shell_type': session.shell_type,
                'is_active': session.is_active,
                'created_at': session.created_at,
                'last_activity': session.last_activity,
                'working_directory': session.working_directory,
                'is_current': session_id == self.active_session_id
            }
        return session_info

    def _cleanup_old_sessions(self):
        """Clean up old inactive sessions"""
        current_time = datetime.now()
        sessions_to_remove = []

        for session_id, session in self.sessions.items():
            # Remove sessions that are inactive or too old
            time_since_activity = (current_time - session.last_activity).total_seconds()
            if not session.is_active or time_since_activity > self.session_timeout:
                sessions_to_remove.append(session_id)

        for session_id in sessions_to_remove:
            if session_id != self.active_session_id:  # Don't remove active session
                self.close_session(session_id)

    def get_working_directory(self, session_id: str = None) -> str:
        """Get current working directory for session"""
        if session_id is None:
            session_id = self.active_session_id

        if session_id in self.sessions:
            return self.sessions[session_id].working_directory
        return os.getcwd()

    def change_directory(self, path: str, session_id: str = None) -> Dict[str, Any]:
        """Change working directory for session"""
        if session_id is None:
            session_id = self.active_session_id

        if session_id not in self.sessions:
            return {
                'success': False,
                'error': 'Invalid session ID',
                'path': path
            }

        try:
            # Validate path
            if not os.path.exists(path):
                return {
                    'success': False,
                    'error': f'Path does not exist: {path}',
                    'path': path
                }

            if not os.path.isdir(path):
                return {
                    'success': False,
                    'error': f'Path is not a directory: {path}',
                    'path': path
                }

            # Update session working directory
            session = self.sessions[session_id]
            old_path = session.working_directory
            session.working_directory = os.path.abspath(path)

            # Execute cd command in session
            cd_command = f"cd /d \"{path}\"" if platform.system().lower() == 'windows' else f"cd \"{path}\""
            result = self.execute_command(cd_command, session_id, require_confirmation=False)

            return {
                'success': True,
                'old_path': old_path,
                'new_path': session.working_directory,
                'path': path
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'path': path
            }

    def is_available(self) -> bool:
        """Check if terminal functionality is available"""
        return len(self.sessions) > 0 and self.active_session_id is not None

    def get_status(self) -> Dict[str, Any]:
        """Get terminal manager status"""
        return {
            'available': self.is_available(),
            'active_session': self.active_session_id,
            'total_sessions': len(self.sessions),
            'command_history_count': len(self.command_history),
            'platform': platform.system(),
            'max_sessions': self.max_sessions
        }

    def shutdown(self):
        """Shutdown terminal manager and close all sessions"""
        self.logger.info("Shutting down terminal manager...")

        for session_id in list(self.sessions.keys()):
            self.close_session(session_id)

        self.sessions.clear()
        self.active_session_id = None
        self.logger.info("Terminal manager shutdown complete")
