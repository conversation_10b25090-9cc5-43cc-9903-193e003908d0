#!/usr/bin/env python3
"""
Voice Chat Interface - ChatGPT-Style Real-time Voice Conversation
Professional voice interaction with visual feedback
"""

import tkinter as tk
import customtkinter as ctk
import threading
import time
import queue
import numpy as np
from typing import Dict, Any, Optional, Callable, List
import pyaudio
import wave

class VoiceWaveform:
    """Real-time audio waveform visualization"""
    
    def __init__(self, parent_frame, width: int = 400, height: int = 100):
        self.parent = parent_frame
        self.width = width
        self.height = height
        
        # Audio parameters
        self.chunk_size = 1024
        self.sample_rate = 44100
        self.channels = 1
        
        # Waveform data
        self.audio_data = queue.Queue()
        self.waveform_data = [0] * 100
        self.is_recording = False
        
        # Create waveform canvas
        self._create_waveform_canvas()
        
        # Start visualization
        self.visualization_running = True
        self._start_visualization()
    
    def _create_waveform_canvas(self):
        """Create waveform visualization canvas"""
        self.canvas = tk.Canvas(
            self.parent,
            width=self.width,
            height=self.height,
            bg='#0A0E13',
            highlightthickness=0
        )
        self.canvas.pack(pady=10)
    
    def _start_visualization(self):
        """Start waveform visualization"""
        self._update_waveform()
    
    def _update_waveform(self):
        """Update waveform display"""
        if not self.visualization_running:
            return
        
        try:
            # Clear canvas
            self.canvas.delete("all")
            
            # Draw waveform
            if self.is_recording:
                self._draw_active_waveform()
            else:
                self._draw_idle_waveform()
            
            # Schedule next update
            self.canvas.after(50, self._update_waveform)
            
        except Exception as e:
            print(f"Waveform update error: {e}")
    
    def _draw_active_waveform(self):
        """Draw active recording waveform"""
        center_y = self.height // 2
        bar_width = self.width // len(self.waveform_data)
        
        for i, amplitude in enumerate(self.waveform_data):
            x = i * bar_width
            bar_height = abs(amplitude) * (self.height // 2)
            
            # Color based on amplitude
            if bar_height > self.height * 0.3:
                color = '#FF6B35'  # High amplitude - orange
            elif bar_height > self.height * 0.1:
                color = '#00D4FF'  # Medium amplitude - cyan
            else:
                color = '#334155'  # Low amplitude - gray
            
            # Draw bar
            self.canvas.create_rectangle(
                x, center_y - bar_height,
                x + bar_width - 1, center_y + bar_height,
                fill=color,
                outline=""
            )
    
    def _draw_idle_waveform(self):
        """Draw idle state waveform"""
        center_y = self.height // 2
        
        # Draw baseline
        self.canvas.create_line(
            0, center_y, self.width, center_y,
            fill='#334155',
            width=2
        )
        
        # Draw subtle pulse
        pulse_time = time.time() * 2
        for i in range(0, self.width, 20):
            pulse_height = np.sin(pulse_time + i * 0.1) * 5
            
            self.canvas.create_oval(
                i - 2, center_y - pulse_height - 2,
                i + 2, center_y - pulse_height + 2,
                fill='#00D4FF',
                outline=""
            )
    
    def start_recording(self):
        """Start recording and waveform display"""
        self.is_recording = True
    
    def stop_recording(self):
        """Stop recording and waveform display"""
        self.is_recording = False
        self.waveform_data = [0] * 100
    
    def update_audio_data(self, audio_chunk):
        """Update waveform with new audio data"""
        if len(audio_chunk) > 0:
            # Convert to amplitude values
            amplitude = np.frombuffer(audio_chunk, dtype=np.int16)
            # Downsample for visualization
            step = len(amplitude) // 100
            if step > 0:
                downsampled = amplitude[::step][:100]
                # Normalize
                if len(downsampled) > 0:
                    max_val = np.max(np.abs(downsampled))
                    if max_val > 0:
                        self.waveform_data = (downsampled / max_val).tolist()
    
    def destroy(self):
        """Clean up waveform resources"""
        self.visualization_running = False


class VoiceChatInterface:
    """ChatGPT-style voice chat interface"""
    
    def __init__(self, parent_frame, gideon_core=None):
        self.parent = parent_frame
        self.gideon_core = gideon_core
        
        # Voice chat state
        self.is_push_to_talk = False
        self.is_continuous_mode = True
        self.is_voice_active = False
        self.conversation_active = False
        
        # Audio components
        self.audio_recorder = None
        self.waveform = None
        
        # Callbacks
        self.on_voice_start = None
        self.on_voice_end = None
        self.on_response_ready = None
        
        # Create interface
        self._create_voice_interface()
    
    def _create_voice_interface(self):
        """Create voice chat interface"""
        # Main container
        self.voice_container = ctk.CTkFrame(
            self.parent,
            fg_color="transparent"
        )
        self.voice_container.pack(fill="x", padx=20, pady=10)
        
        # Voice mode selector
        self._create_mode_selector()
        
        # Waveform visualization
        self.waveform = VoiceWaveform(self.voice_container)
        
        # Voice controls
        self._create_voice_controls()
        
        # Status display
        self._create_status_display()
    
    def _create_mode_selector(self):
        """Create voice mode selector"""
        mode_frame = ctk.CTkFrame(self.voice_container)
        mode_frame.pack(fill="x", pady=(0, 10))
        
        # Mode label
        mode_label = ctk.CTkLabel(
            mode_frame,
            text="🎤 Voice Mode:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        mode_label.pack(side="left", padx=20, pady=10)
        
        # Mode buttons
        self.continuous_btn = ctk.CTkButton(
            mode_frame,
            text="🔄 Continuous",
            command=self._set_continuous_mode,
            width=120,
            fg_color="#10B981",
            hover_color="#059669"
        )
        self.continuous_btn.pack(side="left", padx=10, pady=10)
        
        self.push_to_talk_btn = ctk.CTkButton(
            mode_frame,
            text="🎯 Push-to-Talk",
            command=self._set_push_to_talk_mode,
            width=120,
            fg_color="#6B7280",
            hover_color="#4B5563"
        )
        self.push_to_talk_btn.pack(side="left", padx=10, pady=10)
    
    def _create_voice_controls(self):
        """Create voice control buttons"""
        controls_frame = ctk.CTkFrame(self.voice_container)
        controls_frame.pack(fill="x", pady=10)
        
        # Main voice button
        self.voice_button = ctk.CTkButton(
            controls_frame,
            text="🎤 Start Voice Chat",
            command=self._toggle_voice_chat,
            width=200,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold"),
            fg_color="#00D4FF",
            hover_color="#0099CC"
        )
        self.voice_button.pack(side="left", padx=20, pady=15)
        
        # Stop button
        self.stop_button = ctk.CTkButton(
            controls_frame,
            text="⏹️ Stop",
            command=self._stop_voice_chat,
            width=100,
            height=50,
            fg_color="#EF4444",
            hover_color="#DC2626"
        )
        self.stop_button.pack(side="left", padx=10, pady=15)
        
        # Mute button
        self.mute_button = ctk.CTkButton(
            controls_frame,
            text="🔇 Mute",
            command=self._toggle_mute,
            width=100,
            height=50,
            fg_color="#6B7280",
            hover_color="#4B5563"
        )
        self.mute_button.pack(side="left", padx=10, pady=15)
    
    def _create_status_display(self):
        """Create voice status display"""
        self.status_frame = ctk.CTkFrame(self.voice_container)
        self.status_frame.pack(fill="x", pady=(10, 0))
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="🔵 Ready for voice interaction",
            font=ctk.CTkFont(size=12),
            text_color="#00D4FF"
        )
        self.status_label.pack(pady=10)
    
    def _set_continuous_mode(self):
        """Set continuous conversation mode"""
        self.is_continuous_mode = True
        self.is_push_to_talk = False
        
        # Update button colors
        self.continuous_btn.configure(fg_color="#10B981")
        self.push_to_talk_btn.configure(fg_color="#6B7280")
        
        self._update_status("🔄 Continuous mode active")
    
    def _set_push_to_talk_mode(self):
        """Set push-to-talk mode"""
        self.is_push_to_talk = True
        self.is_continuous_mode = False
        
        # Update button colors
        self.push_to_talk_btn.configure(fg_color="#10B981")
        self.continuous_btn.configure(fg_color="#6B7280")
        
        self._update_status("🎯 Push-to-talk mode active")
    
    def _toggle_voice_chat(self):
        """Toggle voice chat on/off"""
        if not self.conversation_active:
            self._start_voice_chat()
        else:
            self._pause_voice_chat()
    
    def _start_voice_chat(self):
        """Start voice chat session"""
        self.conversation_active = True
        self.is_voice_active = True
        
        # Update UI
        self.voice_button.configure(
            text="🔴 Voice Active",
            fg_color="#EF4444"
        )
        
        # Start waveform
        if self.waveform:
            self.waveform.start_recording()
        
        # Start voice processing
        if self.gideon_core:
            if self.is_continuous_mode:
                self._start_continuous_listening()
            else:
                self._start_push_to_talk()
        
        self._update_status("🎤 Voice chat active - Listening...")
        
        # Callback
        if self.on_voice_start:
            self.on_voice_start()
    
    def _pause_voice_chat(self):
        """Pause voice chat session"""
        self.is_voice_active = False
        
        # Update UI
        self.voice_button.configure(
            text="🎤 Resume Voice Chat",
            fg_color="#00D4FF"
        )
        
        # Stop waveform
        if self.waveform:
            self.waveform.stop_recording()
        
        self._update_status("⏸️ Voice chat paused")
    
    def _stop_voice_chat(self):
        """Stop voice chat session"""
        self.conversation_active = False
        self.is_voice_active = False
        
        # Update UI
        self.voice_button.configure(
            text="🎤 Start Voice Chat",
            fg_color="#00D4FF"
        )
        
        # Stop waveform
        if self.waveform:
            self.waveform.stop_recording()
        
        # Stop voice processing
        if self.gideon_core:
            self.gideon_core.stop_always_listening()
        
        self._update_status("🔵 Voice chat stopped")
        
        # Callback
        if self.on_voice_end:
            self.on_voice_end()
    
    def _toggle_mute(self):
        """Toggle mute on/off"""
        # Implementation for mute functionality
        self._update_status("🔇 Mute toggled")
    
    def _start_continuous_listening(self):
        """Start continuous listening mode"""
        if self.gideon_core:
            self.gideon_core.start_always_listening()
    
    def _start_push_to_talk(self):
        """Start push-to-talk mode"""
        # Implementation for push-to-talk
        pass
    
    def _update_status(self, message: str):
        """Update status display"""
        if hasattr(self, 'status_label'):
            self.status_label.configure(text=message)
    
    def set_listening_state(self):
        """Set interface to listening state"""
        self._update_status("👂 Listening for your voice...")
        if self.waveform:
            self.waveform.start_recording()
    
    def set_processing_state(self):
        """Set interface to processing state"""
        self._update_status("🧠 Processing your request...")
        if self.waveform:
            self.waveform.stop_recording()
    
    def set_speaking_state(self):
        """Set interface to speaking state"""
        self._update_status("🗣️ Gideon is speaking...")
    
    def set_ready_state(self):
        """Set interface to ready state"""
        self._update_status("✅ Ready for next interaction")
    
    def update_audio_visualization(self, audio_data):
        """Update audio visualization with new data"""
        if self.waveform and self.is_voice_active:
            self.waveform.update_audio_data(audio_data)
    
    def destroy(self):
        """Clean up voice chat interface"""
        if self.waveform:
            self.waveform.destroy()
        
        if hasattr(self, 'voice_container'):
            self.voice_container.destroy()
