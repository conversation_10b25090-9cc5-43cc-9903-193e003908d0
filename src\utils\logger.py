"""
Logging configuration for Gideon AI Assistant
"""

import logging
import os
from pathlib import Path
from datetime import datetime

try:
    import colorlog
    COLORLOG_AVAILABLE = True
except ImportError:
    COLORLOG_AVAILABLE = False


def setup_logger(name="<PERSON>", level=logging.INFO):
    """Setup logger with file and console handlers"""
    
    # Create logs directory
    log_dir = Path("data/logs")
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatters
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    if COLORLOG_AVAILABLE:
        console_formatter = colorlog.ColoredFormatter(
            '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
    else:
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
    
    # File handler
    log_file = log_dir / f"gideon_{datetime.now().strftime('%Y%m%d')}.log"
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(file_formatter)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(console_formatter)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger


class GideonLogger:
    """Enhanced logger for Gideon with additional features"""
    
    def __init__(self, name="Gideon"):
        self.logger = setup_logger(name)
        self.conversation_log = []
        
    def info(self, message):
        self.logger.info(message)
        
    def debug(self, message):
        self.logger.debug(message)
        
    def warning(self, message):
        self.logger.warning(message)
        
    def error(self, message):
        self.logger.error(message)
        
    def critical(self, message):
        self.logger.critical(message)
    
    def log_conversation(self, user_input, ai_response):
        """Log conversation for analysis"""
        timestamp = datetime.now().isoformat()
        conversation_entry = {
            "timestamp": timestamp,
            "user": user_input,
            "ai": ai_response
        }
        self.conversation_log.append(conversation_entry)
        
        # Log to file
        self.logger.info(f"CONVERSATION - User: {user_input}")
        self.logger.info(f"CONVERSATION - AI: {ai_response}")
    
    def log_system_event(self, event_type, details):
        """Log system events"""
        self.logger.info(f"SYSTEM_EVENT - {event_type}: {details}")
    
    def log_error_with_context(self, error, context=None):
        """Log error with additional context"""
        error_msg = f"ERROR: {str(error)}"
        if context:
            error_msg += f" | Context: {context}"
        self.logger.error(error_msg)
    
    def get_conversation_history(self, limit=None):
        """Get conversation history"""
        if limit:
            return self.conversation_log[-limit:]
        return self.conversation_log.copy()
    
    def clear_conversation_history(self):
        """Clear conversation history"""
        self.conversation_log.clear()
        self.logger.info("Conversation history cleared")
