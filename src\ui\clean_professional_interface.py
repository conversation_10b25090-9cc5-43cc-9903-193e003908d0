"""
Clean Professional Interface for Gideon AI Assistant
Organized, user-friendly design with Flash-inspired elements and Arabic support
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from typing import Optional, Callable
import threading
import time

from src.utils.logger import <PERSON><PERSON><PERSON>ger
from src.utils.config import Config
from src.utils.i18n import get_i18n
from src.utils.text_direction import text_direction_manager
from src.optimization.performance_optimizer import PerformanceManager
from src.utils.gender_consistency import gideon_identity

try:
    import customtkinter as ctk
    CUSTOMTKINTER_AVAILABLE = True
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False


class CleanProfessionalInterface:
    """Clean, organized professional interface for Gideon AI Assistant"""
    
    def __init__(self, gideon_core=None):
        self.logger = GideonLogger("CleanProfessionalInterface")
        self.config = Config()
        self.i18n = get_i18n()
        self.gideon_core = gideon_core
        
        # Professional color scheme - Clean and organized
        self.colors = {
            # Background layers
            'bg_primary': '#0a0e13',      # Deep background
            'bg_secondary': '#161b22',    # Secondary background
            'bg_elevated': '#21262d',     # Elevated surfaces
            'bg_input': '#30363d',        # Input backgrounds
            
            # Accent colors
            'accent_blue': '#58a6ff',     # Primary blue
            'accent_green': '#56d364',    # Success green
            'accent_red': '#f85149',      # Error red
            'accent_orange': '#e3b341',   # Warning orange
            'accent_purple': '#bc8cff',   # Purple accent
            
            # Text colors
            'text_primary': '#f0f6fc',    # Primary text
            'text_secondary': '#8b949e',  # Secondary text
            'text_muted': '#6e7681',      # Muted text
            
            # Interactive states
            'border': '#30363d',          # Borders
            'hover': '#262c36',           # Hover state
            'active': '#2d333b',          # Active state
        }
        
        # Window setup
        self.root = None
        self.is_running = False
        
        # UI Components - Organized sections
        self.header_frame = None
        self.main_content = None
        self.sidebar = None
        self.chat_area = None
        self.input_area = None
        self.status_bar = None
        
        # Chat components
        self.chat_display = None
        self.input_entry = None
        self.send_button = None
        self.voice_button = None
        
        # Status components
        self.status_label = None
        self.model_label = None
        self.language_label = None
        
        # State management
        self.is_voice_active = False
        self.current_status = "ready"
        self.thinking_animation = False
        
        # Performance manager
        self.performance_manager = PerformanceManager()
    
    def create_window(self):
        """Create the clean professional window"""
        if CUSTOMTKINTER_AVAILABLE:
            self.root = ctk.CTk()
            self._setup_modern_window()
        else:
            self.root = tk.Tk()
            self._setup_basic_window()
        
        self._create_organized_layout()
        self._setup_bindings()
        self._start_status_updates()
        
        return self.root
    
    def _setup_modern_window(self):
        """Setup modern CustomTkinter window"""
        self.root.title("🤖 Gideon AI Assistant - Professional Edition")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)
        self.root.configure(fg_color=self.colors['bg_primary'])
    
    def _setup_basic_window(self):
        """Setup basic Tkinter window"""
        self.root.title("🤖 Gideon AI Assistant - Professional Edition")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)
        self.root.configure(bg=self.colors['bg_primary'])
    
    def _create_organized_layout(self):
        """Create clean, organized layout"""
        # Main container
        if CUSTOMTKINTER_AVAILABLE:
            main_container = ctk.CTkFrame(self.root, fg_color="transparent")
        else:
            main_container = tk.Frame(self.root, bg=self.colors['bg_primary'])
        
        main_container.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Configure grid
        main_container.grid_columnconfigure(1, weight=1)
        main_container.grid_rowconfigure(1, weight=1)
        
        # Create organized sections
        self._create_header(main_container)
        self._create_sidebar(main_container)
        self._create_main_content(main_container)
        self._create_status_bar(main_container)
    
    def _create_header(self, parent):
        """Create clean header with branding and key info"""
        if CUSTOMTKINTER_AVAILABLE:
            self.header_frame = ctk.CTkFrame(
                parent, 
                height=70, 
                fg_color=self.colors['bg_secondary'],
                corner_radius=12
            )
        else:
            self.header_frame = tk.Frame(
                parent, 
                height=70, 
                bg=self.colors['bg_secondary']
            )
        
        self.header_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 15))
        self.header_frame.grid_propagate(False)
        
        # Left side - Branding
        if CUSTOMTKINTER_AVAILABLE:
            brand_frame = ctk.CTkFrame(self.header_frame, fg_color="transparent")
            brand_frame.pack(side="left", padx=20, pady=15)
            
            # Logo and title
            logo_label = ctk.CTkLabel(
                brand_frame,
                text="🤖",
                font=ctk.CTkFont(size=28),
                text_color=self.colors['accent_blue']
            )
            logo_label.pack(side="left", padx=(0, 10))
            
            title_label = ctk.CTkLabel(
                brand_frame,
                text="Gideon AI Assistant",
                font=ctk.CTkFont(size=20, weight="bold"),
                text_color=self.colors['text_primary']
            )
            title_label.pack(side="left")
        
        # Right side - Status indicators
        if CUSTOMTKINTER_AVAILABLE:
            status_frame = ctk.CTkFrame(self.header_frame, fg_color="transparent")
            status_frame.pack(side="right", padx=20, pady=15)
            
            # Language indicator
            self.language_label = ctk.CTkLabel(
                status_frame,
                text="🌍 Arabic/English",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['text_secondary']
            )
            self.language_label.pack(side="right", padx=(15, 0))
            
            # Model indicator
            self.model_label = ctk.CTkLabel(
                status_frame,
                text="🧠 AI Model: Loading...",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['text_secondary']
            )
            self.model_label.pack(side="right", padx=(15, 0))
    
    def _create_sidebar(self, parent):
        """Create organized sidebar with key controls"""
        if CUSTOMTKINTER_AVAILABLE:
            self.sidebar = ctk.CTkFrame(
                parent,
                width=280,
                fg_color=self.colors['bg_secondary'],
                corner_radius=12
            )
        else:
            self.sidebar = tk.Frame(
                parent,
                width=280,
                bg=self.colors['bg_secondary']
            )
        
        self.sidebar.grid(row=1, column=0, sticky="nsew", padx=(0, 15))
        self.sidebar.grid_propagate(False)
        
        # Create sidebar sections
        self._create_ai_status_section()
        self._create_voice_controls_section()
        self._create_quick_actions_section()
        self._create_model_management_section()
    
    def _create_ai_status_section(self):
        """Create AI status section"""
        if CUSTOMTKINTER_AVAILABLE:
            section = ctk.CTkFrame(
                self.sidebar,
                fg_color=self.colors['bg_elevated'],
                corner_radius=8
            )
            section.pack(fill="x", padx=15, pady=(15, 10))
            
            # Section header
            header = ctk.CTkLabel(
                section,
                text="🤖 AI Status",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=self.colors['text_primary']
            )
            header.pack(anchor="w", padx=15, pady=(15, 10))
            
            # Status indicators
            self.ai_status_label = ctk.CTkLabel(
                section,
                text="● Ready",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['accent_green']
            )
            self.ai_status_label.pack(anchor="w", padx=15, pady=2)
            
            self.response_time_label = ctk.CTkLabel(
                section,
                text="⚡ Response: <1ms",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['text_secondary']
            )
            self.response_time_label.pack(anchor="w", padx=15, pady=(2, 15))
    
    def _create_voice_controls_section(self):
        """Create voice controls section"""
        if CUSTOMTKINTER_AVAILABLE:
            section = ctk.CTkFrame(
                self.sidebar,
                fg_color=self.colors['bg_elevated'],
                corner_radius=8
            )
            section.pack(fill="x", padx=15, pady=10)
            
            # Section header
            header = ctk.CTkLabel(
                section,
                text="🎤 Voice Control",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=self.colors['text_primary']
            )
            header.pack(anchor="w", padx=15, pady=(15, 10))
            
            # Voice button
            self.voice_button = ctk.CTkButton(
                section,
                text="🎤 Start Voice",
                font=ctk.CTkFont(size=12, weight="bold"),
                fg_color=self.colors['accent_blue'],
                hover_color=self.colors['accent_purple'],
                command=self._toggle_voice_input,
                height=35
            )
            self.voice_button.pack(fill="x", padx=15, pady=(0, 15))
    
    def _create_quick_actions_section(self):
        """Create quick actions section"""
        if CUSTOMTKINTER_AVAILABLE:
            section = ctk.CTkFrame(
                self.sidebar,
                fg_color=self.colors['bg_elevated'],
                corner_radius=8
            )
            section.pack(fill="x", padx=15, pady=10)
            
            # Section header
            header = ctk.CTkLabel(
                section,
                text="⚡ Quick Actions",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=self.colors['text_primary']
            )
            header.pack(anchor="w", padx=15, pady=(15, 10))
            
            # Action buttons
            actions = [
                ("🗑️ Clear Chat", self._clear_chat),
                ("🌍 Toggle Language", self._toggle_language),
                ("📸 Screenshot", self._take_screenshot),
                ("❓ Help", self._show_help)
            ]
            
            for text, command in actions:
                btn = ctk.CTkButton(
                    section,
                    text=text,
                    font=ctk.CTkFont(size=11),
                    fg_color=self.colors['bg_input'],
                    hover_color=self.colors['hover'],
                    command=command,
                    height=30
                )
                btn.pack(fill="x", padx=15, pady=2)
            
            # Add padding at bottom
            ctk.CTkLabel(section, text="", height=10).pack()
    
    def _create_model_management_section(self):
        """Create model management section"""
        if CUSTOMTKINTER_AVAILABLE:
            section = ctk.CTkFrame(
                self.sidebar,
                fg_color=self.colors['bg_elevated'],
                corner_radius=8
            )
            section.pack(fill="x", padx=15, pady=10)
            
            # Section header
            header = ctk.CTkLabel(
                section,
                text="🧠 AI Models",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=self.colors['text_primary']
            )
            header.pack(anchor="w", padx=15, pady=(15, 10))
            
            # Model selector button
            model_btn = ctk.CTkButton(
                section,
                text="🔧 Manage Models",
                font=ctk.CTkFont(size=12),
                fg_color=self.colors['accent_purple'],
                hover_color=self.colors['accent_blue'],
                command=self._open_model_manager,
                height=35
            )
            model_btn.pack(fill="x", padx=15, pady=(0, 15))

    def _create_main_content(self, parent):
        """Create main content area with chat interface"""
        if CUSTOMTKINTER_AVAILABLE:
            self.main_content = ctk.CTkFrame(
                parent,
                fg_color=self.colors['bg_secondary'],
                corner_radius=12
            )
        else:
            self.main_content = tk.Frame(
                parent,
                bg=self.colors['bg_secondary']
            )

        self.main_content.grid(row=1, column=1, sticky="nsew")

        # Configure main content grid
        self.main_content.grid_rowconfigure(0, weight=1)
        self.main_content.grid_columnconfigure(0, weight=1)

        # Create chat area and input area
        self._create_chat_area()
        self._create_input_area()

    def _create_chat_area(self):
        """Create clean chat display area"""
        if CUSTOMTKINTER_AVAILABLE:
            chat_container = ctk.CTkFrame(
                self.main_content,
                fg_color=self.colors['bg_elevated'],
                corner_radius=8
            )
            chat_container.grid(row=0, column=0, sticky="nsew", padx=15, pady=(15, 10))

            # Chat display with proper Arabic support
            self.chat_display = ctk.CTkTextbox(
                chat_container,
                font=ctk.CTkFont(family="Consolas", size=12),
                fg_color=self.colors['bg_primary'],
                text_color=self.colors['text_primary'],
                wrap="word",
                corner_radius=8,
                border_width=1,
                border_color=self.colors['border']
            )
            self.chat_display.pack(fill="both", expand=True, padx=15, pady=15)
        else:
            from tkinter import scrolledtext
            chat_container = tk.Frame(
                self.main_content,
                bg=self.colors['bg_elevated']
            )
            chat_container.grid(row=0, column=0, sticky="nsew", padx=15, pady=(15, 10))

            self.chat_display = scrolledtext.ScrolledText(
                chat_container,
                font=("Consolas", 11),
                bg=self.colors['bg_primary'],
                fg=self.colors['text_primary'],
                insertbackground=self.colors['text_primary'],
                wrap=tk.WORD,
                relief="flat",
                bd=1
            )
            self.chat_display.pack(fill="both", expand=True, padx=15, pady=15)

    def _create_input_area(self):
        """Create clean input area"""
        if CUSTOMTKINTER_AVAILABLE:
            input_container = ctk.CTkFrame(
                self.main_content,
                height=80,
                fg_color=self.colors['bg_elevated'],
                corner_radius=8
            )
            input_container.grid(row=1, column=0, sticky="ew", padx=15, pady=(0, 15))
            input_container.grid_propagate(False)

            # Input entry
            self.input_entry = ctk.CTkEntry(
                input_container,
                placeholder_text="Type your message here... (Arabic/English)",
                font=ctk.CTkFont(size=12),
                height=40,
                fg_color=self.colors['bg_input'],
                text_color=self.colors['text_primary'],
                placeholder_text_color=self.colors['text_muted']
            )
            self.input_entry.pack(side="left", fill="x", expand=True, padx=(15, 10), pady=20)

            # Send button
            self.send_button = ctk.CTkButton(
                input_container,
                text="Send",
                width=80,
                height=40,
                font=ctk.CTkFont(size=12, weight="bold"),
                fg_color=self.colors['accent_green'],
                hover_color=self.colors['accent_blue'],
                command=self._send_message
            )
            self.send_button.pack(side="right", padx=(5, 15), pady=20)
        else:
            input_container = tk.Frame(
                self.main_content,
                height=80,
                bg=self.colors['bg_elevated']
            )
            input_container.grid(row=1, column=0, sticky="ew", padx=15, pady=(0, 15))
            input_container.grid_propagate(False)

            # Input entry
            self.input_entry = tk.Entry(
                input_container,
                font=("Arial", 12),
                bg=self.colors['bg_input'],
                fg=self.colors['text_primary'],
                insertbackground=self.colors['text_primary'],
                relief="flat",
                bd=5
            )
            self.input_entry.pack(side="left", fill="x", expand=True, padx=(15, 10), pady=20)

            # Send button
            self.send_button = tk.Button(
                input_container,
                text="Send",
                font=("Arial", 10, "bold"),
                bg=self.colors['accent_green'],
                fg=self.colors['text_primary'],
                relief="flat",
                bd=0,
                width=8,
                command=self._send_message
            )
            self.send_button.pack(side="right", padx=(5, 15), pady=20)

    def _create_status_bar(self, parent):
        """Create clean status bar"""
        if CUSTOMTKINTER_AVAILABLE:
            status_container = ctk.CTkFrame(
                parent,
                height=40,
                fg_color=self.colors['bg_secondary'],
                corner_radius=8
            )
            status_container.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(15, 0))
            status_container.grid_propagate(False)

            # Status label
            self.status_label = ctk.CTkLabel(
                status_container,
                text="✅ Ready",
                font=ctk.CTkFont(size=11),
                text_color=self.colors['text_secondary']
            )
            self.status_label.pack(side="left", padx=20, pady=10)

            # Performance indicator
            self.performance_label = ctk.CTkLabel(
                status_container,
                text="⚡ Ultra-Low Latency Active",
                font=ctk.CTkFont(size=11),
                text_color=self.colors['accent_blue']
            )
            self.performance_label.pack(side="right", padx=20, pady=10)
        else:
            status_container = tk.Frame(
                parent,
                height=40,
                bg=self.colors['bg_secondary']
            )
            status_container.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(15, 0))
            status_container.grid_propagate(False)

            # Status label
            self.status_label = tk.Label(
                status_container,
                text="✅ Ready",
                font=("Arial", 9),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_secondary']
            )
            self.status_label.pack(side="left", padx=20, pady=10)

    def _setup_bindings(self):
        """Setup keyboard shortcuts and bindings"""
        self.root.bind('<Return>', lambda e: self._send_message())
        self.root.bind('<Control-n>', lambda e: self._clear_chat())
        self.root.bind('<Control-l>', lambda e: self._toggle_language())
        self.root.bind('<F1>', lambda e: self._show_help())

        # Focus on input
        if self.input_entry:
            self.input_entry.focus()

    def _start_status_updates(self):
        """Start status update loop"""
        self._update_clock()
        self._update_model_status()

    def _update_clock(self):
        """Update clock display"""
        if hasattr(self, 'clock_label'):
            current_time = datetime.now().strftime("%H:%M:%S")
            self.clock_label.configure(text=current_time)

        # Schedule next update
        self.root.after(1000, self._update_clock)

    def _update_model_status(self):
        """Update model status display"""
        if self.gideon_core and hasattr(self.gideon_core, 'ai_engine') and self.gideon_core.ai_engine:
            if hasattr(self.gideon_core.ai_engine, 'active_backend') and self.gideon_core.ai_engine.active_backend:
                model_name = getattr(self.gideon_core.ai_engine.active_backend, 'model_name', 'Unknown')
                if hasattr(self, 'model_label'):
                    self.model_label.configure(text=f"🧠 AI Model: {model_name}")

        # Schedule next update
        self.root.after(5000, self._update_model_status)

    def _send_message(self):
        """Send message with Arabic support"""
        if not self.input_entry:
            return

        message = self.input_entry.get().strip()
        if not message:
            return

        # Clear input
        self.input_entry.delete(0, 'end')

        # Add user message with proper Arabic formatting
        self._add_message("You", message, "user")

        # Update status
        self._update_status("🤔 Thinking...")

        # Process with AI in background
        threading.Thread(target=self._process_ai_response, args=(message,), daemon=True).start()

    def _process_ai_response(self, message: str):
        """Process AI response with Arabic support"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'ai_engine') and self.gideon_core.ai_engine:
                # Use performance manager for optimized response
                response = self.performance_manager.process_request_optimized(
                    message,
                    self.gideon_core.ai_engine
                )

                # Apply gender consistency
                corrected_response, _ = gideon_identity.validate_and_correct(response)

                # Update UI on main thread
                self.root.after(0, lambda: self._add_message("Gideon", corrected_response, "ai"))
                self.root.after(0, lambda: self._update_status("✅ Ready"))
            else:
                self.root.after(0, lambda: self._add_message("System", "AI engine not available", "system"))
                self.root.after(0, lambda: self._update_status("❌ Error"))
        except Exception as e:
            self.logger.error(f"AI response error: {e}")
            self.root.after(0, lambda: self._add_message("System", f"Error: {e}", "system"))
            self.root.after(0, lambda: self._update_status("❌ Error"))

    def _add_message(self, sender: str, message: str, msg_type: str = "user"):
        """Add message to chat with proper Arabic support"""
        if not self.chat_display:
            return

        timestamp = datetime.now().strftime("%H:%M")

        # Format message based on type
        if msg_type == "user":
            icon = "👤"
        elif msg_type == "ai":
            icon = "🤖"
        else:
            icon = "⚙️"

        # Handle Arabic text direction using the same logic as simple interface
        formatted_message, direction = text_direction_manager.format_text_for_display(message)

        if direction == "rtl":
            # Arabic text - right to left
            display_text = f"{formatted_message} :{sender} {icon} [{timestamp}]\n"
        else:
            # English text - left to right
            display_text = f"[{timestamp}] {icon} {sender}: {formatted_message}\n"

        # Insert message
        if CUSTOMTKINTER_AVAILABLE:
            self.chat_display.insert("end", display_text)
            self.chat_display.see("end")
        else:
            self.chat_display.insert(tk.END, display_text)
            self.chat_display.see(tk.END)

    def _toggle_voice_input(self):
        """Toggle voice input"""
        if not self.gideon_core:
            self._add_message("System", "Voice input not available", "system")
            return

        if self.is_voice_active:
            self._stop_voice_input()
        else:
            self._start_voice_input()

    def _start_voice_input(self):
        """Start voice input"""
        self.is_voice_active = True
        if CUSTOMTKINTER_AVAILABLE:
            self.voice_button.configure(text="🔴 Stop Voice", fg_color=self.colors['accent_red'])

        self._update_status("🎤 Listening...")

        # Start voice recognition in background
        threading.Thread(target=self._voice_recognition, daemon=True).start()

    def _stop_voice_input(self):
        """Stop voice input"""
        self.is_voice_active = False
        if CUSTOMTKINTER_AVAILABLE:
            self.voice_button.configure(text="🎤 Start Voice", fg_color=self.colors['accent_blue'])

        self._update_status("✅ Ready")

    def _voice_recognition(self):
        """Handle voice recognition"""
        try:
            if self.gideon_core and hasattr(self.gideon_core, 'stt_engine'):
                # Listen for speech
                text = self.gideon_core.stt_engine.listen_once()
                if text:
                    # Insert recognized text into input
                    self.root.after(0, lambda: self.input_entry.insert(0, text))
                    self.root.after(0, lambda: self._stop_voice_input())
                else:
                    self.root.after(0, lambda: self._stop_voice_input())
            else:
                self.root.after(0, lambda: self._add_message("System", "Voice recognition not available", "system"))
                self.root.after(0, lambda: self._stop_voice_input())
        except Exception as e:
            self.logger.error(f"Voice recognition error: {e}")
            self.root.after(0, lambda: self._stop_voice_input())

    def _clear_chat(self):
        """Clear chat history"""
        if self.chat_display:
            if CUSTOMTKINTER_AVAILABLE:
                self.chat_display.delete("1.0", "end")
            else:
                self.chat_display.delete(1.0, tk.END)

            self._add_message("System", "Chat cleared", "system")

    def _toggle_language(self):
        """Toggle between Arabic and English"""
        try:
            current_lang = self.config.get("app.language", "en")

            if current_lang == "en":
                # Switch to Arabic
                self.config.set("app.language", "ar")

                # Set AI engine response language to Arabic
                if self.gideon_core and self.gideon_core.ai_engine:
                    self.gideon_core.ai_engine.set_response_language("ar")
                    self.logger.info("🌍 AI engine response language set to Arabic")

                self._add_message("System", "تم تغيير اللغة إلى العربية 🇸🇦", "system")

                if hasattr(self, 'language_label'):
                    self.language_label.configure(text="🌍 Arabic (Primary)")
            else:
                # Switch to English
                self.config.set("app.language", "en")

                # Set AI engine response language to English
                if self.gideon_core and self.gideon_core.ai_engine:
                    self.gideon_core.ai_engine.set_response_language("en")
                    self.logger.info("🌍 AI engine response language set to English")

                self._add_message("System", "Language switched to English 🇺🇸", "system")

                if hasattr(self, 'language_label'):
                    self.language_label.configure(text="🌍 English (Primary)")

        except Exception as e:
            self.logger.error(f"Error toggling language: {e}")
            self._add_message("System", f"Language toggle error: {e}", "system")

    def _take_screenshot(self):
        """Take screenshot"""
        if self.gideon_core:
            self._update_status("📸 Taking screenshot...")
            filepath = self.gideon_core.take_screenshot()
            if filepath:
                self._add_message("System", f"📸 Screenshot saved: {filepath}", "system")
                self._update_status("✅ Ready")
            else:
                self._add_message("System", "❌ Failed to take screenshot", "system")
                self._update_status("✅ Ready")
        else:
            self._add_message("System", "❌ Screenshot functionality not available", "system")

    def _open_model_manager(self):
        """Open model manager"""
        try:
            from src.ui.model_selector import ModelSelectorDialog

            def on_model_changed(backend, model):
                self._add_message("System", f"🧠 AI Model changed: {model} via {backend}", "system")
                self._update_status("✅ Model updated")

            if self.gideon_core and hasattr(self.gideon_core, 'ai_engine'):
                dialog = ModelSelectorDialog(
                    self.root,
                    self.gideon_core.ai_engine,
                    on_model_changed
                )
                result = dialog.show()

                if result:
                    self.logger.info(f"Model configuration updated: {result}")
                    self._add_message("System", "✅ AI model configuration updated", "system")
            else:
                self._add_message("System", "❌ AI engine not available", "system")

        except ImportError:
            self._add_message("System", "❌ Model selector not available", "system")
        except Exception as e:
            self.logger.error(f"Error opening model selector: {e}")
            self._add_message("System", f"❌ Model selector error: {e}", "system")

    def _show_help(self):
        """Show help information"""
        help_text = """🤖 Gideon AI Assistant - Professional Edition

KEYBOARD SHORTCUTS:
• Enter: Send message
• Ctrl+N: Clear chat
• Ctrl+L: Toggle language
• F1: Show this help

FEATURES:
• Type messages in Arabic or English
• Click voice button for voice input
• Automatic language detection
• Real-time AI responses
• Professional interface design

VOICE COMMANDS:
• Say "Gideon" + your question for voice interaction
• Supports both Arabic and English

LANGUAGE SUPPORT:
• Arabic: Right-to-left text display
• English: Left-to-right text display
• Automatic language detection
• Toggle between languages with Ctrl+L"""

        messagebox.showinfo("Help - Gideon AI Assistant", help_text)

    def _update_status(self, status: str):
        """Update status display"""
        if self.status_label:
            if CUSTOMTKINTER_AVAILABLE:
                self.status_label.configure(text=status)
            else:
                self.status_label.configure(text=status)

    def run(self):
        """Start the interface"""
        if not self.root:
            self.create_window()

        self.is_running = True
        self._add_message("System", "🚀 Gideon AI Assistant Ready!", "system")
        self._add_message("System", "Professional interface with Arabic support active", "system")

        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.logger.info("Interface closed by user")
        finally:
            self.is_running = False

    def stop(self):
        """Stop the interface"""
        self.is_running = False
        if self.root:
            self.root.quit()
            self.root.destroy()
