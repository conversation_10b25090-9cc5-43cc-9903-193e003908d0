#!/usr/bin/env python3
"""
Enterprise Splash Screen for Gideon AI Assistant
Professional loading screen with progress indicators and smooth animations
"""

import tkinter as tk
import time
import threading
from typing import Callable, Optional
from src.utils.logger import <PERSON><PERSON><PERSON>ger

try:
    import customtkinter as ctk
    CUSTOMTKINTER_AVAILABLE = True
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False

class EnterpriseSplashScreen:
    """Professional splash screen with Full HD support and smooth animations"""
    
    def __init__(self, on_complete: Optional[Callable] = None):
        self.logger = GideonLogger("EnterpriseSplash")
        self.on_complete = on_complete
        
        # Splash window
        self.splash = None
        self.is_running = False
        
        # Progress tracking
        self.current_progress = 0.0
        self.target_progress = 0.0
        self.progress_steps = []
        self.current_step = 0
        
        # Animation state
        self.animation_running = False
        self.fade_alpha = 0.0
        
        # Design system for splash
        self.design = {
            'colors': {
                'bg_primary': '#0a0e13',
                'bg_secondary': '#161b22',
                'accent_primary': '#58a6ff',
                'accent_secondary': '#bc8cff',
                'text_primary': '#ffffff',
                'text_secondary': '#8b949e',
                'success': '#56d364',
                'glow': '#58a6ff40'
            },
            'fonts': {
                'title': ('Segoe UI Variable Display', 28, 'bold'),
                'subtitle': ('Segoe UI Variable', 14, 'normal'),
                'status': ('Segoe UI Variable', 12, 'normal'),
                'version': ('Segoe UI Variable', 10, 'normal')
            }
        }
        
        # Loading steps
        self.loading_steps = [
            {"text": "Initializing Enterprise Systems...", "duration": 0.8},
            {"text": "Loading AI Engine...", "duration": 1.2},
            {"text": "Configuring Neural Networks...", "duration": 1.0},
            {"text": "Establishing Voice Recognition...", "duration": 0.9},
            {"text": "Preparing Bilingual Support...", "duration": 0.7},
            {"text": "Optimizing Performance...", "duration": 0.6},
            {"text": "Finalizing Interface...", "duration": 0.5},
            {"text": "Enterprise Ready!", "duration": 0.3}
        ]
    
    def show(self, total_duration: float = 6.0):
        """Show the splash screen with smooth loading animation"""
        try:
            self.is_running = True
            self._create_splash_window()
            self._start_loading_sequence(total_duration)
            self._run_splash()
        except Exception as e:
            self.logger.error(f"Error showing splash screen: {e}")
            if self.on_complete:
                self.on_complete()
    
    def _create_splash_window(self):
        """Create the professional splash window"""
        if CUSTOMTKINTER_AVAILABLE:
            self.splash = ctk.CTkToplevel()
        else:
            self.splash = tk.Toplevel()
        
        # Window configuration for Full HD
        window_width = 800
        window_height = 500
        
        # Center on screen
        screen_width = self.splash.winfo_screenwidth()
        screen_height = self.splash.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.splash.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.splash.title("Gideon AI Assistant - Enterprise Edition")
        
        # Window properties
        self.splash.resizable(False, False)
        self.splash.overrideredirect(True)  # Remove window decorations
        
        if CUSTOMTKINTER_AVAILABLE:
            self.splash.configure(fg_color=self.design['colors']['bg_primary'])
        else:
            self.splash.configure(bg=self.design['colors']['bg_primary'])
        
        # Set window attributes
        try:
            self.splash.attributes('-topmost', True)
            self.splash.attributes('-alpha', 0.0)  # Start transparent
        except:
            pass
        
        self._create_splash_content()
    
    def _create_splash_content(self):
        """Create the splash screen content"""
        if CUSTOMTKINTER_AVAILABLE:
            self._create_modern_content()
        else:
            self._create_fallback_content()
    
    def _create_modern_content(self):
        """Create modern splash content with CustomTkinter"""
        # Main container
        main_frame = ctk.CTkFrame(
            self.splash,
            fg_color="transparent",
            corner_radius=0
        )
        main_frame.pack(fill="both", expand=True, padx=40, pady=40)
        
        # Header section
        header_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        header_frame.pack(fill="x", pady=(0, 40))
        
        # Logo and title
        logo_label = ctk.CTkLabel(
            header_frame,
            text="🤖",
            font=ctk.CTkFont(size=64),
            text_color=self.design['colors']['accent_primary']
        )
        logo_label.pack(pady=(20, 10))
        
        title_label = ctk.CTkLabel(
            header_frame,
            text="GIDEON AI ASSISTANT",
            font=ctk.CTkFont(
                family=self.design['fonts']['title'][0],
                size=self.design['fonts']['title'][1],
                weight=self.design['fonts']['title'][2]
            ),
            text_color=self.design['colors']['text_primary']
        )
        title_label.pack()
        
        subtitle_label = ctk.CTkLabel(
            header_frame,
            text="Enterprise Edition",
            font=ctk.CTkFont(
                family=self.design['fonts']['subtitle'][0],
                size=self.design['fonts']['subtitle'][1]
            ),
            text_color=self.design['colors']['accent_secondary']
        )
        subtitle_label.pack(pady=(5, 0))
        
        # Progress section
        progress_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        progress_frame.pack(fill="x", pady=(0, 20))
        
        # Progress bar
        self.progress_bar = ctk.CTkProgressBar(
            progress_frame,
            height=8,
            fg_color=self.design['colors']['bg_secondary'],
            progress_color=self.design['colors']['accent_primary']
        )
        self.progress_bar.pack(fill="x", pady=(0, 15))
        self.progress_bar.set(0.0)
        
        # Status text
        self.status_label = ctk.CTkLabel(
            progress_frame,
            text="Initializing...",
            font=ctk.CTkFont(
                family=self.design['fonts']['status'][0],
                size=self.design['fonts']['status'][1]
            ),
            text_color=self.design['colors']['text_secondary']
        )
        self.status_label.pack()
        
        # Footer section
        footer_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        footer_frame.pack(side="bottom", fill="x")
        
        version_label = ctk.CTkLabel(
            footer_frame,
            text="Version 2.0.0 Enterprise | Full HD Optimized",
            font=ctk.CTkFont(
                family=self.design['fonts']['version'][0],
                size=self.design['fonts']['version'][1]
            ),
            text_color=self.design['colors']['text_secondary']
        )
        version_label.pack(side="bottom")
        
        copyright_label = ctk.CTkLabel(
            footer_frame,
            text="© 2024 Gideon AI Systems. Enterprise AI Assistant.",
            font=ctk.CTkFont(
                family=self.design['fonts']['version'][0],
                size=self.design['fonts']['version'][1]
            ),
            text_color=self.design['colors']['text_secondary']
        )
        copyright_label.pack(side="bottom", pady=(0, 10))
    
    def _create_fallback_content(self):
        """Create fallback content for standard Tkinter"""
        # Main frame
        main_frame = tk.Frame(self.splash, bg=self.design['colors']['bg_primary'])
        main_frame.pack(fill="both", expand=True, padx=40, pady=40)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="🤖 GIDEON AI ASSISTANT",
            font=self.design['fonts']['title'],
            fg=self.design['colors']['text_primary'],
            bg=self.design['colors']['bg_primary']
        )
        title_label.pack(pady=(50, 10))
        
        subtitle_label = tk.Label(
            main_frame,
            text="Enterprise Edition",
            font=self.design['fonts']['subtitle'],
            fg=self.design['colors']['accent_secondary'],
            bg=self.design['colors']['bg_primary']
        )
        subtitle_label.pack()
        
        # Status
        self.status_label = tk.Label(
            main_frame,
            text="Initializing...",
            font=self.design['fonts']['status'],
            fg=self.design['colors']['text_secondary'],
            bg=self.design['colors']['bg_primary']
        )
        self.status_label.pack(pady=(100, 20))
        
        # Simple progress indicator
        self.progress_frame = tk.Frame(main_frame, bg=self.design['colors']['bg_primary'])
        self.progress_frame.pack(fill="x", padx=100)
        
        self.progress_canvas = tk.Canvas(
            self.progress_frame,
            height=8,
            bg=self.design['colors']['bg_secondary'],
            highlightthickness=0
        )
        self.progress_canvas.pack(fill="x")
    
    def _start_loading_sequence(self, total_duration: float):
        """Start the loading sequence with smooth progress"""
        def loading_thread():
            step_duration = total_duration / len(self.loading_steps)
            
            for i, step in enumerate(self.loading_steps):
                if not self.is_running:
                    break
                
                # Update status
                self.splash.after(0, lambda text=step["text"]: self._update_status(text))
                
                # Animate progress
                target_progress = (i + 1) / len(self.loading_steps)
                self._animate_progress(target_progress, step_duration)
                
                time.sleep(step_duration)
            
            # Fade out and complete
            self.splash.after(0, self._fade_out_and_complete)
        
        threading.Thread(target=loading_thread, daemon=True).start()
    
    def _update_status(self, text: str):
        """Update the status text"""
        if self.status_label and self.is_running:
            self.status_label.configure(text=text)
    
    def _animate_progress(self, target: float, duration: float):
        """Animate progress bar to target value"""
        if not self.is_running:
            return
        
        start_progress = self.current_progress
        steps = int(duration * 60)  # 60 FPS
        
        for step in range(steps):
            if not self.is_running:
                break
            
            progress = start_progress + (target - start_progress) * (step / steps)
            self.current_progress = progress
            
            self.splash.after(0, lambda p=progress: self._update_progress(p))
            time.sleep(1/60)  # 60 FPS
    
    def _update_progress(self, progress: float):
        """Update the progress bar"""
        if CUSTOMTKINTER_AVAILABLE and hasattr(self, 'progress_bar'):
            self.progress_bar.set(progress)
        elif hasattr(self, 'progress_canvas'):
            # Update canvas progress for fallback
            width = self.progress_canvas.winfo_width()
            if width > 1:
                self.progress_canvas.delete("progress")
                self.progress_canvas.create_rectangle(
                    0, 0, width * progress, 8,
                    fill=self.design['colors']['accent_primary'],
                    tags="progress"
                )
    
    def _fade_out_and_complete(self):
        """Fade out the splash screen and complete"""
        def fade_step(alpha):
            if alpha <= 0:
                self._complete()
                return
            
            try:
                self.splash.attributes('-alpha', alpha)
                self.splash.after(16, lambda: fade_step(alpha - 0.05))  # ~60 FPS
            except:
                self._complete()
        
        fade_step(1.0)
    
    def _complete(self):
        """Complete the splash screen"""
        self.is_running = False
        if self.splash:
            self.splash.destroy()
        
        if self.on_complete:
            self.on_complete()
    
    def _run_splash(self):
        """Run the splash screen"""
        # Fade in
        def fade_in(alpha):
            if alpha >= 1.0:
                return
            try:
                self.splash.attributes('-alpha', alpha)
                self.splash.after(16, lambda: fade_in(alpha + 0.05))
            except:
                pass
        
        fade_in(0.0)
        
        # Keep splash running
        self.splash.mainloop()
