"""
Speech-to-Text Engine for Gideon AI Assistant
Offline speech recognition capabilities
"""

import threading
import time
from typing import Optional, Callable

try:
    import speech_recognition as sr
    SPEECH_RECOGNITION_AVAILABLE = True
except ImportError:
    SPEECH_RECOGNITION_AVAILABLE = False

from src.utils.logger import <PERSON><PERSON><PERSON><PERSON>
from src.utils.config import Config


class STTEngine:
    """Speech-to-Text Engine"""
    
    def __init__(self):
        self.logger = <PERSON>Logger("STTEngine")
        self.config = Config()
        
        self.recognizer = None
        self.microphone = None
        self.is_listening = False
        self.is_initialized = False

        # Enhanced recognition settings
        self.energy_threshold = None
        self.dynamic_energy_threshold = True
        self.pause_threshold = 0.8  # Seconds of silence before considering phrase complete
        self.phrase_threshold = 0.3  # Minimum seconds of speaking audio before considering phrase
        self.non_speaking_duration = 0.5  # Seconds of silence to keep on both sides

        # Language detection and bilingual support
        self.current_language = "auto"  # auto, en, ar
        self.language_confidence_threshold = 0.7
        self.bilingual_mode = True
        self.supported_languages = ["en-US", "ar-SA", "ar-EG", "ar-AE"]

        # Wake word detection improvements
        self.wake_word_sensitivity = 0.6  # Lower = more sensitive
        self.wake_word_timeout = 30.0  # Seconds to wait for command after wake word
        self.last_wake_word_time = 0
        self.wake_word_variants = {}  # Will be populated in initialization

        # Performance tracking
        self.recognition_attempts = 0
        self.successful_recognitions = 0
        self.wake_word_detections = 0
        self.language_detection_stats = {"en": 0, "ar": 0}

        # Callbacks
        self.on_speech_detected = None
        self.on_speech_recognized = None
        self.on_error = None
        self.on_language_detected = None
        self.on_wake_word_detected = None
        
        # Initialize if available
        if SPEECH_RECOGNITION_AVAILABLE:
            self._initialize()
    
    def _initialize(self):
        """Initialize speech recognition with enhanced bilingual support"""
        try:
            self.recognizer = sr.Recognizer()

            # Try to find the best microphone
            mic_list = sr.Microphone.list_microphone_names()
            self.logger.info(f"Available microphones: {mic_list}")

            # Use default microphone or find the best one
            self.microphone = self._find_best_microphone()

            # Enhanced calibration for better accuracy
            self._calibrate_microphone()

            # Configure recognizer with optimized settings
            self._configure_recognizer()

            # Initialize wake word variants for bilingual support
            self._initialize_wake_word_variants()

            # Initialize language detection
            self._initialize_language_detection()

            self.is_initialized = True
            self.logger.info("✅ Enhanced speech recognition initialized successfully")
            self.logger.info(f"🌍 Bilingual mode: {self.bilingual_mode}")
            self.logger.info(f"🎯 Supported languages: {self.supported_languages}")

        except Exception as e:
            self.logger.error(f"Failed to initialize speech recognition: {e}")
            self.is_initialized = False

    def _find_best_microphone(self):
        """Find the best available microphone"""
        try:
            # Try to find a headset microphone first (usually better quality)
            mic_list = sr.Microphone.list_microphone_names()

            # Preferred microphone keywords (in order of preference)
            preferred_keywords = [
                "headset", "headphone", "gaming", "usb", "wireless",
                "bluetooth", "external", "desktop", "studio"
            ]

            for keyword in preferred_keywords:
                for i, mic_name in enumerate(mic_list):
                    if keyword.lower() in mic_name.lower():
                        self.logger.info(f"🎤 Selected preferred microphone: {mic_name}")
                        return sr.Microphone(device_index=i)

            # Fallback to default microphone
            self.logger.info("🎤 Using default microphone")
            return sr.Microphone()

        except Exception as e:
            self.logger.warning(f"Error selecting microphone: {e}, using default")
            return sr.Microphone()

    def _calibrate_microphone(self):
        """Enhanced microphone calibration"""
        try:
            with self.microphone as source:
                self.logger.info("🎤 Calibrating microphone for ambient noise...")
                self.logger.info("Please be quiet for a moment while I calibrate...")

                # Longer calibration for better accuracy
                self.recognizer.adjust_for_ambient_noise(source, duration=3)

                # Store the calibrated energy threshold
                self.energy_threshold = self.recognizer.energy_threshold

                self.logger.info(f"✅ Microphone calibrated. Energy threshold: {self.energy_threshold}")

        except Exception as e:
            self.logger.error(f"Microphone calibration failed: {e}")
            # Use default values
            self.energy_threshold = 300
            self.recognizer.energy_threshold = self.energy_threshold

    def _configure_recognizer(self):
        """Configure recognizer with optimized settings"""
        # Optimized settings for better wake word detection and accuracy
        self.recognizer.energy_threshold = max(self.energy_threshold or 300, 150)  # Not too low to avoid noise
        self.recognizer.dynamic_energy_threshold = True  # Adapt to environment
        self.recognizer.pause_threshold = self.pause_threshold  # Wait for complete phrases
        self.recognizer.phrase_threshold = self.phrase_threshold  # Minimum speech duration
        self.recognizer.non_speaking_duration = self.non_speaking_duration  # Silence padding

        self.logger.info(f"🔧 Recognizer configured:")
        self.logger.info(f"   Energy threshold: {self.recognizer.energy_threshold}")
        self.logger.info(f"   Pause threshold: {self.recognizer.pause_threshold}s")
        self.logger.info(f"   Phrase threshold: {self.recognizer.phrase_threshold}s")

    def _initialize_wake_word_variants(self):
        """Initialize wake word variants for better detection"""
        self.wake_word_variants = {
            "en": [
                "gideon", "gedeon", "guideon", "gidion", "gidean",
                "jideon", "gidion", "gedion", "gidean", "gidian"
            ],
            "ar": [
                "جيديون", "جيدون", "جيديان", "جيدان", "جيديون",
                "gideon", "gedeon"  # English fallback in Arabic mode
            ]
        }

        self.logger.info(f"🎯 Wake word variants initialized: {len(self.wake_word_variants['en'])} English, {len(self.wake_word_variants['ar'])} Arabic")

    def _initialize_language_detection(self):
        """Initialize language detection capabilities"""
        self.current_language = self.config.get("speech.language", "auto")
        self.bilingual_mode = self.config.get("speech.bilingual_mode", True)
        
        # Override bilingual mode if language is set to auto
        if self.current_language == "auto":
            self.bilingual_mode = True
            self.logger.info("🌍 Auto language detection enabled (bilingual mode)")
        elif self.bilingual_mode:
            self.logger.info(f"🌍 Bilingual mode enabled - Primary: {self.config.get('speech.primary_language', 'ar-SA')}, Secondary: {self.config.get('speech.secondary_language', 'en-US')}")
        else:
            self.logger.info(f"🌍 Single language mode: {self.current_language}")
    
    def is_available(self) -> bool:
        """Check if speech recognition is available"""
        return SPEECH_RECOGNITION_AVAILABLE and self.is_initialized
    
    def listen_once(self, timeout: float = 5.0, phrase_time_limit: float = 10.0) -> Optional[str]:
        """Listen for speech once and return recognized text"""
        if not self.is_available():
            self.logger.warning("Speech recognition not available")
            return None
        
        try:
            self.logger.debug("Listening for speech...")
            
            with self.microphone as source:
                # Listen for audio
                audio = self.recognizer.listen(
                    source, 
                    timeout=timeout, 
                    phrase_time_limit=phrase_time_limit
                )
            
            if self.on_speech_detected:
                self.on_speech_detected()
            
            # Recognize speech
            text = self._recognize_audio(audio)
            
            if text and self.on_speech_recognized:
                self.on_speech_recognized(text)
            
            return text
            
        except sr.WaitTimeoutError:
            self.logger.debug("Listening timeout - no speech detected")
            return None
        except Exception as e:
            self.logger.error(f"Error during speech recognition: {e}")
            if self.on_error:
                self.on_error(str(e))
            return None
    
    def start_continuous_listening(self, callback: Callable[[str], None], wake_word: str = "gideon"):
        """Start continuous listening in background with wake word detection"""
        if not self.is_available() or self.is_listening:
            return False

        def listen_continuously():
            self.is_listening = True
            self.logger.info(f"Started continuous listening for wake word: '{wake_word}'")

            # Create a dedicated microphone instance for continuous listening
            try:
                continuous_mic = sr.Microphone()
                with continuous_mic as source:
                    self.recognizer.adjust_for_ambient_noise(source, duration=1)
                    self.logger.debug("Adjusted for ambient noise in continuous listening")
            except Exception as e:
                self.logger.error(f"Failed to initialize continuous listening microphone: {e}")
                self.is_listening = False
                return

            while self.is_listening:
                try:
                    # Use the dedicated microphone for continuous listening
                    with continuous_mic as source:
                        # Listen for audio with short timeout for responsiveness
                        try:
                            audio = self.recognizer.listen(source, timeout=1.0, phrase_time_limit=3.0)
                        except sr.WaitTimeoutError:
                            # Timeout is normal in continuous listening
                            continue

                    # Process the audio
                    text = self._recognize_audio(audio)
                    if text:
                        text_lower = text.lower().strip()
                        self.logger.info(f"🎤 Heard: '{text}'")

                        # Enhanced wake word detection with bilingual support
                        wake_word_found, detected_variant, command_text = self._detect_wake_word(text, text_lower, wake_word)

                        if wake_word_found:
                            self.wake_word_detections += 1
                            self.last_wake_word_time = time.time()

                            self.logger.info(f"🎯 Wake word '{detected_variant}' detected in '{text}'!")

                            # Notify wake word detection callback
                            if self.on_wake_word_detected:
                                self.on_wake_word_detected(detected_variant, text)

                            if command_text:
                                # There's a command after the wake word
                                self.logger.info(f"📝 Command detected: '{command_text}'")
                                if callback:
                                    callback(command_text)
                            else:
                                # Just the wake word, acknowledge and listen for command
                                self.logger.info("👂 Wake word only detected, listening for command...")
                                if self.on_speech_detected:
                                    self.on_speech_detected()

                                # Enhanced follow-up command listening
                                follow_up_command = self._listen_for_follow_up_command(continuous_mic)
                                if follow_up_command and callback:
                                    self.logger.info(f"✅ Follow-up command: '{follow_up_command}'")
                                    callback(follow_up_command)
                                elif not follow_up_command:
                                    self.logger.info("⏰ No follow-up command detected")

                        if not wake_word_found:
                            # Not a wake word, just log for debugging (less verbose)
                            self.logger.debug(f"🔇 Heard '{text}' but no wake word detected")

                except Exception as e:
                    if "timeout" not in str(e).lower() and "waitimeouterror" not in str(e).lower():
                        self.logger.error(f"Error during speech recognition: {e}")
                    # Short pause to prevent excessive CPU usage
                    time.sleep(0.1)

            self.logger.info("Stopped continuous listening")

        # Start listening thread
        listen_thread = threading.Thread(target=listen_continuously, daemon=True)
        listen_thread.start()

        return True

    def _detect_wake_word(self, original_text: str, text_lower: str, wake_word: str) -> tuple:
        """Enhanced wake word detection with bilingual support"""
        # Determine which language variants to use
        detected_language = self._detect_text_language(original_text)

        if detected_language == "ar" or self.bilingual_mode:
            variants_to_check = self.wake_word_variants["ar"] + self.wake_word_variants["en"]
        else:
            variants_to_check = self.wake_word_variants["en"]

        # Add the original wake word if not in variants
        if wake_word.lower() not in variants_to_check:
            variants_to_check.append(wake_word.lower())

        # Check for wake word variants with fuzzy matching
        for variant in variants_to_check:
            # Exact match
            if variant in text_lower:
                wake_word_index = text_lower.find(variant)
                command_text = original_text[wake_word_index + len(variant):].strip()
                return True, variant, command_text

            # Fuzzy match for common mispronunciations
            if self._fuzzy_match_wake_word(variant, text_lower):
                # Find the best position for command extraction
                command_text = self._extract_command_after_fuzzy_match(original_text, variant)
                return True, variant, command_text

        return False, None, None

    def _fuzzy_match_wake_word(self, variant: str, text_lower: str) -> bool:
        """Fuzzy matching for wake word variants"""
        # Simple fuzzy matching - check if most characters match
        words = text_lower.split()

        for word in words:
            if len(word) >= 4:  # Only check words of reasonable length
                # Calculate similarity
                similarity = self._calculate_similarity(variant, word)
                if similarity >= self.wake_word_sensitivity:
                    return True

        return False

    def _calculate_similarity(self, word1: str, word2: str) -> float:
        """Calculate similarity between two words"""
        if not word1 or not word2:
            return 0.0

        # Simple character-based similarity
        longer = word1 if len(word1) > len(word2) else word2
        shorter = word2 if len(word1) > len(word2) else word1

        if len(longer) == 0:
            return 1.0

        matches = sum(1 for i, char in enumerate(shorter) if i < len(longer) and char == longer[i])
        return matches / len(longer)

    def _extract_command_after_fuzzy_match(self, original_text: str, variant: str) -> str:
        """Extract command text after fuzzy wake word match"""
        words = original_text.split()

        # Find the word that best matches the variant
        best_match_index = -1
        best_similarity = 0

        for i, word in enumerate(words):
            similarity = self._calculate_similarity(variant, word.lower())
            if similarity > best_similarity:
                best_similarity = similarity
                best_match_index = i

        if best_match_index >= 0 and best_match_index < len(words) - 1:
            # Return everything after the matched word
            return " ".join(words[best_match_index + 1:])

        return ""

    def _detect_text_language(self, text: str) -> str:
        """Simple language detection based on character analysis"""
        if not text:
            return "en"

        # Count Arabic characters
        arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
        total_chars = len([char for char in text if char.isalpha()])

        if total_chars == 0:
            return "en"

        arabic_ratio = arabic_chars / total_chars

        # If more than 30% Arabic characters, consider it Arabic
        return "ar" if arabic_ratio > 0.3 else "en"

    def _listen_for_follow_up_command(self, microphone) -> Optional[str]:
        """Enhanced follow-up command listening with better timeout handling"""
        try:
            self.logger.info("🔊 Listening for your command...")

            # Use adaptive timeout based on previous interactions
            timeout = min(self.wake_word_timeout, 10.0)  # Max 10 seconds
            phrase_limit = 20.0  # Allow longer phrases

            with microphone as source:
                # Brief adjustment for current conditions
                self.recognizer.adjust_for_ambient_noise(source, duration=0.5)

                # Listen for command
                command_audio = self.recognizer.listen(
                    source,
                    timeout=timeout,
                    phrase_time_limit=phrase_limit
                )

            # Recognize the command
            command = self._recognize_audio(command_audio)
            return command

        except sr.WaitTimeoutError:
            self.logger.info("⏰ No follow-up command detected (timeout)")
            return None
        except Exception as e:
            self.logger.error(f"Error listening for follow-up command: {e}")
            return None

    def stop_listening(self):
        """Stop continuous listening"""
        self.is_listening = False
    
    def _recognize_audio(self, audio) -> Optional[str]:
        """Enhanced audio recognition with bilingual support and language detection"""
        self.recognition_attempts += 1

        # Try multiple languages if in bilingual mode
        if self.bilingual_mode and self.current_language == "auto":
            return self._recognize_audio_multilingual(audio)
        else:
            return self._recognize_audio_single_language(audio)

    def _recognize_audio_multilingual(self, audio) -> Optional[str]:
        """Try recognition in multiple languages"""
        primary_lang = self.config.get("speech.primary_language", "ar-SA")
        secondary_lang = self.config.get("speech.secondary_language", "en-US")
        languages_to_try = [primary_lang, secondary_lang]  # Try primary language first

        best_result = None
        best_confidence = 0
        detected_language = "en"

        for lang_code in languages_to_try:
            try:
                # Try Google recognition with specific language
                text = self.recognizer.recognize_google(audio, language=lang_code)
                if text and text.strip():
                    # Simple confidence estimation based on text characteristics
                    confidence = self._estimate_language_confidence(text, lang_code)

                    self.logger.debug(f"Recognized in {lang_code}: '{text}' (confidence: {confidence:.2f})")

                    if confidence > best_confidence:
                        best_result = text.strip()
                        best_confidence = confidence
                        detected_language = "ar" if lang_code.startswith("ar") else "en"

            except sr.UnknownValueError:
                self.logger.debug(f"Could not understand audio in {lang_code}")
                continue
            except sr.RequestError as e:
                self.logger.warning(f"Recognition service error for {lang_code}: {e}")
                continue
            except Exception as e:
                self.logger.error(f"Unexpected recognition error for {lang_code}: {e}")
                continue

        if best_result:
            self.successful_recognitions += 1
            self.language_detection_stats[detected_language] += 1

            # Notify language detection callback
            if self.on_language_detected:
                self.on_language_detected(detected_language, best_confidence)

            self.logger.info(f"🌍 Detected language: {detected_language} | Text: '{best_result}' | Confidence: {best_confidence:.2f}")
            return best_result

        # Fallback to offline recognition
        return self._try_offline_recognition(audio)

    def _recognize_audio_single_language(self, audio) -> Optional[str]:
        """Recognize audio in a single specified language"""
        try:
            lang_code = self._get_language_code()
            text = self.recognizer.recognize_google(audio, language=lang_code)

            if text and text.strip():
                self.successful_recognitions += 1
                result = text.strip()
                self.logger.debug(f"Recognized in {lang_code}: '{result}'")
                return result

        except sr.UnknownValueError:
            self.logger.debug("Could not understand audio")
        except sr.RequestError as e:
            self.logger.warning(f"Recognition service error: {e}")
        except Exception as e:
            self.logger.error(f"Unexpected recognition error: {e}")

        # Fallback to offline recognition
        return self._try_offline_recognition(audio)

    def _try_offline_recognition(self, audio) -> Optional[str]:
        """Try offline recognition as fallback - disabled to avoid PocketSphinx dependency"""
        # PocketSphinx offline recognition disabled to avoid dependency issues
        # Google Speech Recognition is more reliable for bilingual support
        self.logger.debug("Offline recognition disabled - using online recognition only")
        return None

    def _estimate_language_confidence(self, text: str, lang_code: str) -> float:
        """Estimate confidence of language detection based on text characteristics"""
        if not text:
            return 0.0

        # Basic heuristics for language detection confidence
        if lang_code.startswith("ar"):
            # Arabic text characteristics
            arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
            arabic_ratio = arabic_chars / len(text) if text else 0

            # Higher confidence for more Arabic characters
            confidence = min(0.9, 0.3 + (arabic_ratio * 0.6))

            # Boost confidence for common Arabic words
            arabic_words = ["مرحبا", "اسم", "كيف", "حال", "شكرا", "نعم", "لا", "أنا", "أنت"]
            for word in arabic_words:
                if word in text:
                    confidence += 0.1

        else:
            # English text characteristics
            english_chars = sum(1 for char in text if char.isascii() and char.isalpha())
            english_ratio = english_chars / len(text) if text else 0

            # Higher confidence for more English characters
            confidence = min(0.9, 0.3 + (english_ratio * 0.6))

            # Boost confidence for common English words
            english_words = ["hello", "hi", "name", "how", "are", "you", "thank", "yes", "no", "what"]
            text_lower = text.lower()
            for word in english_words:
                if word in text_lower:
                    confidence += 0.1

        return min(confidence, 1.0)
    
    def _get_language_code(self) -> str:
        """Get language code for recognition"""
        app_language = self.config.get("app.language", "ar")
        speech_language = self.config.get("speech.language", "auto")
        primary_language = self.config.get("speech.primary_language", "ar-SA")
        
        # Map common language codes
        language_map = {
            "en": "en-US",
            "ar": "ar-SA",
            "en-US": "en-US",
            "ar-SA": "ar-SA",
            "ar-EG": "ar-EG",  # Egyptian Arabic
            "ar-AE": "ar-AE",  # UAE Arabic
            "ar-JO": "ar-JO",  # Jordanian Arabic
            "ar-LB": "ar-LB",  # Lebanese Arabic
            "ar-MA": "ar-MA",  # Moroccan Arabic
        }

        # If speech language is auto or not set, use primary language
        if speech_language == "auto" or not speech_language:
            return language_map.get(primary_language, "ar-SA")
        
        # Use app language if speech language matches app setting
        if app_language == "ar":
            return language_map.get(primary_language, "ar-SA")

        return language_map.get(speech_language, language_map.get(app_language, "ar-SA"))
    
    def test_microphone(self) -> bool:
        """Test microphone functionality"""
        if not self.is_available():
            return False
        
        try:
            with self.microphone as source:
                self.logger.info("Testing microphone... Say something!")
                audio = self.recognizer.listen(source, timeout=3, phrase_time_limit=3)
                
            text = self._recognize_audio(audio)
            if text:
                self.logger.info(f"Microphone test successful. Heard: {text}")
                return True
            else:
                self.logger.warning("Microphone test: No speech recognized")
                return False
                
        except Exception as e:
            self.logger.error(f"Microphone test failed: {e}")
            return False
    
    def get_microphone_list(self) -> list:
        """Get list of available microphones"""
        if not SPEECH_RECOGNITION_AVAILABLE:
            return []
        
        try:
            return sr.Microphone.list_microphone_names()
        except Exception as e:
            self.logger.error(f"Error getting microphone list: {e}")
            return []
    
    def set_microphone(self, device_index: int) -> bool:
        """Set microphone device"""
        try:
            self.microphone = sr.Microphone(device_index=device_index)
            
            # Re-adjust for ambient noise
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
            
            self.logger.info(f"Microphone set to device index: {device_index}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting microphone: {e}")
            return False
    
    # Callback setters
    def set_speech_detected_callback(self, callback: Callable):
        """Set callback for when speech is detected"""
        self.on_speech_detected = callback
    
    def set_speech_recognized_callback(self, callback: Callable[[str], None]):
        """Set callback for when speech is recognized"""
        self.on_speech_recognized = callback
    
    def set_error_callback(self, callback: Callable[[str], None]):
        """Set callback for errors"""
        self.on_error = callback

    def set_language_detected_callback(self, callback: Callable[[str, float], None]):
        """Set callback for language detection"""
        self.on_language_detected = callback

    def set_wake_word_detected_callback(self, callback: Callable[[str, str], None]):
        """Set callback for wake word detection"""
        self.on_wake_word_detected = callback

    def get_performance_stats(self) -> dict:
        """Get performance statistics"""
        success_rate = (self.successful_recognitions / max(self.recognition_attempts, 1)) * 100

        return {
            "recognition_attempts": self.recognition_attempts,
            "successful_recognitions": self.successful_recognitions,
            "success_rate": round(success_rate, 2),
            "wake_word_detections": self.wake_word_detections,
            "language_detection_stats": self.language_detection_stats.copy(),
            "current_language": self.current_language,
            "bilingual_mode": self.bilingual_mode,
            "energy_threshold": self.energy_threshold,
            "is_listening": self.is_listening
        }

    def reset_performance_stats(self):
        """Reset performance statistics"""
        self.recognition_attempts = 0
        self.successful_recognitions = 0
        self.wake_word_detections = 0
        self.language_detection_stats = {"en": 0, "ar": 0}
        self.logger.info("🔄 Performance statistics reset")

    def set_language_mode(self, language: str):
        """Set language mode (en, ar, auto)"""
        if language in ["en", "ar", "auto"]:
            self.current_language = language
            self.bilingual_mode = (language == "auto")
            self.logger.info(f"🌍 Language mode set to: {language}")
            return True
        else:
            self.logger.warning(f"Unsupported language mode: {language}")
            return False

    def adjust_wake_word_sensitivity(self, sensitivity: float):
        """Adjust wake word detection sensitivity (0.0 to 1.0)"""
        if 0.0 <= sensitivity <= 1.0:
            self.wake_word_sensitivity = sensitivity
            self.logger.info(f"🎯 Wake word sensitivity adjusted to: {sensitivity}")
            return True
        else:
            self.logger.warning(f"Invalid sensitivity value: {sensitivity}")
            return False

    def get_supported_languages(self) -> list:
        """Get list of supported languages"""
        return self.supported_languages.copy()

    def is_bilingual_mode(self) -> bool:
        """Check if bilingual mode is enabled"""
        return self.bilingual_mode


# Fallback STT Engine for when speech_recognition is not available
class FallbackSTTEngine:
    """Fallback STT Engine when speech recognition is not available"""
    
    def __init__(self):
        self.logger = GideonLogger("FallbackSTTEngine")
        self.logger.warning("Speech recognition not available - using fallback")
    
    def is_available(self) -> bool:
        return False
    
    def listen_once(self, timeout: float = 5.0, phrase_time_limit: float = 10.0) -> Optional[str]:
        self.logger.warning("Speech recognition not available")
        return None
    
    def start_continuous_listening(self, callback: Callable[[str], None]) -> bool:
        self.logger.warning("Speech recognition not available")
        return False
    
    def stop_listening(self):
        pass
    
    def test_microphone(self) -> bool:
        return False
    
    def get_microphone_list(self) -> list:
        return []
    
    def set_microphone(self, device_index: int) -> bool:
        return False
    
    def set_speech_detected_callback(self, callback: Callable):
        pass
    
    def set_speech_recognized_callback(self, callback: Callable[[str], None]):
        pass
    
    def set_error_callback(self, callback: Callable[[str], None]):
        pass


# Use fallback if speech recognition is not available
if not SPEECH_RECOGNITION_AVAILABLE:
    STTEngine = FallbackSTTEngine
