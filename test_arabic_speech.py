#!/usr/bin/env python3
"""
Test script for Arabic speech recognition
"""

import sys
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

from src.speech.stt_engine import STTEngine
from src.utils.config import Config

def test_arabic_speech():
    """Test Arabic speech recognition"""
    print("🎤 Testing Arabic Speech Recognition")
    print("=" * 40)
    
    # Initialize STT engine
    stt = STTEngine()
    
    if not stt.is_available():
        print("❌ Speech recognition not available")
        return
    
    print("✅ Speech recognition initialized")
    print(f"🌍 Bilingual mode: {stt.bilingual_mode}")
    print(f"🎯 Current language: {stt.current_language}")
    print(f"🔧 Supported languages: {stt.supported_languages}")
    
    # Test configuration
    config = Config()
    print(f"\n📋 Configuration:")
    print(f"   App language: {config.get('app.language')}")
    print(f"   Speech language: {config.get('speech.language')}")
    print(f"   Primary language: {config.get('speech.primary_language')}")
    print(f"   Secondary language: {config.get('speech.secondary_language')}")
    print(f"   Bilingual mode: {config.get('speech.bilingual_mode')}")
    
    # Test single recognition
    print(f"\n🎤 Say something in Arabic or English (you have 10 seconds)...")
    result = stt.listen_once(timeout=10.0, phrase_time_limit=15.0)
    
    if result:
        print(f"✅ Recognized: '{result}'")
        
        # Detect language
        detected_lang = stt._detect_text_language(result)
        print(f"🌍 Detected language: {detected_lang}")
        
        # Test wake word detection
        wake_found, variant, command = stt._detect_wake_word(result, result.lower(), "gideon")
        if wake_found:
            print(f"🎯 Wake word detected: '{variant}'")
            if command:
                print(f"📝 Command: '{command}'")
        else:
            print("🔇 No wake word detected")
    else:
        print("❌ No speech recognized")
    
    print("\n🧪 Test completed")

if __name__ == "__main__":
    test_arabic_speech()