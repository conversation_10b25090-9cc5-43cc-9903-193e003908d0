"""
Main Window for Gideon AI Assistant
Dark-themed GUI inspired by "The Flash" Gideon AI aesthetic
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
from datetime import datetime
from typing import Optional

try:
    import customtkinter as ctk
    CUSTOMTKINTER_AVAILABLE = True
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False

from src.utils.logger import <PERSON><PERSON><PERSON><PERSON>
from src.utils.config import Config
from src.utils.i18n import get_i18n
from src.utils.text_direction import text_direction_manager


class GideonMainWindow:
    """Main window for Gideon AI Assistant"""
    
    def __init__(self, gideon_core):
        self.logger = GideonLogger("MainWindow")
        self.config = Config()
        self.i18n = get_i18n()
        self.gideon_core = gideon_core
        
        # Window setup
        self.root = None
        self.is_running = False
        
        # UI Components
        self.chat_display = None
        self.input_entry = None
        self.status_label = None
        self.voice_button = None
        self.send_button = None
        
        # State
        self.is_voice_active = False
        self.current_status = "ready"
        
        # Setup callbacks
        if self.gideon_core:
            self.gideon_core.set_response_ready_callback(self._on_response_ready)
            self.gideon_core.set_status_change_callback(self._on_status_change)
            self.gideon_core.set_error_callback(self._on_error)
    
    def create_window(self):
        """Create the main window"""
        try:
            if CUSTOMTKINTER_AVAILABLE:
                self.root = ctk.CTk()
                self._setup_customtkinter_window()
            else:
                self.root = tk.Tk()
                self._setup_tkinter_window()
            
            self._create_widgets()
            self._setup_layout()
            self._setup_bindings()
            
            self.logger.info("Main window created successfully")
            
        except Exception as e:
            self.logger.error(f"Error creating main window: {e}")
            raise
    
    def _setup_customtkinter_window(self):
        """Setup CustomTkinter window"""
        # Set appearance
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # Window configuration
        self.root.title(self.config.app_name)
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Gideon-inspired colors
        self.colors = {
            'bg_primary': '#0a0a0a',      # Very dark background
            'bg_secondary': '#1a1a1a',    # Slightly lighter background
            'accent_blue': '#00d4ff',     # Gideon blue
            'accent_gold': '#ffd700',     # Gold accent
            'text_primary': '#ffffff',    # White text
            'text_secondary': '#cccccc',  # Light gray text
            'success': '#00ff88',         # Success green
            'warning': '#ffaa00',         # Warning orange
            'error': '#ff4444'            # Error red
        }
    
    def _setup_tkinter_window(self):
        """Setup standard Tkinter window"""
        self.root.title(self.config.app_name)
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Dark theme colors for standard Tkinter
        self.colors = {
            'bg_primary': '#2b2b2b',
            'bg_secondary': '#3b3b3b',
            'accent_blue': '#4a9eff',
            'accent_gold': '#ffd700',
            'text_primary': '#ffffff',
            'text_secondary': '#cccccc',
            'success': '#4caf50',
            'warning': '#ff9800',
            'error': '#f44336'
        }
        
        self.root.configure(bg=self.colors['bg_primary'])
    
    def _create_widgets(self):
        """Create UI widgets"""
        if CUSTOMTKINTER_AVAILABLE:
            self._create_customtkinter_widgets()
        else:
            self._create_tkinter_widgets()
    
    def _create_customtkinter_widgets(self):
        """Create CustomTkinter widgets"""
        # Main container
        self.main_frame = ctk.CTkFrame(self.root)
        
        # Header
        self.header_frame = ctk.CTkFrame(self.main_frame)
        self.title_label = ctk.CTkLabel(
            self.header_frame,
            text="🤖 GIDEON AI ASSISTANT",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=self.colors['accent_blue']
        )
        
        self.status_label = ctk.CTkLabel(
            self.header_frame,
            text="Ready",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['success']
        )
        
        # Chat area
        self.chat_frame = ctk.CTkFrame(self.main_frame)
        self.chat_display = ctk.CTkTextbox(
            self.chat_frame,
            height=400,
            font=ctk.CTkFont(size=12),
            wrap="word"
        )
        
        # Input area
        self.input_frame = ctk.CTkFrame(self.main_frame)
        
        self.input_entry = ctk.CTkEntry(
            self.input_frame,
            placeholder_text="Type your message or use voice input...",
            font=ctk.CTkFont(size=12),
            height=40
        )
        
        self.voice_button = ctk.CTkButton(
            self.input_frame,
            text="🎤",
            width=50,
            height=40,
            font=ctk.CTkFont(size=16),
            command=self._toggle_voice_input,
            fg_color=self.colors['accent_blue']
        )
        
        self.send_button = ctk.CTkButton(
            self.input_frame,
            text="Send",
            width=80,
            height=40,
            command=self._send_message,
            fg_color=self.colors['accent_gold']
        )
        
        # Control buttons
        self.controls_frame = ctk.CTkFrame(self.main_frame)
        
        self.screenshot_button = ctk.CTkButton(
            self.controls_frame,
            text="📸 Screenshot",
            command=self._take_screenshot
        )
        
        self.clear_button = ctk.CTkButton(
            self.controls_frame,
            text="🗑️ Clear",
            command=self._clear_chat
        )
        
        self.settings_button = ctk.CTkButton(
            self.controls_frame,
            text="⚙️ Settings",
            command=self._open_settings
        )

        self.models_button = ctk.CTkButton(
            self.controls_frame,
            text="🤖 Models",
            command=self._open_model_manager
        )

        self.test_speech_button = ctk.CTkButton(
            self.controls_frame,
            text="🎤 Test Speech",
            command=self._test_speech
        )

        self.language_button = ctk.CTkButton(
            self.controls_frame,
            text="🌍 عربي/EN",
            command=self._toggle_language
        )
    
    def _create_tkinter_widgets(self):
        """Create standard Tkinter widgets"""
        # Main container
        self.main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        
        # Header
        self.header_frame = tk.Frame(self.main_frame, bg=self.colors['bg_secondary'])
        self.title_label = tk.Label(
            self.header_frame,
            text="🤖 GIDEON AI ASSISTANT",
            font=("Arial", 18, "bold"),
            fg=self.colors['accent_blue'],
            bg=self.colors['bg_secondary']
        )
        
        self.status_label = tk.Label(
            self.header_frame,
            text="Ready",
            font=("Arial", 10),
            fg=self.colors['success'],
            bg=self.colors['bg_secondary']
        )
        
        # Chat area
        self.chat_frame = tk.Frame(self.main_frame, bg=self.colors['bg_primary'])
        self.chat_display = scrolledtext.ScrolledText(
            self.chat_frame,
            height=20,
            font=("Consolas", 10),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            insertbackground=self.colors['text_primary'],
            wrap=tk.WORD
        )
        
        # Input area
        self.input_frame = tk.Frame(self.main_frame, bg=self.colors['bg_primary'])
        
        self.input_entry = tk.Entry(
            self.input_frame,
            font=("Arial", 12),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            insertbackground=self.colors['text_primary']
        )
        
        self.voice_button = tk.Button(
            self.input_frame,
            text="🎤",
            font=("Arial", 12),
            bg=self.colors['accent_blue'],
            fg=self.colors['text_primary'],
            command=self._toggle_voice_input,
            width=3
        )
        
        self.send_button = tk.Button(
            self.input_frame,
            text="Send",
            font=("Arial", 10),
            bg=self.colors['accent_gold'],
            fg=self.colors['bg_primary'],
            command=self._send_message,
            width=8
        )
        
        # Control buttons
        self.controls_frame = tk.Frame(self.main_frame, bg=self.colors['bg_primary'])
        
        self.screenshot_button = tk.Button(
            self.controls_frame,
            text="📸 Screenshot",
            command=self._take_screenshot,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        )
        
        self.clear_button = tk.Button(
            self.controls_frame,
            text="🗑️ Clear",
            command=self._clear_chat,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        )
        
        self.settings_button = tk.Button(
            self.controls_frame,
            text="⚙️ Settings",
            command=self._open_settings,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        )

        self.models_button = tk.Button(
            self.controls_frame,
            text="🤖 Models",
            command=self._open_model_manager,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        )

        self.test_speech_button = tk.Button(
            self.controls_frame,
            text="🎤 Test Speech",
            command=self._test_speech,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        )

        self.language_button = tk.Button(
            self.controls_frame,
            text="🌍 عربي/EN",
            command=self._toggle_language,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        )
    
    def _setup_layout(self):
        """Setup widget layout"""
        # Main frame
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Header
        self.header_frame.pack(fill=tk.X, pady=(0, 10))
        self.title_label.pack(side=tk.LEFT)
        self.status_label.pack(side=tk.RIGHT)
        
        # Chat area
        self.chat_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        self.chat_display.pack(fill=tk.BOTH, expand=True)
        
        # Input area
        self.input_frame.pack(fill=tk.X, pady=(0, 10))
        self.input_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        self.voice_button.pack(side=tk.RIGHT, padx=(0, 5))
        self.send_button.pack(side=tk.RIGHT)
        
        # Controls
        self.controls_frame.pack(fill=tk.X)
        self.screenshot_button.pack(side=tk.LEFT, padx=(0, 5))
        self.clear_button.pack(side=tk.LEFT, padx=(0, 5))
        self.settings_button.pack(side=tk.LEFT, padx=(0, 5))
        self.models_button.pack(side=tk.LEFT, padx=(0, 5))
        self.test_speech_button.pack(side=tk.LEFT, padx=(0, 5))
        self.language_button.pack(side=tk.LEFT)
    
    def _setup_bindings(self):
        """Setup event bindings"""
        # Enter key to send message
        self.input_entry.bind('<Return>', lambda e: self._send_message())
        
        # Window close event
        self.root.protocol("WM_DELETE_WINDOW", self._on_window_close)
        
        # Focus on input entry
        self.input_entry.focus_set()
    
    def _send_message(self):
        """Send text message"""
        message = self.input_entry.get().strip()
        if not message:
            return
        
        # Clear input
        self.input_entry.delete(0, tk.END)
        
        # Display user message
        self._add_message("You", message, self.colors['accent_blue'])
        
        # Process with Gideon
        if self.gideon_core:
            self.gideon_core.process_text_input(message, self._on_text_response)
        else:
            self._add_message("Gideon", "Core system not available", self.colors['error'])
    
    def _toggle_voice_input(self):
        """Toggle voice input"""
        if not self.gideon_core:
            self._add_message("System", "Voice input not available", self.colors['error'])
            return
        
        if self.is_voice_active:
            # Stop voice input
            self.is_voice_active = False
            self._update_voice_button()
        else:
            # Start voice input
            self.is_voice_active = True
            self._update_voice_button()
            self.gideon_core.process_voice_input(self._on_voice_response)
    
    def _update_voice_button(self):
        """Update voice button appearance"""
        if self.is_voice_active:
            if CUSTOMTKINTER_AVAILABLE:
                self.voice_button.configure(text="🔴", fg_color=self.colors['error'])
            else:
                self.voice_button.configure(text="🔴", bg=self.colors['error'])
        else:
            if CUSTOMTKINTER_AVAILABLE:
                self.voice_button.configure(text="🎤", fg_color=self.colors['accent_blue'])
            else:
                self.voice_button.configure(text="🎤", bg=self.colors['accent_blue'])
    
    def _take_screenshot(self):
        """Take screenshot"""
        if self.gideon_core:
            filepath = self.gideon_core.take_screenshot()
            if filepath:
                self._add_message("System", f"Screenshot saved: {filepath}", self.colors['success'])
            else:
                self._add_message("System", "Failed to take screenshot", self.colors['error'])
        else:
            self._add_message("System", "Screenshot functionality not available", self.colors['error'])
    
    def _clear_chat(self):
        """Clear chat display"""
        if CUSTOMTKINTER_AVAILABLE:
            self.chat_display.delete("0.0", tk.END)
        else:
            self.chat_display.delete("1.0", tk.END)
        
        self._add_message("System", "Chat cleared", self.colors['text_secondary'])
    
    def _open_settings(self):
        """Open settings window"""
        messagebox.showinfo("Settings", "Settings window would open here")

    def _open_model_manager(self):
        """Open AI model selector dialog"""
        try:
            from src.ui.model_selector import ModelSelectorDialog

            def on_model_changed(backend, model):
                self._add_message("System", f"🧠 AI Model changed: {model} via {backend}", self.colors['success'])
                if self.gideon_core and self.gideon_core.tts_engine:
                    self.gideon_core.tts_engine.speak(f"AI model loaded: {model}")

            if self.gideon_core and hasattr(self.gideon_core, 'ai_engine'):
                dialog = ModelSelectorDialog(
                    self.root,
                    self.gideon_core.ai_engine,
                    on_model_changed
                )
                result = dialog.show()

                if result:
                    self.logger.info(f"Model configuration updated: {result}")
                    self._add_message("System", "✅ AI model configuration updated", self.colors['success'])
            else:
                self._add_message("System", "❌ AI engine not available", self.colors['error'])

        except ImportError as e:
            self.logger.error(f"Failed to import model selector: {e}")
            self._add_message("System", "❌ Model selector not available", self.colors['error'])
        except Exception as e:
            self.logger.error(f"Error opening model selector: {e}")
            self._add_message("System", f"❌ Model selector error: {e}", self.colors['error'])

    def _test_speech(self):
        """Test speech functionality"""
        try:
            if not self.gideon_core:
                messagebox.showerror("Error", "Gideon core not available")
                return

            # Test TTS first
            self._add_message("System", "Testing text-to-speech...", self.colors['warning'])
            if self.gideon_core.tts_engine and self.gideon_core.tts_engine.is_available():
                self.gideon_core.tts_engine.speak("Hello! This is a text to speech test. Can you hear me clearly?")
                self._add_message("System", "✅ TTS test completed", self.colors['success'])
            else:
                self._add_message("System", "❌ TTS not available", self.colors['error'])

            # Test STT
            self._add_message("System", "Testing speech recognition...", self.colors['warning'])
            if self.gideon_core.stt_engine and self.gideon_core.stt_engine.is_available():
                self._add_message("System", "🎤 Say 'Hello Gideon' now...", self.colors['accent_blue'])

                def test_callback(user_input, ai_response):
                    if user_input:
                        self._add_message("System", f"✅ Heard: '{user_input}'", self.colors['success'])
                        if "gideon" in user_input.lower():
                            self._add_message("System", "🎯 Wake word detected successfully!", self.colors['success'])
                        else:
                            self._add_message("System", "⚠️ Wake word not detected, but speech recognition works", self.colors['warning'])
                    else:
                        self._add_message("System", "❌ No speech detected", self.colors['error'])

                # Start voice input test
                self.gideon_core.process_voice_input(test_callback)
            else:
                self._add_message("System", "❌ Speech recognition not available", self.colors['error'])

        except Exception as e:
            self.logger.error(f"Error testing speech: {e}")
            self._add_message("System", f"❌ Speech test error: {e}", self.colors['error'])

    def _toggle_language(self):
        """Toggle between English and Arabic"""
        try:
            current_lang = self.config.get("app.language", "en")

            if current_lang == "en":
                # Switch to Arabic
                self.config.set("app.language", "ar")
                self.i18n.set_language("ar")

                # CRITICAL FIX: Set AI engine response language to Arabic
                if self.gideon_core and self.gideon_core.ai_engine:
                    self.gideon_core.ai_engine.set_response_language("ar")
                    self.logger.info("🌍 AI engine response language set to Arabic")

                # Update UI text direction if needed
                self._add_message("System", "تم تغيير اللغة إلى العربية 🇸🇦", self.colors['success'])

                # Test Arabic TTS
                if self.gideon_core and self.gideon_core.tts_engine:
                    self.gideon_core.tts_engine.speak("مرحباً! تم تغيير اللغة إلى العربية")

                # Update button text
                if CUSTOMTKINTER_AVAILABLE:
                    self.language_button.configure(text="🌍 English")
                else:
                    self.language_button.configure(text="🌍 English")

            else:
                # Switch to English
                self.config.set("app.language", "en")
                self.i18n.set_language("en")

                # CRITICAL FIX: Set AI engine response language to English
                if self.gideon_core and self.gideon_core.ai_engine:
                    self.gideon_core.ai_engine.set_response_language("en")
                    self.logger.info("🌍 AI engine response language set to English")

                self._add_message("System", "Language changed to English 🇺🇸", self.colors['success'])

                # Test English TTS
                if self.gideon_core and self.gideon_core.tts_engine:
                    self.gideon_core.tts_engine.speak("Language changed to English. Hello!")

                # Update button text
                if CUSTOMTKINTER_AVAILABLE:
                    self.language_button.configure(text="🌍 عربي/AR")
                else:
                    self.language_button.configure(text="🌍 عربي/AR")

            # Reinitialize TTS with new language
            if self.gideon_core and self.gideon_core.tts_engine:
                self.gideon_core.tts_engine._set_voice()

        except Exception as e:
            self.logger.error(f"Error toggling language: {e}")
            self._add_message("System", f"❌ Language toggle error: {e}", self.colors['error'])
    
    def _add_message(self, sender: str, message: str, color: str = None):
        """Add message to chat display with text direction support"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        if color is None:
            color = self.colors['text_primary']

        # Detect text direction and format accordingly
        formatted_message, direction = text_direction_manager.format_text_for_display(message)

        # Create directional message format
        if direction == "rtl":
            # RTL format: message - sender - timestamp (right-aligned)
            display_message = f"{formatted_message} :{sender} [{timestamp}]\n"
            tag_name = f"rtl_{sender.lower()}"
            justify = 'right'
        else:
            # LTR format: timestamp - sender - message (left-aligned)
            display_message = f"[{timestamp}] {sender}: {formatted_message}\n"
            tag_name = f"ltr_{sender.lower()}"
            justify = 'left'

        # Configure text tags for direction if widget supports it
        if hasattr(self.chat_display, 'tag_configure'):
            self.chat_display.tag_configure(
                tag_name,
                justify=justify,
                foreground=color,
                lmargin1=20 if direction == "ltr" else 0,
                rmargin=20 if direction == "rtl" else 0
            )

            # Insert with tag
            start_pos = self.chat_display.index(tk.END)
            self.chat_display.insert(tk.END, display_message)
            end_pos = self.chat_display.index(tk.END)
            self.chat_display.tag_add(tag_name, start_pos, end_pos)
        else:
            # Fallback for widgets without tag support
            if CUSTOMTKINTER_AVAILABLE:
                self.chat_display.insert(tk.END, display_message)
            else:
                self.chat_display.insert(tk.END, display_message)

        # Scroll to end
        if hasattr(self.chat_display, 'see'):
            self.chat_display.see(tk.END)
    
    def _on_text_response(self, user_input: str, ai_response: str):
        """Handle text response from Gideon"""
        self._add_message("Gideon", ai_response, self.colors['accent_gold'])
        
        # Speak response if TTS is enabled
        if self.gideon_core and self.config.get("speech.tts_enabled", True):
            self.gideon_core.speak_text(ai_response)
    
    def _on_voice_response(self, user_input: str, ai_response: str):
        """Handle voice response from Gideon"""
        if user_input:
            self._add_message("You (Voice)", user_input, self.colors['accent_blue'])
        
        if ai_response:
            self._add_message("Gideon", ai_response, self.colors['accent_gold'])
            
            # Speak response
            if self.gideon_core:
                self.gideon_core.speak_text(ai_response)
        
        # Reset voice button
        self.is_voice_active = False
        self._update_voice_button()
    
    def _on_response_ready(self, user_input: str, ai_response: str):
        """Handle response ready callback"""
        # This is handled by the specific response callbacks
        pass
    
    def _on_status_change(self, status: str):
        """Handle status change"""
        self.current_status = status
        
        status_text = {
            "ready": "Ready",
            "listening": "Listening...",
            "processing": "Processing...",
            "speaking": "Speaking..."
        }.get(status, status)
        
        status_color = {
            "ready": self.colors['success'],
            "listening": self.colors['accent_blue'],
            "processing": self.colors['warning'],
            "speaking": self.colors['accent_gold']
        }.get(status, self.colors['text_secondary'])
        
        if CUSTOMTKINTER_AVAILABLE:
            self.status_label.configure(text=status_text, text_color=status_color)
        else:
            self.status_label.configure(text=status_text, fg=status_color)
    
    def _on_error(self, error_message: str):
        """Handle error callback"""
        self._add_message("Error", error_message, self.colors['error'])
    
    def _on_window_close(self):
        """Handle window close event"""
        self.is_running = False
        if self.gideon_core:
            self.gideon_core.shutdown()
        self.root.destroy()
    
    def run(self):
        """Run the main window"""
        try:
            self.create_window()
            self.is_running = True
            
            # Add welcome message
            self._add_message("Gideon", self.i18n.get_text("messages.hello", "Hello! I'm Gideon, your AI assistant. How can I help you today?"), self.colors['accent_gold'])
            
            # Start the main loop
            self.root.mainloop()
            
        except Exception as e:
            self.logger.error(f"Error running main window: {e}")
            raise
