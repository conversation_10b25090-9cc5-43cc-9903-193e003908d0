"""
Kivy-based Main Window for Gideon AI Assistant
Modern, cross-platform UI with Material Design
"""

import os
import sys
from datetime import datetime
from typing import Optional

# Kivy configuration - must be set before importing kivy
os.environ['KIVY_WINDOW'] = 'sdl2'
os.environ['KIVY_GL_BACKEND'] = 'angle_sdl2'  # Use ANGLE for better Windows compatibility
os.environ['KIVY_GRAPHICS'] = 'opengl'

try:
    import kivy
    kivy.require('2.1.0')
    
    from kivy.app import App
    from kivy.uix.boxlayout import BoxLayout
    from kivy.uix.gridlayout import GridLayout
    from kivy.uix.button import Button
    from kivy.uix.label import Label
    from kivy.uix.textinput import TextInput
    from kivy.uix.scrollview import ScrollView
    from kivy.uix.popup import Popup
    from kivy.clock import Clock
    from kivy.core.window import Window
    from kivy.graphics import Color, Rectangle
    from kivy.metrics import dp
    
    # Try to import KivyMD for Material Design
    try:
        from kivymd.app import MDApp
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFloatingActionButton
        from kivymd.uix.label import MDLabel
        from kivymd.uix.textfield import MDTextField
        from kivymd.uix.card import MDCard
        from kivymd.uix.toolbar import MDTopAppBar
        from kivymd.uix.scrollview import MDScrollView
        from kivymd.theming import ThemableBehavior
        KIVYMD_AVAILABLE = True
    except ImportError:
        KIVYMD_AVAILABLE = False
    
    KIVY_AVAILABLE = True
except ImportError:
    KIVY_AVAILABLE = False
    KIVYMD_AVAILABLE = False

from src.utils.logger import GideonLogger
from src.utils.config import Config
from src.utils.i18n import get_i18n


class ChatDisplay(ScrollView):
    """Chat display widget for conversation history"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.logger = GideonLogger("ChatDisplay")
        
        # Create main layout
        self.layout = BoxLayout(orientation='vertical', spacing=dp(5), size_hint_y=None)
        self.layout.bind(minimum_height=self.layout.setter('height'))
        
        self.add_widget(self.layout)
        
        # Set dark theme colors
        self.colors = {
            'bg_primary': (0.04, 0.04, 0.04, 1),      # Very dark background
            'bg_secondary': (0.1, 0.1, 0.1, 1),      # Slightly lighter background
            'accent_blue': (0, 0.83, 1, 1),          # Gideon blue
            'accent_gold': (1, 0.84, 0, 1),          # Gold accent
            'text_primary': (1, 1, 1, 1),            # White text
            'text_secondary': (0.8, 0.8, 0.8, 1),    # Light gray text
            'success': (0, 1, 0.53, 1),              # Success green
            'warning': (1, 0.67, 0, 1),              # Warning orange
            'error': (1, 0.27, 0.27, 1)              # Error red
        }
    
    def add_message(self, sender: str, message: str, color_key: str = 'text_primary'):
        """Add a message to the chat display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # Create message container
        if KIVYMD_AVAILABLE:
            message_card = MDCard(
                size_hint_y=None,
                height=dp(60),
                md_bg_color=self.colors['bg_secondary'],
                padding=dp(10),
                spacing=dp(5)
            )
            
            message_label = MDLabel(
                text=f"[{timestamp}] {sender}: {message}",
                theme_text_color="Custom",
                text_color=self.colors[color_key],
                size_hint_y=None,
                height=dp(40)
            )
        else:
            message_card = BoxLayout(
                size_hint_y=None,
                height=dp(60),
                padding=dp(10),
                spacing=dp(5)
            )
            
            # Add background color
            with message_card.canvas.before:
                Color(*self.colors['bg_secondary'])
                Rectangle(pos=message_card.pos, size=message_card.size)
            
            message_label = Label(
                text=f"[{timestamp}] {sender}: {message}",
                color=self.colors[color_key],
                size_hint_y=None,
                height=dp(40),
                text_size=(None, None),
                halign='left'
            )
        
        message_card.add_widget(message_label)
        self.layout.add_widget(message_card)
        
        # Auto-scroll to bottom
        Clock.schedule_once(lambda dt: self.scroll_to(message_card), 0.1)


class GideonKivyApp(MDApp if KIVYMD_AVAILABLE else App):
    """Main Kivy application for Gideon AI Assistant"""
    
    def __init__(self, gideon_core=None, **kwargs):
        super().__init__(**kwargs)
        self.gideon_core = gideon_core
        self.logger = GideonLogger("GideonKivyApp")
        self.config_obj = Config()
        self.i18n = get_i18n()
        
        # App properties
        self.title = "Gideon AI Assistant"
        
        if KIVYMD_AVAILABLE:
            # Material Design theme
            self.theme_cls.theme_style = "Dark"
            self.theme_cls.primary_palette = "Blue"
            self.theme_cls.accent_palette = "Amber"
        
        # UI components
        self.chat_display = None
        self.text_input = None
        self.voice_button = None
        self.status_label = None
        
        # State
        self.is_voice_active = False
        
        # Setup callbacks
        if self.gideon_core:
            self.gideon_core.set_response_ready_callback(self._on_response_ready)
            self.gideon_core.set_status_change_callback(self._on_status_change)
            self.gideon_core.set_error_callback(self._on_error)
    
    def build(self):
        """Build the main UI"""
        try:
            # Set window properties
            Window.size = (1200, 800)
            Window.clearcolor = (0.04, 0.04, 0.04, 1)  # Dark background
            
            # Create main layout
            if KIVYMD_AVAILABLE:
                main_layout = MDBoxLayout(orientation='vertical', spacing=dp(10), padding=dp(10))
            else:
                main_layout = BoxLayout(orientation='vertical', spacing=dp(10), padding=dp(10))
            
            # Add background
            with main_layout.canvas.before:
                Color(0.04, 0.04, 0.04, 1)
                Rectangle(pos=main_layout.pos, size=main_layout.size)
            
            # Create header
            header = self._create_header()
            main_layout.add_widget(header)
            
            # Create chat area
            self.chat_display = ChatDisplay()
            main_layout.add_widget(self.chat_display)
            
            # Create input area
            input_area = self._create_input_area()
            main_layout.add_widget(input_area)
            
            # Create control buttons
            controls = self._create_controls()
            main_layout.add_widget(controls)
            
            # Add welcome message
            self.chat_display.add_message(
                "Gideon", 
                self.i18n.get_text("messages.hello", "Hello! I'm Gideon, your AI assistant. How can I help you today?"),
                'accent_gold'
            )
            
            return main_layout
            
        except Exception as e:
            self.logger.error(f"Error building Kivy UI: {e}")
            raise
    
    def _create_header(self):
        """Create header with title and status"""
        if KIVYMD_AVAILABLE:
            header = MDTopAppBar(
                title="🤖 GIDEON AI ASSISTANT",
                md_bg_color=(0, 0.83, 1, 1),  # Gideon blue
                size_hint_y=None,
                height=dp(60)
            )
        else:
            header = BoxLayout(
                size_hint_y=None,
                height=dp(60),
                padding=dp(10)
            )
            
            # Add background
            with header.canvas.before:
                Color(0, 0.83, 1, 1)  # Gideon blue
                Rectangle(pos=header.pos, size=header.size)
            
            title_label = Label(
                text="🤖 GIDEON AI ASSISTANT",
                font_size=dp(20),
                bold=True,
                color=(1, 1, 1, 1)
            )
            header.add_widget(title_label)
            
            self.status_label = Label(
                text="Ready",
                font_size=dp(12),
                color=(0, 1, 0.53, 1),  # Success green
                size_hint_x=None,
                width=dp(100)
            )
            header.add_widget(self.status_label)
        
        return header
    
    def _create_input_area(self):
        """Create input area with text field and voice button"""
        if KIVYMD_AVAILABLE:
            input_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(60),
                spacing=dp(10)
            )
            
            self.text_input = MDTextField(
                hint_text="Type your message or use voice input...",
                size_hint_x=0.8,
                multiline=False
            )
            
            self.voice_button = MDFloatingActionButton(
                icon="microphone",
                md_bg_color=(0, 0.83, 1, 1),  # Gideon blue
                size_hint_x=None,
                width=dp(60)
            )
            
            send_button = MDRaisedButton(
                text="Send",
                md_bg_color=(1, 0.84, 0, 1),  # Gold
                size_hint_x=None,
                width=dp(80)
            )
        else:
            input_layout = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(60),
                spacing=dp(10)
            )
            
            self.text_input = TextInput(
                hint_text="Type your message or use voice input...",
                size_hint_x=0.8,
                multiline=False,
                background_color=(0.1, 0.1, 0.1, 1),
                foreground_color=(1, 1, 1, 1)
            )
            
            self.voice_button = Button(
                text="🎤",
                size_hint_x=None,
                width=dp(60),
                background_color=(0, 0.83, 1, 1)
            )
            
            send_button = Button(
                text="Send",
                size_hint_x=None,
                width=dp(80),
                background_color=(1, 0.84, 0, 1)
            )
        
        # Bind events
        self.text_input.bind(on_text_validate=self._send_message)
        self.voice_button.bind(on_press=self._toggle_voice_input)
        send_button.bind(on_press=self._send_message)
        
        input_layout.add_widget(self.text_input)
        input_layout.add_widget(self.voice_button)
        input_layout.add_widget(send_button)
        
        return input_layout
    
    def _create_controls(self):
        """Create control buttons"""
        if KIVYMD_AVAILABLE:
            controls_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(50),
                spacing=dp(10)
            )
            
            screenshot_btn = MDRaisedButton(text="📸 Screenshot")
            clear_btn = MDRaisedButton(text="🗑️ Clear")
            models_btn = MDRaisedButton(text="🤖 Models")
            language_btn = MDRaisedButton(text="🌍 عربي/EN")
            test_btn = MDRaisedButton(text="🎤 Test Speech")
        else:
            controls_layout = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(50),
                spacing=dp(10)
            )
            
            screenshot_btn = Button(text="📸 Screenshot", background_color=(0.1, 0.1, 0.1, 1))
            clear_btn = Button(text="🗑️ Clear", background_color=(0.1, 0.1, 0.1, 1))
            models_btn = Button(text="🤖 Models", background_color=(0.1, 0.1, 0.1, 1))
            language_btn = Button(text="🌍 عربي/EN", background_color=(0.1, 0.1, 0.1, 1))
            test_btn = Button(text="🎤 Test Speech", background_color=(0.1, 0.1, 0.1, 1))
        
        # Bind events
        screenshot_btn.bind(on_press=self._take_screenshot)
        clear_btn.bind(on_press=self._clear_chat)
        models_btn.bind(on_press=self._open_model_manager)
        language_btn.bind(on_press=self._toggle_language)
        test_btn.bind(on_press=self._test_speech)
        
        controls_layout.add_widget(screenshot_btn)
        controls_layout.add_widget(clear_btn)
        controls_layout.add_widget(models_btn)
        controls_layout.add_widget(language_btn)
        controls_layout.add_widget(test_btn)
        
        return controls_layout
    
    def _send_message(self, instance=None):
        """Send text message"""
        message = self.text_input.text.strip()
        if not message:
            return
        
        # Clear input
        self.text_input.text = ""
        
        # Display user message
        self.chat_display.add_message("You", message, 'accent_blue')
        
        # Process with Gideon
        if self.gideon_core:
            self.gideon_core.process_text_input(message, self._on_text_response)
        else:
            self.chat_display.add_message("Gideon", "Core system not available", 'error')
    
    def _toggle_voice_input(self, instance):
        """Toggle voice input"""
        if not self.gideon_core:
            self.chat_display.add_message("System", "Voice input not available", 'error')
            return
        
        if self.is_voice_active:
            # Stop voice input
            self.is_voice_active = False
            self._update_voice_button()
        else:
            # Start voice input
            self.is_voice_active = True
            self._update_voice_button()
            self.gideon_core.process_voice_input(self._on_voice_response)
    
    def _update_voice_button(self):
        """Update voice button appearance"""
        if KIVYMD_AVAILABLE:
            if self.is_voice_active:
                self.voice_button.icon = "stop"
                self.voice_button.md_bg_color = (1, 0.27, 0.27, 1)  # Red
            else:
                self.voice_button.icon = "microphone"
                self.voice_button.md_bg_color = (0, 0.83, 1, 1)  # Blue
        else:
            if self.is_voice_active:
                self.voice_button.text = "🔴"
                self.voice_button.background_color = (1, 0.27, 0.27, 1)
            else:
                self.voice_button.text = "🎤"
                self.voice_button.background_color = (0, 0.83, 1, 1)
    
    def _take_screenshot(self, instance):
        """Take screenshot"""
        if self.gideon_core:
            filepath = self.gideon_core.take_screenshot()
            if filepath:
                self.chat_display.add_message("System", f"Screenshot saved: {filepath}", 'success')
            else:
                self.chat_display.add_message("System", "Failed to take screenshot", 'error')
        else:
            self.chat_display.add_message("System", "Screenshot functionality not available", 'error')
    
    def _clear_chat(self, instance):
        """Clear chat display"""
        self.chat_display.layout.clear_widgets()
        self.chat_display.add_message("System", "Chat cleared", 'text_secondary')
    
    def _open_model_manager(self, instance):
        """Open model manager"""
        self.chat_display.add_message("System", "Model manager would open here", 'warning')
    
    def _toggle_language(self, instance):
        """Toggle language"""
        current_lang = self.config_obj.get("app.language", "en")
        
        if current_lang == "en":
            self.config_obj.set("app.language", "ar")
            self.i18n.set_language("ar")
            self.chat_display.add_message("System", "تم تغيير اللغة إلى العربية 🇸🇦", 'success')
        else:
            self.config_obj.set("app.language", "en")
            self.i18n.set_language("en")
            self.chat_display.add_message("System", "Language changed to English 🇺🇸", 'success')
    
    def _test_speech(self, instance):
        """Test speech functionality"""
        self.chat_display.add_message("System", "Testing speech functionality...", 'warning')
        
        if self.gideon_core and self.gideon_core.tts_engine:
            self.gideon_core.tts_engine.speak("Hello! This is a speech test from Kivy interface.")
            self.chat_display.add_message("System", "✅ TTS test completed", 'success')
        else:
            self.chat_display.add_message("System", "❌ TTS not available", 'error')
    
    def _on_text_response(self, user_input: str, ai_response: str):
        """Handle text response from Gideon"""
        self.chat_display.add_message("Gideon", ai_response, 'accent_gold')
        
        # Speak response if TTS is enabled
        if self.gideon_core and self.config_obj.get("speech.tts_enabled", True):
            self.gideon_core.speak_text(ai_response)
    
    def _on_voice_response(self, user_input: str, ai_response: str):
        """Handle voice response from Gideon"""
        if user_input:
            self.chat_display.add_message("You (Voice)", user_input, 'accent_blue')
        
        if ai_response:
            self.chat_display.add_message("Gideon", ai_response, 'accent_gold')
            
            # Speak response
            if self.gideon_core:
                self.gideon_core.speak_text(ai_response)
        
        # Reset voice button
        self.is_voice_active = False
        self._update_voice_button()
    
    def _on_status_change(self, status: str):
        """Handle status change"""
        if hasattr(self, 'status_label') and self.status_label:
            status_text = {
                "ready": "Ready",
                "listening": "Listening...",
                "processing": "Processing...",
                "speaking": "Speaking..."
            }.get(status, status)
            
            self.status_label.text = status_text
    
    def _on_error(self, error_message: str):
        """Handle error callback"""
        self.chat_display.add_message("Error", error_message, 'error')


class GideonKivyWindow:
    """Kivy window wrapper for Gideon AI Assistant"""
    
    def __init__(self, gideon_core):
        self.gideon_core = gideon_core
        self.logger = GideonLogger("GideonKivyWindow")
        self.app = None
    
    def run(self):
        """Run the Kivy application"""
        if not KIVY_AVAILABLE:
            self.logger.error("Kivy not available")
            raise ImportError("Kivy is not installed. Install with: pip install kivy kivymd")

        try:
            self.logger.info("Starting Kivy interface...")

            # Create and run app
            self.app = GideonKivyApp(self.gideon_core)
            self.app.run()

        except Exception as e:
            self.logger.error(f"Error running Kivy interface: {e}")

            # Try fallback graphics backend
            if "GL is not available" in str(e) or "Unable to find any valuable Window provider" in str(e):
                self.logger.warning("OpenGL issues detected, trying fallback...")
                print("⚠️ OpenGL issues detected. Trying alternative graphics backend...")

                # Try different backends
                fallback_backends = ['gl', 'mock']
                for backend in fallback_backends:
                    try:
                        os.environ['KIVY_GL_BACKEND'] = backend
                        print(f"🔧 Trying graphics backend: {backend}")

                        # Restart Kivy with new backend
                        import kivy.core.window
                        kivy.core.window.Window = None

                        self.app = GideonKivyApp(self.gideon_core)
                        self.app.run()
                        return

                    except Exception as fallback_error:
                        print(f"❌ Backend {backend} failed: {fallback_error}")
                        continue

                print("❌ All graphics backends failed. Kivy interface not available.")
                print("💡 Try using the Tkinter interface instead: python main.py")

            raise


# Fallback class when Kivy is not available
class FallbackKivyWindow:
    """Fallback when Kivy is not available"""
    
    def __init__(self, gideon_core):
        self.logger = GideonLogger("FallbackKivyWindow")
        self.logger.warning("Kivy not available - using fallback")
    
    def run(self):
        """Fallback run method"""
        self.logger.error("Kivy interface not available")
        print("❌ Kivy interface not available")
        print("Install Kivy with: pip install kivy kivymd")
        raise ImportError("Kivy is not installed")


# Use fallback if Kivy is not available
if not KIVY_AVAILABLE:
    GideonKivyWindow = FallbackKivyWindow
