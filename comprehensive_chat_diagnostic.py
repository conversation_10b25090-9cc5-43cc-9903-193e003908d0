#!/usr/bin/env python3
"""
Comprehensive Chat Interface Diagnostic Test
This test will identify exactly what's wrong with the chat interface
"""

import sys
import os
import threading
import time
import tkinter as tk
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

class ChatDiagnostic:
    def __init__(self):
        self.test_results = {}
        self.interface = None
        self.gideon_core = None
        self.root = None
        
    def log_test(self, test_name, status, details=""):
        """Log test results"""
        self.test_results[test_name] = {"status": status, "details": details}
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {status}")
        if details:
            print(f"   Details: {details}")
    
    def run_comprehensive_diagnostic(self):
        """Run comprehensive chat diagnostic"""
        print("🔍 COMPREHENSIVE CHAT INTERFACE DIAGNOSTIC")
        print("=" * 60)
        print("This test will identify exactly what's wrong with the chat interface")
        print("=" * 60)
        
        try:
            # Test 1: Import modules
            self.test_module_imports()
            
            # Test 2: Initialize core systems
            self.test_core_initialization()
            
            # Test 3: Create interface
            self.test_interface_creation()
            
            # Test 4: Test widget creation and properties
            self.test_widget_properties()
            
            # Test 5: Test event bindings
            self.test_event_bindings()
            
            # Test 6: Test manual message sending
            self.test_manual_message_sending()
            
            # Test 7: Test AI response generation
            self.test_ai_response_generation()
            
            # Test 8: Test bilingual functionality
            self.test_bilingual_functionality()
            
            # Test 9: Test actual user interaction simulation
            self.test_user_interaction_simulation()
            
            # Show final results
            self.show_diagnostic_results()
            
        except Exception as e:
            print(f"❌ CRITICAL ERROR in diagnostic: {e}")
            import traceback
            traceback.print_exc()
    
    def test_module_imports(self):
        """Test 1: Module imports"""
        try:
            from src.core.gideon_core import GideonCore
            from src.ui.ultra_professional_interface import UltraProfessionalInterface
            self.log_test("Module Imports", "PASS", "All required modules imported successfully")
        except Exception as e:
            self.log_test("Module Imports", "FAIL", f"Import error: {e}")
            raise
    
    def test_core_initialization(self):
        """Test 2: Core system initialization"""
        try:
            from src.core.gideon_core import GideonCore
            print("   Initializing Gideon core...")
            self.gideon_core = GideonCore()
            self.gideon_core.initialize()
            
            # Check AI engine
            if hasattr(self.gideon_core, 'ai_engine') and self.gideon_core.ai_engine:
                ai_status = f"AI Engine available, LLM enabled: {self.gideon_core.ai_engine.llm_enabled}"
                self.log_test("Core Initialization", "PASS", ai_status)
            else:
                self.log_test("Core Initialization", "WARN", "AI engine not available")
                
        except Exception as e:
            self.log_test("Core Initialization", "FAIL", f"Core init error: {e}")
            raise
    
    def test_interface_creation(self):
        """Test 3: Interface creation"""
        try:
            from src.ui.ultra_professional_interface import UltraProfessionalInterface
            print("   Creating interface...")
            self.interface = UltraProfessionalInterface(self.gideon_core)
            self.root = self.interface.create_window()
            
            self.log_test("Interface Creation", "PASS", "Interface and window created successfully")
        except Exception as e:
            self.log_test("Interface Creation", "FAIL", f"Interface creation error: {e}")
            raise
    
    def test_widget_properties(self):
        """Test 4: Widget properties and accessibility"""
        try:
            # Test input entry widget
            if hasattr(self.interface, 'input_entry') and self.interface.input_entry:
                # Test if we can get/set text
                self.interface.input_entry.delete(0, tk.END)
                self.interface.input_entry.insert(0, "test")
                text = self.interface.input_entry.get()
                
                if text == "test":
                    self.log_test("Input Entry Widget", "PASS", "Input entry accessible and functional")
                else:
                    self.log_test("Input Entry Widget", "FAIL", f"Input entry not working properly, got: '{text}'")
            else:
                self.log_test("Input Entry Widget", "FAIL", "Input entry widget not found")
            
            # Test chat display widget
            if hasattr(self.interface, 'chat_display') and self.interface.chat_display:
                self.log_test("Chat Display Widget", "PASS", "Chat display widget exists")
            else:
                self.log_test("Chat Display Widget", "FAIL", "Chat display widget not found")
            
            # Test send button
            if hasattr(self.interface, 'send_button') and self.interface.send_button:
                self.log_test("Send Button Widget", "PASS", "Send button widget exists")
            else:
                self.log_test("Send Button Widget", "FAIL", "Send button widget not found")
                
        except Exception as e:
            self.log_test("Widget Properties", "FAIL", f"Widget test error: {e}")
    
    def test_event_bindings(self):
        """Test 5: Event bindings"""
        try:
            # Test if _send_message method exists
            if hasattr(self.interface, '_send_message'):
                self.log_test("Send Message Method", "PASS", "_send_message method exists")
            else:
                self.log_test("Send Message Method", "FAIL", "_send_message method not found")
                return
            
            # Test Enter key binding by checking if it's bound
            bindings = self.interface.input_entry.bind()
            enter_bound = '<Return>' in str(bindings) or any('<Return>' in str(b) for b in bindings)
            
            if enter_bound:
                self.log_test("Enter Key Binding", "PASS", "Enter key is bound to input entry")
            else:
                self.log_test("Enter Key Binding", "FAIL", "Enter key binding not found")
            
            # Test send button command
            if hasattr(self.interface.send_button, 'cget'):
                try:
                    command = self.interface.send_button.cget('command')
                    if command:
                        self.log_test("Send Button Command", "PASS", "Send button has command binding")
                    else:
                        self.log_test("Send Button Command", "FAIL", "Send button has no command")
                except:
                    self.log_test("Send Button Command", "WARN", "Could not check send button command")
            
        except Exception as e:
            self.log_test("Event Bindings", "FAIL", f"Event binding test error: {e}")
    
    def test_manual_message_sending(self):
        """Test 6: Manual message sending"""
        try:
            # Clear input and add test message
            self.interface.input_entry.delete(0, tk.END)
            self.interface.input_entry.insert(0, "diagnostic test message")
            
            # Manually call _send_message
            print("   Manually calling _send_message...")
            self.interface._send_message()
            
            # Check if input was cleared (indicates _send_message worked)
            remaining_text = self.interface.input_entry.get()
            if not remaining_text.strip():
                self.log_test("Manual Message Sending", "PASS", "_send_message executed and cleared input")
            else:
                self.log_test("Manual Message Sending", "FAIL", f"Input not cleared, remaining: '{remaining_text}'")
                
        except Exception as e:
            self.log_test("Manual Message Sending", "FAIL", f"Manual send error: {e}")
    
    def test_ai_response_generation(self):
        """Test 7: AI response generation"""
        try:
            if self.gideon_core and self.gideon_core.ai_engine:
                print("   Testing AI response generation...")
                test_message = "hello diagnostic test"
                
                # Set language and generate response
                self.gideon_core.ai_engine.set_response_language("en")
                response = self.gideon_core.ai_engine.generate_response(test_message)
                
                if response and response.strip():
                    self.log_test("AI Response Generation", "PASS", f"AI generated response: '{response[:50]}...'")
                else:
                    self.log_test("AI Response Generation", "FAIL", "AI generated empty response")
            else:
                self.log_test("AI Response Generation", "WARN", "AI engine not available")
                
        except Exception as e:
            self.log_test("AI Response Generation", "FAIL", f"AI response error: {e}")
    
    def test_bilingual_functionality(self):
        """Test 8: Bilingual functionality"""
        try:
            from src.utils.text_direction import text_direction_manager
            
            # Test Arabic detection
            arabic_text = "مرحبا"
            english_text = "hello"
            
            arabic_detected = text_direction_manager.is_arabic_text(arabic_text)
            english_detected = text_direction_manager.is_arabic_text(english_text)
            
            if arabic_detected and not english_detected:
                self.log_test("Language Detection", "PASS", "Arabic/English detection working")
            else:
                self.log_test("Language Detection", "FAIL", f"Detection failed: Arabic={arabic_detected}, English={english_detected}")
            
            # Test text formatting
            formatted_arabic, direction_ar = text_direction_manager.format_text_for_display(arabic_text)
            formatted_english, direction_en = text_direction_manager.format_text_for_display(english_text)
            
            if direction_ar == "rtl" and direction_en == "ltr":
                self.log_test("Text Direction", "PASS", "RTL/LTR direction detection working")
            else:
                self.log_test("Text Direction", "FAIL", f"Direction failed: Arabic={direction_ar}, English={direction_en}")
                
        except Exception as e:
            self.log_test("Bilingual Functionality", "FAIL", f"Bilingual test error: {e}")
    
    def test_user_interaction_simulation(self):
        """Test 9: User interaction simulation"""
        try:
            print("   Simulating user interaction...")
            
            # Test 1: Simulate typing and Enter key
            self.interface.input_entry.delete(0, tk.END)
            self.interface.input_entry.insert(0, "simulated user input")
            
            # Simulate Enter key event
            event = type('Event', (), {'keysym': 'Return'})()
            
            # Try to trigger the Enter key binding
            try:
                # Get the bound function for Return key
                bindings = self.interface.input_entry.bind('<Return>')
                if bindings:
                    # This should trigger _send_message
                    self.interface.input_entry.event_generate('<Return>')
                    time.sleep(0.1)  # Small delay
                    
                    # Check if input was cleared
                    remaining = self.interface.input_entry.get()
                    if not remaining.strip():
                        self.log_test("Enter Key Simulation", "PASS", "Enter key simulation worked")
                    else:
                        self.log_test("Enter Key Simulation", "FAIL", f"Enter key didn't clear input: '{remaining}'")
                else:
                    self.log_test("Enter Key Simulation", "FAIL", "No Enter key binding found")
                    
            except Exception as e:
                self.log_test("Enter Key Simulation", "FAIL", f"Enter simulation error: {e}")
            
            # Test 2: Simulate button click
            try:
                self.interface.input_entry.delete(0, tk.END)
                self.interface.input_entry.insert(0, "button click test")
                
                # Simulate button click
                self.interface.send_button.invoke()
                time.sleep(0.1)
                
                remaining = self.interface.input_entry.get()
                if not remaining.strip():
                    self.log_test("Button Click Simulation", "PASS", "Button click simulation worked")
                else:
                    self.log_test("Button Click Simulation", "FAIL", f"Button click didn't work: '{remaining}'")
                    
            except Exception as e:
                self.log_test("Button Click Simulation", "FAIL", f"Button click error: {e}")
                
        except Exception as e:
            self.log_test("User Interaction Simulation", "FAIL", f"Interaction test error: {e}")
    
    def show_diagnostic_results(self):
        """Show final diagnostic results"""
        print("\n" + "=" * 60)
        print("🔍 DIAGNOSTIC RESULTS SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for result in self.test_results.values() if result["status"] == "PASS")
        failed = sum(1 for result in self.test_results.values() if result["status"] == "FAIL")
        warned = sum(1 for result in self.test_results.values() if result["status"] == "WARN")
        
        print(f"✅ PASSED: {passed}")
        print(f"❌ FAILED: {failed}")
        print(f"⚠️ WARNINGS: {warned}")
        
        if failed > 0:
            print(f"\n❌ FAILED TESTS:")
            for test_name, result in self.test_results.items():
                if result["status"] == "FAIL":
                    print(f"   • {test_name}: {result['details']}")
        
        print(f"\n💡 RECOMMENDATIONS:")
        
        # Provide specific recommendations based on failures
        if any("Input Entry Widget" in test and result["status"] == "FAIL" for test, result in self.test_results.items()):
            print("   • Input entry widget is not working - check widget creation in interface")
        
        if any("Enter Key" in test and result["status"] == "FAIL" for test, result in self.test_results.items()):
            print("   • Enter key binding is not working - check _setup_advanced_bindings method")
        
        if any("Send Button" in test and result["status"] == "FAIL" for test, result in self.test_results.items()):
            print("   • Send button is not working - check button command binding")
        
        if any("Manual Message Sending" in test and result["status"] == "FAIL" for test, result in self.test_results.items()):
            print("   • _send_message method is not working - check method implementation")
        
        if any("AI Response" in test and result["status"] == "FAIL" for test, result in self.test_results.items()):
            print("   • AI response generation is failing - check AI engine configuration")
        
        if failed == 0:
            print("   🎉 All tests passed! The chat interface should be working.")
            print("   💡 If you still can't send messages, try:")
            print("      1. Click in the input field to focus it")
            print("      2. Type a message and press Enter")
            print("      3. Wait 10-30 seconds for AI response")
            print("      4. Check console for any error messages")
        
        print("\n🎯 To test manually:")
        print("   1. Run: python main_ultra_pro.py")
        print("   2. Type a message in the input field")
        print("   3. Press Enter or click Send")
        print("   4. Watch console for debug messages")
        
        # Keep window open for manual testing
        if self.root:
            print(f"\n🔧 MANUAL TEST WINDOW:")
            print("   The diagnostic window is now open for manual testing.")
            print("   Try typing in the input field and pressing Enter.")
            print("   Close the window when done.")
            
            # Add a test message to the interface
            if hasattr(self.interface, '_add_message'):
                self.interface._add_message("System", "🧪 Diagnostic complete! Try typing a message below.", "#00ff00")
            
            self.root.mainloop()

def main():
    """Run the comprehensive diagnostic"""
    diagnostic = ChatDiagnostic()
    diagnostic.run_comprehensive_diagnostic()

if __name__ == "__main__":
    main()
