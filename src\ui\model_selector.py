"""
Model Selector Dialog for Gideon AI Assistant
Allows users to select and configure AI models
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Dict, List, Optional, Callable
import threading

from src.utils.logger import GideonLogger
from src.utils.config import Config

try:
    import customtkinter as ctk
    CUSTOMTKINTER_AVAILABLE = True
except ImportError:
    CUSTOMTKINTER_AVAILABLE = False


class ModelSelectorDialog:
    """Dialog for selecting and configuring AI models"""
    
    def __init__(self, parent, ai_engine, on_model_changed: Callable = None):
        self.parent = parent
        self.ai_engine = ai_engine
        self.on_model_changed = on_model_changed
        self.logger = GideonLogger("ModelSelector")
        self.config = Config()
        
        # Dialog window
        self.dialog = None
        self.result = None
        
        # UI components
        self.backend_var = None
        self.model_var = None
        self.backend_combo = None
        self.model_combo = None
        self.status_label = None
        self.load_button = None
        
        # Available models
        self.available_models = {}
        
        # Colors for dark theme
        self.colors = {
            'bg_primary': '#0a0a0a',
            'bg_secondary': '#1a1a1a',
            'accent_blue': '#00d4ff',
            'accent_gold': '#ffd700',
            'text_primary': '#ffffff',
            'text_secondary': '#cccccc',
            'success': '#00ff87',
            'warning': '#ffab00',
            'error': '#ff4545'
        }
    
    def show(self):
        """Show the model selector dialog"""
        self._create_dialog()
        self._load_available_models()
        self._update_ui()
        
        # Center the dialog
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Wait for dialog to close
        self.parent.wait_window(self.dialog)
        
        return self.result
    
    def _create_dialog(self):
        """Create the dialog window"""
        if CUSTOMTKINTER_AVAILABLE:
            self.dialog = ctk.CTkToplevel(self.parent)
            self.dialog.title("🤖 AI Model Selector")
            self.dialog.geometry("600x500")
            self.dialog.configure(fg_color=self.colors['bg_primary'])
        else:
            self.dialog = tk.Toplevel(self.parent)
            self.dialog.title("🤖 AI Model Selector")
            self.dialog.geometry("600x500")
            self.dialog.configure(bg=self.colors['bg_primary'])
        
        # Create main frame
        if CUSTOMTKINTER_AVAILABLE:
            main_frame = ctk.CTkFrame(self.dialog)
            main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        else:
            main_frame = tk.Frame(self.dialog, bg=self.colors['bg_secondary'])
            main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        if CUSTOMTKINTER_AVAILABLE:
            title_label = ctk.CTkLabel(
                main_frame,
                text="🧠 Gideon's AI Brain Configuration",
                font=ctk.CTkFont(size=20, weight="bold")
            )
        else:
            title_label = tk.Label(
                main_frame,
                text="🧠 Gideon's AI Brain Configuration",
                font=("Arial", 16, "bold"),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']
            )
        title_label.pack(pady=(0, 20))
        
        # Current status
        self._create_status_section(main_frame)
        
        # Backend selection
        self._create_backend_section(main_frame)
        
        # Model selection
        self._create_model_section(main_frame)
        
        # Configuration options
        self._create_config_section(main_frame)
        
        # Buttons
        self._create_buttons(main_frame)
    
    def _create_status_section(self, parent):
        """Create status section"""
        if CUSTOMTKINTER_AVAILABLE:
            status_frame = ctk.CTkFrame(parent)
            status_frame.pack(fill="x", pady=(0, 20))
            
            status_title = ctk.CTkLabel(status_frame, text="Current Status", font=ctk.CTkFont(weight="bold"))
            status_title.pack(pady=(10, 5))
            
            self.status_label = ctk.CTkLabel(status_frame, text="Loading...")
            self.status_label.pack(pady=(0, 10))
        else:
            status_frame = tk.LabelFrame(parent, text="Current Status", bg=self.colors['bg_secondary'], fg=self.colors['text_primary'])
            status_frame.pack(fill="x", pady=(0, 20))
            
            self.status_label = tk.Label(status_frame, text="Loading...", bg=self.colors['bg_secondary'], fg=self.colors['text_secondary'])
            self.status_label.pack(pady=10)
    
    def _create_backend_section(self, parent):
        """Create backend selection section"""
        if CUSTOMTKINTER_AVAILABLE:
            backend_frame = ctk.CTkFrame(parent)
            backend_frame.pack(fill="x", pady=(0, 10))
            
            backend_label = ctk.CTkLabel(backend_frame, text="AI Backend:", font=ctk.CTkFont(weight="bold"))
            backend_label.pack(anchor="w", padx=10, pady=(10, 5))
            
            self.backend_var = tk.StringVar()
            self.backend_combo = ctk.CTkComboBox(
                backend_frame,
                variable=self.backend_var,
                command=self._on_backend_changed
            )
            self.backend_combo.pack(fill="x", padx=10, pady=(0, 10))
        else:
            backend_frame = tk.LabelFrame(parent, text="AI Backend", bg=self.colors['bg_secondary'], fg=self.colors['text_primary'])
            backend_frame.pack(fill="x", pady=(0, 10))
            
            self.backend_var = tk.StringVar()
            self.backend_combo = ttk.Combobox(backend_frame, textvariable=self.backend_var, state="readonly")
            self.backend_combo.pack(fill="x", padx=10, pady=10)
            self.backend_combo.bind("<<ComboboxSelected>>", self._on_backend_changed)
    
    def _create_model_section(self, parent):
        """Create model selection section"""
        if CUSTOMTKINTER_AVAILABLE:
            model_frame = ctk.CTkFrame(parent)
            model_frame.pack(fill="x", pady=(0, 10))
            
            model_label = ctk.CTkLabel(model_frame, text="AI Model:", font=ctk.CTkFont(weight="bold"))
            model_label.pack(anchor="w", padx=10, pady=(10, 5))
            
            self.model_var = tk.StringVar()
            self.model_combo = ctk.CTkComboBox(
                model_frame,
                variable=self.model_var
            )
            self.model_combo.pack(fill="x", padx=10, pady=(0, 10))
        else:
            model_frame = tk.LabelFrame(parent, text="AI Model", bg=self.colors['bg_secondary'], fg=self.colors['text_primary'])
            model_frame.pack(fill="x", pady=(0, 10))
            
            self.model_var = tk.StringVar()
            self.model_combo = ttk.Combobox(model_frame, textvariable=self.model_var, state="readonly")
            self.model_combo.pack(fill="x", padx=10, pady=10)
    
    def _create_config_section(self, parent):
        """Create configuration section"""
        if CUSTOMTKINTER_AVAILABLE:
            config_frame = ctk.CTkFrame(parent)
            config_frame.pack(fill="x", pady=(0, 20))
            
            config_label = ctk.CTkLabel(config_frame, text="Configuration:", font=ctk.CTkFont(weight="bold"))
            config_label.pack(anchor="w", padx=10, pady=(10, 5))
            
            # Always use LLM checkbox
            self.always_llm_var = tk.BooleanVar(value=self.config.get("ai.always_use_llm", False))
            always_llm_check = ctk.CTkCheckBox(
                config_frame,
                text="Always use AI model (instead of rule-based responses)",
                variable=self.always_llm_var
            )
            always_llm_check.pack(anchor="w", padx=10, pady=5)
            
            # Fallback to rules checkbox
            self.fallback_var = tk.BooleanVar(value=self.config.get("ai.fallback_to_rules", True))
            fallback_check = ctk.CTkCheckBox(
                config_frame,
                text="Fallback to rule-based responses if AI fails",
                variable=self.fallback_var
            )
            fallback_check.pack(anchor="w", padx=10, pady=(0, 10))
        else:
            config_frame = tk.LabelFrame(parent, text="Configuration", bg=self.colors['bg_secondary'], fg=self.colors['text_primary'])
            config_frame.pack(fill="x", pady=(0, 20))
            
            self.always_llm_var = tk.BooleanVar(value=self.config.get("ai.always_use_llm", False))
            always_llm_check = tk.Checkbutton(
                config_frame,
                text="Always use AI model",
                variable=self.always_llm_var,
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']
            )
            always_llm_check.pack(anchor="w", padx=10, pady=5)
            
            self.fallback_var = tk.BooleanVar(value=self.config.get("ai.fallback_to_rules", True))
            fallback_check = tk.Checkbutton(
                config_frame,
                text="Fallback to rule-based responses",
                variable=self.fallback_var,
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']
            )
            fallback_check.pack(anchor="w", padx=10, pady=5)
    
    def _create_buttons(self, parent):
        """Create dialog buttons"""
        if CUSTOMTKINTER_AVAILABLE:
            button_frame = ctk.CTkFrame(parent)
            button_frame.pack(fill="x", pady=(0, 10))
            
            self.load_button = ctk.CTkButton(
                button_frame,
                text="🔄 Load Model",
                command=self._load_model,
                fg_color=self.colors['accent_blue']
            )
            self.load_button.pack(side="left", padx=(10, 5), pady=10)
            
            browse_button = ctk.CTkButton(
                button_frame,
                text="📁 Browse for Model",
                command=self._browse_model,
                fg_color=self.colors['accent_gold']
            )
            browse_button.pack(side="left", padx=5, pady=10)
            
            close_button = ctk.CTkButton(
                button_frame,
                text="✅ Apply & Close",
                command=self._apply_and_close
            )
            close_button.pack(side="right", padx=(5, 10), pady=10)
        else:
            button_frame = tk.Frame(parent, bg=self.colors['bg_secondary'])
            button_frame.pack(fill="x", pady=(0, 10))
            
            self.load_button = tk.Button(
                button_frame,
                text="🔄 Load Model",
                command=self._load_model,
                bg=self.colors['accent_blue'],
                fg="white"
            )
            self.load_button.pack(side="left", padx=(10, 5), pady=10)
            
            browse_button = tk.Button(
                button_frame,
                text="📁 Browse",
                command=self._browse_model,
                bg=self.colors['accent_gold'],
                fg="black"
            )
            browse_button.pack(side="left", padx=5, pady=10)
            
            close_button = tk.Button(
                button_frame,
                text="✅ Apply & Close",
                command=self._apply_and_close,
                bg=self.colors['success'],
                fg="black"
            )
            close_button.pack(side="right", padx=(5, 10), pady=10)
    
    def _load_available_models(self):
        """Load available models in background"""
        def load_models():
            try:
                self.available_models = self.ai_engine.get_available_models()
                self.dialog.after(0, self._update_model_lists)
            except Exception as e:
                self.logger.error(f"Error loading models: {e}")
                self.dialog.after(0, lambda: self._update_status(f"Error loading models: {e}", "error"))
        
        threading.Thread(target=load_models, daemon=True).start()
    
    def _update_model_lists(self):
        """Update model lists in UI"""
        # Update backend combo
        backends = list(self.available_models.keys())
        if backends:
            if CUSTOMTKINTER_AVAILABLE:
                self.backend_combo.configure(values=backends)
            else:
                self.backend_combo['values'] = backends
            
            # Set current backend
            current_backend = self.config.get("ai.preferred_backend", "")
            if current_backend in backends:
                self.backend_var.set(current_backend)
            elif backends:
                self.backend_var.set(backends[0])
            
            self._on_backend_changed()
        else:
            self._update_status("No AI backends available", "warning")
    
    def _on_backend_changed(self, event=None):
        """Handle backend selection change"""
        backend = self.backend_var.get()
        if backend in self.available_models:
            models = self.available_models[backend]
            
            if CUSTOMTKINTER_AVAILABLE:
                self.model_combo.configure(values=models)
            else:
                self.model_combo['values'] = models
            
            # Set current model
            current_model = self.config.get("ai.preferred_model", "")
            if current_model in models:
                self.model_var.set(current_model)
            elif models:
                self.model_var.set(models[0])
    
    def _load_model(self):
        """Load the selected model"""
        backend = self.backend_var.get()
        model = self.model_var.get()
        
        if not backend or not model:
            messagebox.showwarning("Selection Required", "Please select both backend and model")
            return
        
        self._update_status("Loading model...", "info")
        self.load_button.configure(state="disabled")
        
        def load_model():
            try:
                success = self.ai_engine.load_model(backend, model)
                self.dialog.after(0, lambda: self._on_model_loaded(success, backend, model))
            except Exception as e:
                self.dialog.after(0, lambda: self._on_model_load_error(str(e)))
        
        threading.Thread(target=load_model, daemon=True).start()
    
    def _on_model_loaded(self, success: bool, backend: str, model: str):
        """Handle model load result"""
        self.load_button.configure(state="normal")
        
        if success:
            self._update_status(f"✅ Model loaded: {model} via {backend}", "success")
            if self.on_model_changed:
                self.on_model_changed(backend, model)
        else:
            self._update_status(f"❌ Failed to load model: {model}", "error")
    
    def _on_model_load_error(self, error: str):
        """Handle model load error"""
        self.load_button.configure(state="normal")
        self._update_status(f"❌ Error: {error}", "error")
    
    def _browse_model(self):
        """Browse for model file"""
        filetypes = [
            ("GGUF Models", "*.gguf"),
            ("All Model Files", "*.gguf *.bin *.safetensors *.pt *.pth"),
            ("All Files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="Select AI Model File",
            filetypes=filetypes
        )
        
        if filename:
            # Set to llamacpp backend and use the file path
            self.backend_var.set("llamacpp")
            self.model_var.set(filename)
            self._update_status(f"Selected model file: {filename}", "info")
    
    def _apply_and_close(self):
        """Apply settings and close dialog"""
        # Save configuration
        self.config.set("ai.always_use_llm", self.always_llm_var.get())
        self.config.set("ai.fallback_to_rules", self.fallback_var.get())
        
        # Update AI engine settings
        if hasattr(self.ai_engine, 'fallback_to_rules'):
            self.ai_engine.fallback_to_rules = self.fallback_var.get()
        
        self.result = {
            'backend': self.backend_var.get(),
            'model': self.model_var.get(),
            'always_llm': self.always_llm_var.get(),
            'fallback_to_rules': self.fallback_var.get()
        }
        
        self.dialog.destroy()
    
    def _update_status(self, message: str, status_type: str = "info"):
        """Update status message"""
        colors = {
            'info': self.colors['text_secondary'],
            'success': self.colors['success'],
            'warning': self.colors['warning'],
            'error': self.colors['error']
        }
        
        if CUSTOMTKINTER_AVAILABLE:
            self.status_label.configure(text=message, text_color=colors.get(status_type, colors['info']))
        else:
            self.status_label.configure(text=message, fg=colors.get(status_type, colors['info']))
    
    def _update_ui(self):
        """Update UI with current state"""
        if self.ai_engine.llm_enabled and self.ai_engine.active_backend:
            backend_name = self.ai_engine.active_backend.name
            self._update_status(f"✅ Active: {backend_name}", "success")
        else:
            self._update_status("Using rule-based responses only", "warning")
